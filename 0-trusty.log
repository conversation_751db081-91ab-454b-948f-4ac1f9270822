c0  args->params[0] is 0x20. ###
[    1.886938] c0 enter vboot verify
[    1.886940] c0 SECUREBOOTDataVal is not NULL.
[    1.886942] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.886950] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    1.886953] c0 from uboot smc...addr:0x99800000 len:0x600000
[    1.887059] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.887063] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    1.887066] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    1.887070] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    1.887073] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6c12000
[    1.887076] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6c13000
[    1.887079] c0 img_name:l_agdsp, g_avbUserData.img_name:l_agdsp.
[    1.887081] c0 avb check image name:l_agdsp.
[    1.887084] c0 ### enter avb_slot_verify. ###
[    1.887091] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.887096] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe6c0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.887108] c0 [kbc] save vbmeta image for cp verify.
[    1.887119] c0 enter implement read_rollback_index().
[    1.887120] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.887123] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.887126] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.887132] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.887135] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887139] c0 Skip verify chain_partition[boot] while load verify l_agdsp partition... 
[    1.887142] c0 n = 0
[    1.887144] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887147] c0 Skip verify chain_partition[dtbo] while load verify l_agdsp partition... 
[    1.887149] c0 n = 1
[    1.887151] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887154] c0 Skip verify chain_partition[init_boot] while load verify l_agdsp partition... 
[    1.887156] c0 n = 2
[    1.887157] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887160] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_agdsp partition... 
[    1.887163] c0 n = 3
[    1.887164] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887167] c0 Skip verify chain_partition[vbmeta_product] while load verify l_agdsp partition... 
[    1.887170] c0 n = 4
[    1.887171] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887174] c0 Skip verify chain_partition[vbmeta_system] while load verify l_agdsp partition... 
[    1.887177] c0 n = 5
[    1.887178] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887181] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_agdsp partition... 
[    1.887183] c0 n = 6
[    1.887185] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887188] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_agdsp partition... 
[    1.887190] c0 n = 7
[    1.887192] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887194] c0 Skip verify chain_partition[vendor_boot] while load verify l_agdsp partition... 
[    1.887197] c0 n = 8
[    1.887198] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887201] c0 Skip verify chain_partition[l_modem] while load verify l_agdsp partition... 
[    1.887204] c0 n = 9
[    1.887205] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887208] c0 Skip verify chain_partition[l_ldsp] while load verify l_agdsp partition... 
[    1.887210] c0 n = 10
[    1.887212] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887215] c0 Skip verify chain_partition[l_gdsp] while load verify l_agdsp partition... 
[    1.887217] c0 n = 11
[    1.887219] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887222] c0 Skip verify chain_partition[pm_sys] while load verify l_agdsp partition... 
[    1.887224] c0 n = 12
[    1.887226] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.887229] c0 chain partition founded: n = 13
[    1.887232] c0 read partition l_agdsp, offset: 0x5fffc0, bytes: 0x40.
[    1.887235] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x5fffc0 num_bytes:0x40
[    1.887241] c0 Loading vbmeta struct in footer from partition 'read partition l_agdsp, offset: 0x500000, bytes: 0x840.
[    1.887244] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x500000 num_bytes:0x840
[    1.889902] c0 partition: l_agdsp vbmeta_verify_ret is :0. 
[    1.889911] c0 enter implement read_rollback_index().
[    1.889913] c0 read_is_device_unlocked() rollback_index_slot = 15 
[    1.889917] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.889920] c0 Info: g_sprd_vboot_version.img_ver[15]= 0
[    1.889923] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    1.889925] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    1.889933] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    1.889935] c0 check dat cp
[    1.916759] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    1.916766] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    1.916770] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.916773] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.916776] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.916779] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.916782] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.916786] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.916790] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    1.916794] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    1.916806] c0 read_is_device_unlocked() ret = 0 
[    1.917249] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    1.917252] c0 partition: vbmeta  guid_buf_size = 37 
[    1.917255] c0 guid: 1.0 
[    1.917262] c0 in avb_slot_verify, l:1626. 
[    1.917265] c0 in avb_slot_verify, l:1640. 
[    1.917269] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    1.917272] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    1.917274] c0 slot_data[0] is 0xe04d8458.
[    1.917276] c0 slot_data[1] is 0x0.
[    1.917279] c0 vboot_verify_ret is :0
[    1.917284] c0 enter copy just debug... 344.
[    1.917287] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20416 androidboot.vbmeta.digest=654190a6059be94ac2255a0f22d6a52b2200b92aca64cd2696d3
[    1.923937] c0 have got sip smc all from uboot###
[    1.923941] c0  args->params[0] is 0x20. ###
[    1.923945] c0 enter vboot verify
[    1.923947] c0 SECUREBOOTDataVal is not NULL.
[    1.923949] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.923965] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    1.923969] c0 from uboot smc...addr:0x99800000 len:0x800000
[    1.924108] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.924113] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    1.924117] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    1.924121] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    1.924124] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6e12000
[    1.924127] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6e13000
[    1.924131] c0 img_name:dtbo, g_avbUserData.img_name:dtbo.
[    1.924134] c0 avb check image name:dtbo.
[    1.924136] c0 ### enter avb_slot_verify. ###
[    1.924145] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.924150] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe6e0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.924162] c0 [kbc] save vbmeta image for cp verify.
[    1.924173] c0 enter implement read_rollback_index().
[    1.924174] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.924177] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.924182] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.924188] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.924192] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.924197] c0 Skip verify chain_partition[boot] while load verify dtbo partition... 
[    1.924200] c0 n = 0
[    1.924202] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.924205] c0 chain partition founded: n = 1
[    1.924208] c0 read partition dtbo, offset: 0x7fffc0, bytes: 0x40.
[    1.924211] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x7fffc0 num_bytes:0x40
[    1.924217] c0 Loading vbmeta struct in footer from partition 'read partition dtbo, offset: 0x8e000, bytes: 0x840.
[    1.924220] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x8e000 num_bytes:0x840
[    1.926858] c0 partition: dtbo vbmeta_verify_ret is :0. 
[    1.926866] c0 enter implement read_rollback_index().
[    1.926868] c0 read_is_device_unlocked() rollback_index_slot = 6 
[    1.926870] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.926873] c0 Info: g_sprd_vboot_version.img_ver[6]= 0
[    1.926876] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    1.926878] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    1.926884] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    1.926886] c0 check dat cp
[    1.929923] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    1.929927] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    1.929932] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929936] c0 Skip verify chain_partition[init_boot] while load verify dtbo partition... 
[    1.929939] c0 n = 2
[    1.929940] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929943] c0 Skip verify chain_partition[vbmeta_odm] while load verify dtbo partition... 
[    1.929946] c0 n = 3
[    1.929947] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929950] c0 Skip verify chain_partition[vbmeta_product] while load verify dtbo partition... 
[    1.929953] c0 n = 4
[    1.929954] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929957] c0 Skip verify chain_partition[vbmeta_system] while load verify dtbo partition... 
[    1.929959] c0 n = 5
[    1.929961] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929964] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify dtbo partition... 
[    1.929966] c0 n = 6
[    1.929968] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929974] c0 Skip verify chain_partition[vbmeta_vendor] while load verify dtbo partition... 
[    1.929976] c0 n = 7
[    1.929978] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929984] c0 Skip verify chain_partition[vendor_boot] while load verify dtbo partition... 
[    1.929986] c0 n = 8
[    1.929988] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.929991] c0 Skip verify chain_partition[l_modem] while load verify dtbo partition... 
[    1.929993] c0 n = 9
[    1.929995] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.930000] c0 Skip verify chain_partition[l_ldsp] while load verify dtbo partition... 
[    1.930003] c0 n = 10
[    1.930005] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.930009] c0 Skip verify chain_partition[l_gdsp] while load verify dtbo partition... 
[    1.930011] c0 n = 11
[    1.930013] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.930016] c0 Skip verify chain_partition[pm_sys] while load verify dtbo partition... 
[    1.930018] c0 n = 12
[    1.930020] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.930026] c0 Skip verify chain_partition[l_agdsp] while load verify dtbo partition... 
[    1.930029] c0 n = 13
[    1.930030] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.930033] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.930036] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.930038] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.930041] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.930043] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.930048] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    1.930051] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    1.930061] c0 read_is_device_unlocked() ret = 0 
[    1.930498] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    1.930500] c0 partition: vbmeta  guid_buf_size = 37 
[    1.930503] c0 guid: 1.0 
[    1.930509] c0 in avb_slot_verify, l:1626. 
[    1.930512] c0 in avb_slot_verify, l:1640. 
[    1.930516] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    1.930518] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    1.930520] c0 slot_data[0] is 0xe04d8458.
[    1.930522] c0 slot_data[1] is 0x0.
[    1.930525] c0 vboot_verify_ret is :0
[    1.930529] c0 enter copy just debug... 344.
[    1.930532] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20416 androidboot.vbmeta.digest=acb66e64291df9905d472e68f190496ed443f6eb3e4452447d21
[    1.977290] c0 have got sip smc all from uboot###
[    1.977293] c0  args->params[0] is 0x20. ###
[    1.977296] c0 enter vboot verify
[    1.977298] c0 SECUREBOOTDataVal is not NULL.
[    1.977299] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.977307] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    1.977310] c0 from uboot smc...addr:0x99800000 len:0x800000
[    1.977445] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.977449] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    1.977452] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    1.977455] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    1.977461] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6e12000
[    1.977463] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6e13000
[    1.977467] c0 img_name:init_boot, g_avbUserData.img_name:init_boot.
[    1.977469] c0 avb check image name:init_boot.
[    1.977472] c0 ### enter avb_slot_verify. ###
[    1.977479] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.977484] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe6e0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.977495] c0 [kbc] save vbmeta image for cp verify.
[    1.977503] c0 enter implement read_rollback_index().
[    1.977504] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.977507] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.977510] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.977515] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.977519] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.977522] c0 Skip verify chain_partition[boot] while load verify init_boot partition... 
[    1.977524] c0 n = 0
[    1.977526] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.977529] c0 Skip verify chain_partition[dtbo] while load verify init_boot partition... 
[    1.977531] c0 n = 1
[    1.977533] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.977536] c0 chain partition founded: n = 2
[    1.977538] c0 read partition init_boot, offset: 0x7fffc0, bytes: 0x40.
[    1.977541] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x7fffc0 num_bytes:0x40
[    1.977547] c0 Loading vbmeta struct in footer from partition 'read partition init_boot, offset: 0x2c3000, bytes: 0x900.
[    1.977550] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x2c3000 num_bytes:0x900
[    1.980182] c0 partition: init_boot vbmeta_verify_ret is :0. 
[    1.980190] c0 enter implement read_rollback_index().
[    1.980191] c0 read_is_device_unlocked() rollback_index_slot = 8 
[    1.980196] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.980199] c0 Info: g_sprd_vboot_version.img_ver[8]= 0
[    1.980202] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:3.
[    1.980204] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    1.980211] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    1.980212] c0 check dat cp
[    1.995079] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    1.995086] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995089] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995093] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    1.995096] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995101] c0 Skip verify chain_partition[vbmeta_odm] while load verify init_boot partition... 
[    1.995104] c0 n = 3
[    1.995106] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995109] c0 Skip verify chain_partition[vbmeta_product] while load verify init_boot partition... 
[    1.995111] c0 n = 4
[    1.995113] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995117] c0 Skip verify chain_partition[vbmeta_system] while load verify init_boot partition... 
[    1.995119] c0 n = 5
[    1.995121] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995125] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify init_boot partition... 
[    1.995127] c0 n = 6
[    1.995129] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995132] c0 Skip verify chain_partition[vbmeta_vendor] while load verify init_boot partition... 
[    1.995135] c0 n = 7
[    1.995137] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995141] c0 Skip verify chain_partition[vendor_boot] while load verify init_boot partition... 
[    1.995143] c0 n = 8
[    1.995145] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995149] c0 Skip verify chain_partition[l_modem] while load verify init_boot partition... 
[    1.995151] c0 n = 9
[    1.995153] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995156] c0 Skip verify chain_partition[l_ldsp] while load verify init_boot partition... 
[    1.995159] c0 n = 10
[    1.995161] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995164] c0 Skip verify chain_partition[l_gdsp] while load verify init_boot partition... 
[    1.995166] c0 n = 11
[    1.995169] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995172] c0 Skip verify chain_partition[pm_sys] while load verify init_boot partition... 
[    1.995174] c0 n = 12
[    1.995176] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.995179] c0 Skip verify chain_partition[l_agdsp] while load verify init_boot partition... 
[    1.995182] c0 n = 13
[    1.995183] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995186] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995189] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995193] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995195] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995198] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.995202] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    1.995206] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    1.995217] c0 read_is_device_unlocked() ret = 0 
[    1.995658] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    1.995661] c0 partition: vbmeta  guid_buf_size = 37 
[    1.995664] c0 guid: 1.0 
[    1.995671] c0 in avb_slot_verify, l:1626. 
[    1.995674] c0 in avb_slot_verify, l:1640. 
[    1.995678] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    1.995680] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    1.995683] c0 slot_data[0] is 0xe04d8458.
[    1.995684] c0 slot_data[1] is 0x0.
[    1.995687] c0 vboot_verify_ret is :0
[    1.995691] c0 enter copy just debug... 344.
[    1.995695] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20608 androidboot.vbmeta.digest=dd6cb3fd011a057c181a2082fc8e85c016f934b1110c30557b34
[    2.001858] c0 have got sip smc all from uboot###
[    2.001860] c0  args->params[0] is 0x20. ###
[    2.001863] c0 enter vboot verify
[    2.001866] c0 SECUREBOOTDataVal is not NULL.
[    2.001868] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.001876] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    2.001879] c0 from uboot smc...addr:0xc6400000 len:0x6400000
[    2.003526] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.003532] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    2.003536] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    2.003539] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    2.003542] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xeca12000
[    2.003547] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xeca13000
[    2.003551] c0 img_name:vendor_boot, g_avbUserData.img_name:vendor_boot.
[    2.003554] c0 avb check image name:vendor_boot.
[    2.003556] c0 ### enter avb_slot_verify. ###
[    2.003563] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.003569] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xeca0b000, offset_from_partition:0x0 num_bytes:0x5000
[    2.003581] c0 [kbc] save vbmeta image for cp verify.
[    2.003590] c0 enter implement read_rollback_index().
[    2.003591] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.003594] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.003598] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.003604] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.003607] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003611] c0 Skip verify chain_partition[boot] while load verify vendor_boot partition... 
[    2.003614] c0 n = 0
[    2.003615] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003618] c0 Skip verify chain_partition[dtbo] while load verify vendor_boot partition... 
[    2.003621] c0 n = 1
[    2.003622] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003625] c0 Skip verify chain_partition[init_boot] while load verify vendor_boot partition... 
[    2.003628] c0 n = 2
[    2.003629] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003632] c0 Skip verify chain_partition[vbmeta_odm] while load verify vendor_boot partition... 
[    2.003635] c0 n = 3
[    2.003636] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003639] c0 Skip verify chain_partition[vbmeta_product] while load verify vendor_boot partition... 
[    2.003641] c0 n = 4
[    2.003643] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003646] c0 Skip verify chain_partition[vbmeta_system] while load verify vendor_boot partition... 
[    2.003648] c0 n = 5
[    2.003650] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003653] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vendor_boot partition... 
[    2.003655] c0 n = 6
[    2.003657] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003660] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vendor_boot partition... 
[    2.003662] c0 n = 7
[    2.003663] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.003667] c0 chain partition founded: n = 8
[    2.003670] c0 read partition vendor_boot, offset: 0x63fffc0, bytes: 0x40.
[    2.003673] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x63fffc0 num_bytes:0x40
[    2.003679] c0 Loading vbmeta struct in footer from partition 'read partition vendor_boot, offset: 0x2006000, bytes: 0x8c0.
[    2.003682] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x2006000 num_bytes:0x8c0
[    2.006213] c0 partition: vendor_boot vbmeta_verify_ret is :0. 
[    2.006222] c0 enter implement read_rollback_index().
[    2.006224] c0 read_is_device_unlocked() rollback_index_slot = 18 
[    2.006229] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.006231] c0 Info: g_sprd_vboot_version.img_ver[18]= 0
[    2.006234] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:2.
[    2.006237] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    2.006242] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    2.006244] c0 check dat cp
[    2.177399] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    2.177405] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177410] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.177414] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.177419] c0 Skip verify chain_partition[l_modem] while load verify vendor_boot partition... 
[    2.177422] c0 n = 9
[    2.177424] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.177428] c0 Skip verify chain_partition[l_ldsp] while load verify vendor_boot partition... 
[    2.177430] c0 n = 10
[    2.177432] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.177436] c0 Skip verify chain_partition[l_gdsp] while load verify vendor_boot partition... 
[    2.177438] c0 n = 11
[    2.177439] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.177443] c0 Skip verify chain_partition[pm_sys] while load verify vendor_boot partition... 
[    2.177445] c0 n = 12
[    2.177448] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.177451] c0 Skip verify chain_partition[l_agdsp] while load verify vendor_boot partition... 
[    2.177453] c0 n = 13
[    2.177455] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177458] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177461] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177465] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177467] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177470] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.177475] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.177479] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    2.177490] c0 read_is_device_unlocked() ret = 0 
[    2.177934] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.177937] c0 partition: vbmeta  guid_buf_size = 37 
[    2.177940] c0 guid: 1.0 
[    2.177947] c0 in avb_slot_verify, l:1626. 
[    2.177950] c0 in avb_slot_verify, l:1640. 
[    2.177954] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.177957] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.177959] c0 slot_data[0] is 0xe04d8458.
[    2.177962] c0 slot_data[1] is 0x0.
[    2.177964] c0 vboot_verify_ret is :0
[    2.177968] c0 enter copy just debug... 344.
[    2.177971] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20544 androidboot.vbmeta.digest=53497f4eecacb28a8f9fa1fcf6b52b63e97b500445dbbb92d119
[    2.183608] c0 have got sip smc all from uboot###
[    2.183611] c0  args->params[0] is 0x20. ###
[    2.183614] c0 enter vboot verify
[    2.183616] c0 SECUREBOOTDataVal is not NULL.
[    2.183618] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.183624] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    2.183627] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.183631] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.183635] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    2.183638] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    2.183641] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    2.183645] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.183648] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.183651] c0 img_name:vbmeta_system, g_avbUserData.img_name:vbmeta_system.
[    2.183654] c0 avb check image name:vbmeta_system.
[    2.183657] c0 ### enter avb_slot_verify. ###
[    2.183663] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.183668] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.183681] c0 [kbc] save vbmeta image for cp verify.
[    2.183690] c0 enter implement read_rollback_index().
[    2.183691] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.183694] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.183697] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.183703] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.183707] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183711] c0 Skip verify chain_partition[boot] while load verify vbmeta_system partition... 
[    2.183713] c0 n = 0
[    2.183715] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183718] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_system partition... 
[    2.183720] c0 n = 1
[    2.183721] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183724] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_system partition... 
[    2.183727] c0 n = 2
[    2.183728] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183731] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_system partition... 
[    2.183734] c0 n = 3
[    2.183735] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183738] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_system partition... 
[    2.183741] c0 n = 4
[    2.183742] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183748] c0 chain partition founded: n = 5
[    2.183751] c0 Loading vbmeta struct from partition 'read partition vbmeta_system, offset: 0x0, bytes: 0x1000.
[    2.183754] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.186418] c0 partition: vbmeta_system vbmeta_verify_ret is :0. 
[    2.186426] c0 enter implement read_rollback_index().
[    2.186427] c0 read_is_device_unlocked() rollback_index_slot = 2 
[    2.186432] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.186435] c0 Info: g_sprd_vboot_version.img_ver[2]= 0
[    2.186439] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:8.
[    2.186441] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186444] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186447] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186450] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186452] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186455] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186457] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.186461] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.186464] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.186467] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186470] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_system partition... 
[    2.186472] c0 n = 6
[    2.186474] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186477] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_system partition... 
[    2.186479] c0 n = 7
[    2.186481] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186484] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_system partition... 
[    2.186487] c0 n = 8
[    2.186488] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186491] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_system partition... 
[    2.186494] c0 n = 9
[    2.186495] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186498] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_system partition... 
[    2.186501] c0 n = 10
[    2.186502] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186505] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_system partition... 
[    2.186507] c0 n = 11
[    2.186509] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186512] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_system partition... 
[    2.186514] c0 n = 12
[    2.186516] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186519] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_system partition... 
[    2.186521] c0 n = 13
[    2.186523] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186525] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186528] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186530] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186533] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186536] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186538] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.186542] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.186548] c0 read_is_device_unlocked() ret = 0 
[    2.186995] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.186998] c0 partition: vbmeta  guid_buf_size = 37 
[    2.187001] c0 guid: 1.0 
[    2.187007] c0 in avb_slot_verify, l:1626. 
[    2.187009] c0 in avb_slot_verify, l:1640. 
[    2.187011] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.187015] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.187017] c0 slot_data[0] is 0xe04d8458.
[    2.187019] c0 slot_data[1] is 0x0.
[    2.187021] c0 vboot_verify_ret is :0
[    2.187024] c0 enter copy just debug... 344.
[    2.187027] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=21312 androidboot.vbmeta.digest=3cd4eb1eb7b5637ef418e82cc719ad88a3619576781a878e9a8c
[    2.187374] c0 have got sip smc all from uboot###
[    2.187376] c0  args->params[0] is 0x20. ###
[    2.187378] c0 enter vboot verify
[    2.187380] c0 SECUREBOOTDataVal is not NULL.
[    2.187381] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.187386] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    2.187390] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.187393] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.187398] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    2.187401] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    2.187405] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    2.187408] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.187411] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.187413] c0 img_name:vbmeta_vendor, g_avbUserData.img_name:vbmeta_vendor.
[    2.187416] c0 avb check image name:vbmeta_vendor.
[    2.187417] c0 ### enter avb_slot_verify. ###
[    2.187422] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.187429] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.187439] c0 [kbc] save vbmeta image for cp verify.
[    2.187447] c0 enter implement read_rollback_index().
[    2.187448] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.187450] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.187453] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.187457] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.187461] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187464] c0 Skip verify chain_partition[boot] while load verify vbmeta_vendor partition... 
[    2.187466] c0 n = 0
[    2.187468] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187471] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_vendor partition... 
[    2.187473] c0 n = 1
[    2.187474] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187477] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_vendor partition... 
[    2.187480] c0 n = 2
[    2.187481] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187484] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_vendor partition... 
[    2.187487] c0 n = 3
[    2.187488] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187491] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_vendor partition... 
[    2.187494] c0 n = 4
[    2.187495] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187498] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_vendor partition... 
[    2.187501] c0 n = 5
[    2.187502] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187505] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_vendor partition... 
[    2.187508] c0 n = 6
[    2.187509] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187514] c0 chain partition founded: n = 7
[    2.187517] c0 Loading vbmeta struct from partition 'read partition vbmeta_vendor, offset: 0x0, bytes: 0x1000.
[    2.187521] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.190193] c0 partition: vbmeta_vendor vbmeta_verify_ret is :0. 
[    2.190201] c0 enter implement read_rollback_index().
[    2.190202] c0 read_is_device_unlocked() rollback_index_slot = 4 
[    2.190206] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.190209] c0 Info: g_sprd_vboot_version.img_ver[4]= 0
[    2.190212] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:4.
[    2.190215] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190217] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190220] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190223] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.190226] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.190228] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190231] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_vendor partition... 
[    2.190234] c0 n = 8
[    2.190235] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190238] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_vendor partition... 
[    2.190241] c0 n = 9
[    2.190242] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190245] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_vendor partition... 
[    2.190249] c0 n = 10
[    2.190250] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190253] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_vendor partition... 
[    2.190255] c0 n = 11
[    2.190257] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190260] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_vendor partition... 
[    2.190262] c0 n = 12
[    2.190264] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190267] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_vendor partition... 
[    2.190269] c0 n = 13
[    2.190270] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190273] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190276] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190278] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190281] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190283] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190286] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.190290] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.190296] c0 read_is_device_unlocked() ret = 0 
[    2.190727] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.190730] c0 partition: vbmeta  guid_buf_size = 37 
[    2.190732] c0 guid: 1.0 
[    2.190737] c0 in avb_slot_verify, l:1626. 
[    2.190739] c0 in avb_slot_verify, l:1640. 
[    2.190742] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.190744] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.190746] c0 slot_data[0] is 0xe04d8458.
[    2.190748] c0 slot_data[1] is 0x0.
[    2.190750] c0 vboot_verify_ret is :0
[    2.190753] c0 enter copy just debug... 344.
[    2.190755] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20736 androidboot.vbmeta.digest=902e4b61148912b976b08ccfdc891b070e00a70ed3fdf2e84430
[    2.191099] c0 have got sip smc all from uboot###
[    2.191101] c0  args->params[0] is 0x20. ###
[    2.191103] c0 enter vboot verify
[    2.191105] c0 SECUREBOOTDataVal is not NULL.
[    2.191106] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.191111] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    2.191114] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.191118] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.191121] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    2.191126] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    2.191130] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    2.191133] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.191135] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.191137] c0 img_name:vbmeta_system_ext, g_avbUserData.img_name:vbmeta_system_ext.
[    2.191140] c0 avb check image name:vbmeta_system_ext.
[    2.191142] c0 ### enter avb_slot_verify. ###
[    2.191146] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.191150] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.191159] c0 [kbc] save vbmeta image for cp verify.
[    2.191169] c0 enter implement read_rollback_index().
[    2.191171] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.191173] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.191176] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.191180] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.191183] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191186] c0 Skip verify chain_partition[boot] while load verify vbmeta_system_ext partition... 
[    2.191189] c0 n = 0
[    2.191190] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191193] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_system_ext partition... 
[    2.191198] c0 n = 1
[    2.191200] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191203] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_system_ext partition... 
[    2.191205] c0 n = 2
[    2.191207] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191210] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_system_ext partition... 
[    2.191212] c0 n = 3
[    2.191214] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191217] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_system_ext partition... 
[    2.191219] c0 n = 4
[    2.191221] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191224] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_system_ext partition... 
[    2.191226] c0 n = 5
[    2.191228] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191231] c0 chain partition founded: n = 6
[    2.191236] c0 Loading vbmeta struct from partition 'read partition vbmeta_system_ext, offset: 0x0, bytes: 0x1000.
[    2.191240] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.193859] c0 partition: vbmeta_system_ext vbmeta_verify_ret is :0. 
[    2.193866] c0 enter implement read_rollback_index().
[    2.193868] c0 read_is_device_unlocked() rollback_index_slot = 3 
[    2.193870] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.193872] c0 Info: g_sprd_vboot_version.img_ver[3]= 0
[    2.193876] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:6.
[    2.193878] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193881] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193884] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193887] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193890] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.193894] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.193897] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.193899] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193902] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_system_ext partition... 
[    2.193905] c0 n = 7
[    2.193906] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193911] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_system_ext partition... 
[    2.193914] c0 n = 8
[    2.193915] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193918] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_system_ext partition... 
[    2.193921] c0 n = 9
[    2.193922] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193925] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_system_ext partition... 
[    2.193928] c0 n = 10
[    2.193929] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193932] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_system_ext partition... 
[    2.193934] c0 n = 11
[    2.193936] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193939] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_system_ext partition... 
[    2.193941] c0 n = 12
[    2.193943] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.193946] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_system_ext partition... 
[    2.193948] c0 n = 13
[    2.193950] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193953] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193956] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193959] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193961] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193964] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.193967] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.193970] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.193976] c0 read_is_device_unlocked() ret = 0 
[    2.194415] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.194419] c0 partition: vbmeta  guid_buf_size = 37 
[    2.194421] c0 guid: 1.0 
[    2.194427] c0 in avb_slot_verify, l:1626. 
[    2.194430] c0 in avb_slot_verify, l:1640. 
[    2.194434] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.194437] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.194439] c0 slot_data[0] is 0xe04d8458.
[    2.194440] c0 slot_data[1] is 0x0.
[    2.194442] c0 vboot_verify_ret is :0
[    2.194445] c0 enter copy just debug... 344.
[    2.194448] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=21184 androidboot.vbmeta.digest=901ec651a956470d9704564c065a780c509cab952b4b616c06d2
[    2.194785] c0 have got sip smc all from uboot###
[    2.194787] c0  args->params[0] is 0x20. ###
[    2.194789] c0 enter vboot verify
[    2.194791] c0 SECUREBOOTDataVal is not NULL.
[    2.194792] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.194797] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    2.194800] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.194803] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.194807] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    2.194810] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    2.194813] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    2.194818] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.194820] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.194823] c0 img_name:vbmeta_product, g_avbUserData.img_name:vbmeta_product.
[    2.194826] c0 avb check image name:vbmeta_product.
[    2.194828] c0 ### enter avb_slot_verify. ###
[    2.194832] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.194835] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.194847] c0 [kbc] save vbmeta image for cp verify.
[    2.194855] c0 enter implement read_rollback_index().
[    2.194857] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.194859] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.194861] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.194865] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.194868] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194873] c0 Skip verify chain_partition[boot] while load verify vbmeta_product partition... 
[    2.194876] c0 n = 0
[    2.194877] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194880] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_product partition... 
[    2.194883] c0 n = 1
[    2.194884] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194887] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_product partition... 
[    2.194890] c0 n = 2
[    2.194891] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194894] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_product partition... 
[    2.194897] c0 n = 3
[    2.194898] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194901] c0 chain partition founded: n = 4
[    2.194904] c0 Loading vbmeta struct from partition 'read partition vbmeta_product, offset: 0x0, bytes: 0x1000.
[    2.194907] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.197538] c0 partition: vbmeta_product vbmeta_verify_ret is :0. 
[    2.197545] c0 enter implement read_rollback_index().
[    2.197547] c0 read_is_device_unlocked() rollback_index_slot = 5 
[    2.197548] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.197551] c0 Info: g_sprd_vboot_version.img_ver[5]= 0
[    2.197554] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:4.
[    2.197557] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197559] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197562] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197566] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.197570] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.197572] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197575] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_product partition... 
[    2.197578] c0 n = 5
[    2.197579] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197582] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_product partition... 
[    2.197585] c0 n = 6
[    2.197586] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197589] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_product partition... 
[    2.197592] c0 n = 7
[    2.197593] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197596] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_product partition... 
[    2.197599] c0 n = 8
[    2.197600] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197603] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_product partition... 
[    2.197606] c0 n = 9
[    2.197607] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197610] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_product partition... 
[    2.197612] c0 n = 10
[    2.197614] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197617] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_product partition... 
[    2.197621] c0 n = 11
[    2.197623] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197626] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_product partition... 
[    2.197628] c0 n = 12
[    2.197630] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197633] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_product partition... 
[    2.197635] c0 n = 13
[    2.197637] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197639] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197642] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197645] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197647] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197650] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197653] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.197656] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.197662] c0 read_is_device_unlocked() ret = 0 
[    2.198091] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.198094] c0 partition: vbmeta  guid_buf_size = 37 
[    2.198096] c0 guid: 1.0 
[    2.198101] c0 in avb_slot_verify, l:1626. 
[    2.198103] c0 in avb_slot_verify, l:1640. 
[    2.198106] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.198108] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.198109] c0 slot_data[0] is 0xe04d8458.
[    2.198111] c0 slot_data[1] is 0x0.
[    2.198113] c0 vboot_verify_ret is :0
[    2.198116] c0 enter copy just debug... 344.
[    2.198118] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20736 androidboot.vbmeta.digest=f6bc39bf651b29d089e38f5c03eda0929887d1332a49bd8818ee
[    2.198459] c0 have got sip smc all from uboot###
[    2.198461] c0  args->params[0] is 0x20. ###
[    2.198463] c0 enter vboot verify
[    2.198464] c0 SECUREBOOTDataVal is not NULL.
[    2.198466] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.198473] c0 from uboot smc...addr:0x9f4a4000 len:0x24
[    2.198476] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.198480] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.198483] c0 from uboot smc...addr:0x9f4a7000 len:0x20
[    2.198486] c0 from uboot smc...addr:0x9f4a9000 len:0x200
[    2.198489] c0 from uboot smc...addr:0x9f4a8000 len:0x10
[    2.198493] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.198495] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.198499] c0 img_name:vbmeta_odm, g_avbUserData.img_name:vbmeta_odm.
[    2.198502] c0 avb check image name:vbmeta_odm.
[    2.198504] c0 ### enter avb_slot_verify. ###
[    2.198508] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.198512] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.198522] c0 [kbc] save vbmeta image for cp verify.
[    2.198533] c0 enter implement read_rollback_index().
[    2.198534] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.198536] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.198539] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.198543] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.198545] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198548] c0 Skip verify chain_partition[boot] while load verify vbmeta_odm partition... 
[    2.198551] c0 n = 0
[    2.198552] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198555] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_odm partition... 
[    2.198558] c0 n = 1
[    2.198559] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198562] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_odm partition... 
[    2.198565] c0 n = 2
[    2.198566] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198569] c0 chain partition founded: n = 3
[    2.198572] c0 Loading vbmeta struct from partition 'read partition vbmeta_odm, offset: 0x0, bytes: 0x1000.
[    2.198575] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.201253] c0 partition: vbmeta_odm vbmeta_verify_ret is :0. 
[    2.201260] c0 enter implement read_rollback_index().
[    2.201262] c0 read_is_device_unlocked() rollback_index_slot = 7 
[    2.201263] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.201266] c0 Info: g_sprd_vboot_version.img_ver[7]= 0
[    2.201269] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:3.
[    2.201272] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201274] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201277] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.201280] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.201283] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201286] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_odm partition... 
[    2.201288] c0 n = 4
[    2.201290] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201293] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_odm partition... 
[    2.201295] c0 n = 5
[    2.201297] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201300] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_odm partition... 
[    2.201303] c0 n = 6
[    2.201304] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201307] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_odm partition... 
[    2.201310] c0 n = 7
[    2.201311] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201314] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_odm partition... 
[    2.201317] c0 n = 8
[    2.201318] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201321] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_odm partition... 
[    2.201323] c0 n = 9
[    2.201325] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201328] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_odm partition... 
[    2.201330] c0 n = 10
[    2.201332] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201335] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_odm partition... 
[    2.201337] c0 n = 11
[    2.201338] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201341] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_odm partition... 
[    2.201346] c0 n = 12
[    2.201347] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.201350] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_odm partition... 
[    2.201353] c0 n = 13
[    2.201354] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201357] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201359] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201362] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201365] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201367] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.201370] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.201373] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.201379] c0 read_is_device_unlocked() ret = 0 
[    2.201808] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.201810] c0 partition: vbmeta  guid_buf_size = 37 
[    2.201812] c0 guid: 1.0 
[    2.201817] c0 in avb_slot_verify, l:1626. 
[    2.201819] c0 in avb_slot_verify, l:1640. 
[    2.201822] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.201824] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.201825] c0 slot_data[0] is 0xe04d8458.
[    2.201827] c0 slot_data[1] is 0x0.
[    2.201829] c0 vboot_verify_ret is :0
[    2.201832] c0 enter copy just debug... 344.
[    2.201834] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20672 androidboot.vbmeta.digest=29e0ba060e35baf7929c7ca39a8deee3af622f649d6085d58a0d
[    2.245010] c1 have got sip smc all from uboot###
[    2.245014] c1  args->params[0] is 0xa. ###
[    2.245121] c1 process_sip_call, case FUNCTYPE_GET_LCS res:0, tmp is 1
[    2.279557] c0 have got sip smc all from uboot###
[    2.279560] c0  args->params[0] is 0x36. ###
[    2.279563] c0 from uboot smc...addr:0x9f1a4000 len:0x400
[    2.279591] c0 xing offset = 25.
[    2.279594] c0 xing offset2 = 64.
[    2.279621] c0 xing offset = 25.
[    2.279623] c0 xing offset2 = 64.
[    2.283691] c0 have got sip smc all from uboot###
[    2.283695] c0  args->params[0] is 0x40. ###
[    2.283699] c0 from uboot smc(set chip uid)...addr:0x9f1a3000 len:0x8
[    2.283703] c0 get cpu id: 0x7439c2 0x401d409
[    2.283706] c0 chip uid from uboot(id=5) len=8:
[    2.283708] c0 c2 39 74 00 09 d4 01 04 
[    2.283715] c0 key[5] data has been saved!
[    2.283745] c0 have got sip smc all from uboot###
[    2.283749] c0  args->params[0] is 0x22. ###
[    2.283754] c0 enter set rpmb 4194304
[    2.285322] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285338] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285391] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285404] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285508] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285518] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285527] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
<    2.285600> ss: sec_rpmb_bl_client_handle_msg: use new key
[    2.285630] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285645] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
<    2.285655> ss-ipc: 185: do_disconnect ev->handle ox3ea
<    2.285663> ss: sec_rpmb_bl_disconnect: handle 0x3ea
[    2.285676] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285691] c0 have got sip smc all from uboot###
[    2.285693] c0  args->params[0] is 0x25. ###
[    2.285695] c0 set rpmb type 205
[    2.285735] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285760] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285812] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285825] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285861] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285870] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285890] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285933] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285955] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285968] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.285997] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286006] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286027] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286057] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286079] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286092] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286124] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286134] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286152] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94a300: pa=0x9f94a000: mair=0xff, sharable=0x3
[    2.286185] c0 have got sip smc all from uboot###
[    2.286188] c0  args->params[0] is 0x18. ###
[    2.286204] c0 process_sip_call, case FUNCTYPE_CHECK_CPU_FLASH_BIND, res is:1
[    2.913912] c3 entering scheduler on cpu 3
[    2.916398] c4 entering scheduler on cpu 4
[    2.918645] c5 entering scheduler on cpu 5
[    2.920927] c6 entering scheduler on cpu 6
[    2.922949] c7 entering scheduler on cpu 7
[   15.047305] c6 tam_load_request:1513: load look up com.android.trusty.gatekeeper
[   15.047314] c6 handle_conn_req:412: failed (-2) to send response
<   17.141438> ss: cal_rpmb_block_size: rpmb write data-size 512 to addr (16378) sucessfull
<   17.141456> ss: cal_rpmb_block_size: set rpmb block size 512
<   17.147599> ss: block_device_tipc_rpmb_init: rpmb device's block size 512 block count 8178
<   17.151350> super_block_valid:344: bad magic, 0x7beacb50d2c449d7
<   17.155929> fs_init_from_super:603: super block: files at 6 free at 5
<   17.168465> fs_init_from_super:634: loaded super block version 1
[   17.168504] c0 tam_port_publish:1501:  other port com.android.trusty.storage.client.tp
<   17.168513> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tp success
[   17.168529] c0 tam_port_publish:1501:  other port com.android.trusty.storage.client.tdea
<   17.168533> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tdea success
<   17.168541> ss: block_device_tipc_rpmb_init: rb device's block size 4040 block count 1048576
[   17.182580] c5 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   17.182611] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   17.182641] c5 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   17.182644] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   17.182648] c5 ta_manager_verify_img:447: RSA_hash
[   17.183747] c0 ta_manager_verify_img:506: RSA_verify
[   17.185564] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   17.185575] c0 trusty_app:(32) start 0xffffffffe07de000 size 0x00017000
[   17.185585] c0 trusty_app: whitelist.table 0x0, size: 0
[   17.185588] c0 trusty_app 13 uuid: 0x38ba0cdc 0xdf0e 0x11e4 0x9869 0x233fb6ae4795
[   17.185597] c0 trusty_app 0xffffffffe0713dd0: stack_sz=0x1000
[   17.185600] c0 trusty_app 0xffffffffe0713dd0: heap_sz=0x2000
[   17.185603] c0 trusty_app 0xffffffffe0713dd0: one_shot=0x0
[   17.185606] c0 trusty_app 0xffffffffe0713dd0: keep_alive=0x0
[   17.185609] c0 trusty_app 0xffffffffe0713dd0: flags=0x1c
[   17.185612] c0 ta_manager_write_ta:985: enter tam anti rollback
[   17.185620] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   17.185624] c0 ta_manager_write_ta:997: tam anti rollback ok
[   17.185627] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   17.185631] c0 trusty_tapp_init:
[   17.185705] c0 trusty_app 13: code: start 0x00008000 end 0x0001aa1c
[   17.185711] c0 trusty_app 13: data: start 0x0001b000 end 0x0001c000
[   17.185715] c0 trusty_app 13: bss:                end 0x0001b420
[   17.185719] c0 trusty_app 13: brk:  start 0x0001c000 end 0x0001e000
[   17.185723] c0 trusty_app 13: entry 0x0000b418
<   17.185743> trusty_gatekeeper_ta: 304: Initializing
<   17.185755> trusty_gatekeeper_ta: 89: ReseedRng
<   17.185979> trusty_gatekeeper_ta: 97: ReseedRng ok
[   17.186006] c0 tam_port_publish:1496: publish port com.android.trusty.gatekeeper
[   17.186020] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.gatekeeper accomplished!
<   17.216951> super_block_valid:344: bad magic, 0x7beacb50d2c449d7
<   17.221591> super_block_valid:344: bad magic, 0x7beacb50d2c449d7
<   17.221611> fs_init_from_super:641: clear requested, create empty, version 0
<   17.221661> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tr success
<   17.225763> sprdimgversion: sprdimgverion_create_port: rpmb read image/mode image version blk (16382,16380) successful.
<   17.225801> ss: rpmb_proxy_connect: create port com.spreadtrum.sprdimgversion success
<   17.230430> sec_rpmbdata: sec_rpmbdata_create_port: rpmb read sec rpmb data (13, 1) (16365, 16377) successful.
<   17.230468> ss: rpmb_proxy_connect: create port com.spreadtrum.secrpmbdata success
<   17.231015> ss: block_device_tipc_ns_init: ns device's block size 4040 block count 1048576
<   17.266473> block_cache_complete_read: load block 0 failed
<   17.266488> block_cache_load_entry: failed to load block 0
<   17.268540> block_cache_complete_read: load block 1 failed
<   17.268556> block_cache_load_entry: failed to load block 1
<   17.268565> fs_init_from_super:641: clear requested, create empty, version 0
<   17.268664> ss: block_device_tipc_ns_init: create port com.android.trusty.storage.client.td success
<   17.299502> trusty_kernelbootcp: 75: cmd KERNEL_BOOTCP_UNLOCK_DDR
<   17.299517> trusty_kernelbootcp: 322: TA:kbc_unlock_ddr() 
[   17.299526] c0 enter SEC_KBC_GET_TEECFG_FLAG
[   17.299535] c0 pal:g_wifionly_flag = 0 
<   17.299538> trusty_kernelbootcp: 331: TA:g_ioctl_cunter = 1,g_wifionly_flag = 0
[   17.299546] c0 enter SEC_KBC_STOP_CP
[   17.299549] c0 dump_table() len = 0 maplen = 0 addr = 0 
[   17.299553] c0 dump_table() len = 0 maplen = 0 addr = 0 
[   17.299556] c0 dump_table() len = 0 maplen = 0 addr = 0 
[   17.299559] c0 dump_table() len = 0 maplen = 0 addr = 0 
[   17.299562] c0 dump_table() flag = 31 
[   17.299564] c0 dump_table() is_packed = 0 
[   17.299567] c0 kbc_stop_cp() enter MODEM_IMG
[   17.299569] c0 reg_addr = 0xffffffffe61dc174
[   17.299575] c0 before reg = 0400
[   17.299581] c0 after  reg = 0400
[   17.299584] c0 sleep 50ms start 
[   17.350691] c0 reg_addr = 0xffffffffe61ccb98
[   17.350704] c0 before reg = 4800
[   17.350710] c0 after  reg = 4800
[   17.350713] c0 sleep 50ms start 
[   17.400892] c0 reg_addr = 0xffffffffe61cc818
[   17.400905] c0 before reg = 0006
[   17.400912] c0 after  reg = 0006
[   17.400915] c0 sleep 50ms start 
[   17.451025] c0 reg_addr = 0xffffffffe61cc330
[   17.451038] c0 before reg = 2010101
[   17.451044] c0 after  reg = 2010101
[   17.451047] c0 sleep 50ms start 
[   17.501240] c0 reg_addr = 0xffffffffe61dc08c
[   17.501253] c0 before reg = 0001
[   17.501260] c0 after  reg = 0001
[   17.501263] c0 sleep 50ms start SP_IMG
[   17.551352] c0 sleep end 
[   17.551359] c0 kbc_stop_cp() leave 
[   17.551366] c0 enter SEC_KBC_GET_LOAD_MODEM_FLAG
[   17.551368] c0 pal: g_load_modem_flag = 0 
<   17.551374> trusty_kernelbootcp: 339: TA:g_ioctl_cunter = 2,g_load_modem_flag = 0
<   17.551388> trusty_kernelbootcp: 340: TA:SEC_KBC_STOP_CP() ret = 0
<   17.551534> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   17.551543> footer
<   17.551547> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551555> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551563> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551571> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551579> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   17.551586> footer
<   17.551590> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551598> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551606> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551614> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551622> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   17.551628> footer
<   17.551632> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551640> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551647> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551655> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551663> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   17.551670> footer
<   17.551674> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551682> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551689> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551697> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   17.551705> trusty_kernelbootcp: 287: dump_table() flag = 31 
<   17.551711> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
<   17.551779> trusty_kernelbootcp: 348: TA:SEC_FIREWALL_UNLOCK_CP_DDR() ret = 0
<   17.551797> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
[   18.197567] c7 tam_load_request:1513: load look up com.android.trusty.identity
[   18.197576] c7 handle_conn_req:412: failed (-2) to send response
[   18.220347] c6 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   18.220372] c6 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   18.227954] c5 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   18.227966] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   18.227971] c5 ta_manager_verify_img:447: RSA_hash
[   18.232387] c0 ta_manager_verify_img:506: RSA_verify
[   18.235427] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   18.235445] c0 trusty_app:(32) start 0xffffffffe07fa000 size 0x0008b000
[   18.235463] c0 trusty_app: whitelist.table 0x0, size: 0
[   18.235467] c0 trusty_app 14 uuid: 0x3f3010ec 0xfc8 0xc8a2 0x9110 0xc5ef1de1233a
[   18.235476] c0 trusty_app 0xffffffffe04df130: stack_sz=0x40000
[   18.235479] c0 trusty_app 0xffffffffe04df130: heap_sz=0x40000
[   18.235482] c0 trusty_app 0xffffffffe04df130: one_shot=0x0
[   18.235485] c0 trusty_app 0xffffffffe04df130: keep_alive=0x0
[   18.235488] c0 trusty_app 0xffffffffe04df130: flags=0x1c
[   18.235491] c0 ta_manager_write_ta:985: enter tam anti rollback
[   18.235508] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   18.235518] c0 ta_manager_write_ta:997: tam anti rollback ok
[   18.235521] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   18.235526] c0 trusty_tapp_init:
[   18.235709] c0 trusty_app 14: code: start 0x00008000 end 0x0008e3c4
[   18.235715] c0 trusty_app 14: data: start 0x0008f000 end 0x00090000
[   18.235720] c0 trusty_app 14: bss:                end 0x0008fa14
[   18.235724] c0 trusty_app 14: brk:  start 0x00090000 end 0x000d0000
[   18.235729] c0 trusty_app 14: entry 0x00011130
[   18.236103] c0 tam_port_publish:1501:  other port com.android.trusty.identity.secure
[   18.236166] c0 tam_port_publish:1496: publish port com.android.trusty.identity
[   18.236213] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.identity accomplished!
[   18.307612] c2 tam_load_request:1513: load look up com.android.trusty.focalfp
[   18.307624] c2 handle_conn_req:412: failed (-2) to send response
[   18.378542] c4 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   18.378588] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   18.418112] c0 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   18.418124] c0 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   18.418128] c0 ta_manager_verify_img:447: RSA_hash
[   18.448222] c0 ta_manager_verify_img:506: RSA_verify
[   18.450393] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   18.450409] c0 trusty_app:(32) start 0xffffffffe092d000 size 0x002ae000
[   18.450467] c0 trusty_app: whitelist.table 0x0, size: 0
[   18.450471] c0 trusty_app 15 uuid: 0x4c2cd891 0x219d 0x43d8 0xbe2d 0x39fa2080a26f
[   18.450479] c0 trusty_app 0xffffffffe0722c60: stack_sz=0x100000
[   18.450482] c0 trusty_app 0xffffffffe0722c60: heap_sz=0xe00000
[   18.450486] c0 trusty_app 0xffffffffe0722c60: one_shot=0x0
[   18.450489] c0 trusty_app 0xffffffffe0722c60: keep_alive=0x0
[   18.450492] c0 trusty_app 0xffffffffe0722c60: flags=0x1c
[   18.450495] c0 ta_manager_write_ta:985: enter tam anti rollback
[   18.450502] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   18.450506] c0 ta_manager_write_ta:997: tam anti rollback ok
[   18.450509] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   18.450514] c0 trusty_tapp_init:
[   18.454310] c0 trusty_app 15: code: start 0x00008000 end 0x001f0908
[   18.454321] c0 trusty_app 15: data: start 0x001f1000 end 0x002b3000
[   18.454325] c0 trusty_app 15: bss:                end 0x002b2dcc
[   18.454329] c0 trusty_app 15: brk:  start 0x002b3000 end 0x010b3000
[   18.454334] c0 trusty_app 15: entry 0x001b4ef8
<   18.454366> *** Libc Version: 2.2, Built: 02:35:24 Jun  3 2022 ***
<   18.454382> I focaltech:ta:entry: TEE-Sprd TA is born.
[   18.454413] c0 tam_port_publish:1496: publish port com.android.trusty.focalfp
[   18.454428] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.focalfp accomplished!
<   18.454852> D focaltech:ta:base: v2.5.5.0-d60319b-250310@1631
<   18.454865> I focaltech:ta:base: TEE environment checking...
<   18.454876> I focaltech:ta:base: testing timing...0(0)ms.
<   18.504087> I focaltech:ta:base: testing msleep(50)...0ms.
<   18.504104> I focaltech:ta:base: TA is successfully created.
<   18.504789> I focaltech:ta:config: configurations is synced to TA.
<   18.504802> W focaltech:ta:config: no key named 'device.enroll_type', use default value.
<   18.504821> W focaltech:ta:config: no key named 'common.max_authentication_failed_attemps', use default value.
<   18.570039> I focaltech:ta:device: registering chips...
<   18.592460> E focaltech:ta:device: error at ff_device_probe[device.c:102]: failed to probe focaltech fingerprint device.
<   18.592478> E focaltech:ta:device: error at ff_device_probe[device.c:103]: 'Device not found'.
<   18.594910> trusty_kernelbootcp: 67: cmd KERNEL_BOOTCP_VERIFY_ALL
<   18.594925> trusty_kernelbootcp: 177: TA:kbc_verify_all_avb2() 
<   18.594932> trusty_kernelbootcp: 281: dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
<   18.594940> footer
<   18.594944> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   18.594953> 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
<   18.594961> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   18.594968> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.594976> trusty_kernelbootcp: 281: dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
<   18.594983> footer
<   18.594987> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   18.594995> 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
<   18.595004> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   18.595011> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.595019> trusty_kernelbootcp: 281: dump_table() len = 440000 maplen = 440000 addr = 89620000 
<   18.595026> footer
<   18.595030> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   18.595038> 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
<   18.595046> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   18.595597> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.595607> trusty_kernelbootcp: 281: dump_table() len = 200000 maplen = 200000 addr = 88040000 
<   18.595615> footer
<   18.595619> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   18.595627> 0010 00 0b a0 00 00 00 00 00 00 0b a0 00 00 00 00 00
<   18.595635> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   18.595643> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.595651> trusty_kernelbootcp: 287: dump_table() flag = 31 
<   18.595657> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
[   18.595667] c5 enter sec_kbc_check_verify_table
<   18.595684> trusty_kernelbootcp: 167: TA:table->flag = 0x1f:
<   18.595761> trusty_kernelbootcp: 182: TA:SEC_FIREWALL_LOCK_CP_DRR() ret = 0
[   18.595768] c5 enter SEC_KBC_VERIFY_ALL_V2
[   18.595773] c5 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[   18.595778] c5 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[   18.595781] c5 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[   18.595784] c5 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[   18.595788] c5 dump_table() flag = 31 
[   18.595790] c5 dump_table() is_packed = 0 
[   18.595793] c5 [kbc]kbc_image_verify_v2() enter.
[   18.595796] c5 [kbc]call mem map:ns addr = 0x8b000000 map len = 0x1380000 
[   18.596581] c5 [kbc]after map:pAddr = 0xffffffffe662e000
[   18.596589] c5 pAddr:
[   18.596591] c5 0000 4d 45 43 50 56 31 2e 30 00 06 00 00 00 00 00 00
[   18.596606] c5 [kbc]avb_userdata_set() packed offset = 0x0
[   18.596609] c5 [kbc]start verify... 
[   18.596611] c5 [kbc]avb_check_kbc_image() name = l_modem
[   18.596617] c5 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_modem
[   18.596620] c5 ### enter avb_slot_verify. ###
[   18.596629] c5 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   18.596654] c5 [kbc]read_from_partition_kbc leave buf = 0xe07c3a90
[   18.599438] c5 partition: vbmeta vbmeta_verify_ret is :0. 
[   18.599444] c5 Enter: implement validate_vbmeta_public_key().
[   18.599446] c5 dump public_key_hash: public_key_hash_length:32. 
[   18.599449] c5 cal_sha256(): enter cal_sha256 
[   18.599459] c5 expected_public_key is matched.
[   18.599461] c5 enter implement read_rollback_index().
[   18.599463] c5 read_is_device_unlocked() rollback_index_slot = 0 
[   18.599466] c5 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.599470] c5 Info: g_sprd_vboot_version.img_ver[0]= 0
[   18.599476] c5 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   18.599480] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599486] c5 Skip verify chain_partition[boot] while load verify l_modem partition... 
[   18.599489] c5 n = 0
[   18.599491] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599495] c5 Skip verify chain_partition[dtbo] while load verify l_modem partition... 
[   18.599498] c5 n = 1
[   18.599500] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599503] c5 Skip verify chain_partition[init_boot] while load verify l_modem partition... 
[   18.599506] c5 n = 2
[   18.599508] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599512] c5 Skip verify chain_partition[vbmeta_odm] while load verify l_modem partition... 
[   18.599515] c5 n = 3
[   18.599516] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599520] c5 Skip verify chain_partition[vbmeta_product] while load verify l_modem partition... 
[   18.599523] c5 n = 4
[   18.599525] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599528] c5 Skip verify chain_partition[vbmeta_system] while load verify l_modem partition... 
[   18.599531] c5 n = 5
[   18.599533] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599537] c5 Skip verify chain_partition[vbmeta_system_ext] while load verify l_modem partition... 
[   18.599540] c5 n = 6
[   18.599542] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599545] c5 Skip verify chain_partition[vbmeta_vendor] while load verify l_modem partition... 
[   18.599548] c5 n = 7
[   18.599550] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599553] c5 Skip verify chain_partition[vendor_boot] while load verify l_modem partition... 
[   18.599557] c5 n = 8
[   18.599558] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.599562] c5 chain partition founded: n = 9
[   18.599566] c5 [kbc]read partition name: l_modem, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   18.599569] c5 [kbc]read footer
[   18.599572] c5 dump footer
[   18.599574] c5 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   18.599588] c5 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
[   18.599603] c5 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   18.599618] c5 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.599633] c5 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   18.599637] c5 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_modem, offset = 0xd39000, num_bytes = 0x840
[   18.599641] c5 [kbc]read certificate: img_addr = 0xe662e000
[   18.599644] c5 [kbc]read certificate: offset = 0xd39000
[   18.599648] c5 dump certificate
[   18.599650] c5 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   18.599665] c5 [kbc]read_from_partition_kbc leave buf = 0xe07d3b68
[   18.604697] c5 partition: l_modem vbmeta_verify_ret is :0. 
[   18.604714] c5 enter implement read_rollback_index().
[   18.604716] c5 read_is_device_unlocked() rollback_index_slot = 11 
[   18.604719] c5 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.604723] c5 Info: g_sprd_vboot_version.img_ver[11]= 0
[   18.604730] c5 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   18.604734] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   18.604742] c5 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   18.604745] c5 check dat cp
[   18.737682] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   18.737812] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   18.737819] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.737826] c0 Skip verify chain_partition[l_ldsp] while load verify l_modem partition... 
[   18.737830] c0 n = 10
[   18.737832] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.737836] c0 Skip verify chain_partition[l_gdsp] while load verify l_modem partition... 
[   18.737839] c0 n = 11
[   18.737841] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.737845] c0 Skip verify chain_partition[pm_sys] while load verify l_modem partition... 
[   18.737848] c0 n = 12
[   18.737850] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.737854] c0 Skip verify chain_partition[l_agdsp] while load verify l_modem partition... 
[   18.737857] c0 n = 13
[   18.737858] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.737862] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.737865] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.737869] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.737872] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.737876] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.737881] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   18.737886] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   18.737898] c0 read_is_device_unlocked() ret = 0 
[   18.738311] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   18.738314] c0 partition: vbmeta  guid_buf_size = 37 
[   18.738318] c0 guid: 1.0 
[   18.738323] c0 in avb_slot_verify, l:1626. 
[   18.738327] c0 in avb_slot_verify, l:1640. 
[   18.738330] c0 [kbc]avb_slot_verify result is 0.
[   18.738333] c0 [kbc]l_modem_avb_slot_verify result is OK.
[   18.738336] c0 [kbc]slot_data[0] is 0xe07c3050.
[   18.738339] c0 [kbc]slot_data[1] is 0x0.
[   18.738344] c0 [kbc]ret = 0
[   18.738492] c0 [kbc]call mem map:ns addr = 0x89aa8000 map len = 0xb00000 
[   18.738885] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[   18.738889] c0 pAddr:
[   18.738892] c0 0000 00 5a 5a 5a 08 73 e6 d7 b0 20 00 18 08 db e6 d7
[   18.738907] c0 [kbc]avb_userdata_set() packed offset = 0x0
[   18.738910] c0 [kbc]start verify... 
[   18.738912] c0 [kbc]avb_check_kbc_image() name = l_ldsp
[   18.738915] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_ldsp
[   18.738919] c0 ### enter avb_slot_verify. ###
[   18.738927] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   18.738959] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c3a90
[   18.738964] c0 enter implement read_rollback_index().
[   18.738966] c0 read_is_device_unlocked() rollback_index_slot = 0 
[   18.738969] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.738972] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[   18.738980] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   18.738984] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.738988] c0 Skip verify chain_partition[boot] while load verify l_ldsp partition... 
[   18.738991] c0 n = 0
[   18.738993] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.738996] c0 Skip verify chain_partition[dtbo] while load verify l_ldsp partition... 
[   18.738999] c0 n = 1
[   18.739001] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739005] c0 Skip verify chain_partition[init_boot] while load verify l_ldsp partition... 
[   18.739008] c0 n = 2
[   18.739009] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739013] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_ldsp partition... 
[   18.739016] c0 n = 3
[   18.739017] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739021] c0 Skip verify chain_partition[vbmeta_product] while load verify l_ldsp partition... 
[   18.739024] c0 n = 4
[   18.739026] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739030] c0 Skip verify chain_partition[vbmeta_system] while load verify l_ldsp partition... 
[   18.739033] c0 n = 5
[   18.739034] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739038] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_ldsp partition... 
[   18.739041] c0 n = 6
[   18.739043] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739047] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_ldsp partition... 
[   18.739050] c0 n = 7
[   18.739051] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739055] c0 Skip verify chain_partition[vendor_boot] while load verify l_ldsp partition... 
[   18.739058] c0 n = 8
[   18.739059] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739063] c0 Skip verify chain_partition[l_modem] while load verify l_ldsp partition... 
[   18.739066] c0 n = 9
[   18.739068] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.739072] c0 chain partition founded: n = 10
[   18.739076] c0 [kbc]read partition name: l_ldsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   18.739080] c0 [kbc]read footer
[   18.739081] c0 dump footer
[   18.739084] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   18.739098] c0 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
[   18.739113] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   18.739127] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.739142] c0 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   18.739147] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_ldsp, offset = 0x300000, num_bytes = 0x840
[   18.739151] c0 [kbc]read certificate: img_addr = 0xe662e000
[   18.739155] c0 [kbc]read certificate: offset = 0x300000
[   18.739158] c0 dump certificate
[   18.739161] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   18.739175] c0 [kbc]read_from_partition_kbc leave buf = 0xe07d3b68
[   18.744282] c0 partition: l_ldsp vbmeta_verify_ret is :0. 
[   18.744299] c0 enter implement read_rollback_index().
[   18.744301] c0 read_is_device_unlocked() rollback_index_slot = 12 
[   18.744304] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.744308] c0 Info: g_sprd_vboot_version.img_ver[12]= 0
[   18.744315] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   18.744319] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   18.744327] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   18.744330] c0 check dat cp
[   18.773789] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   18.773801] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   18.773805] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.773811] c0 Skip verify chain_partition[l_gdsp] while load verify l_ldsp partition... 
[   18.773814] c0 n = 11
[   18.773817] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.773820] c0 Skip verify chain_partition[pm_sys] while load verify l_ldsp partition... 
[   18.773824] c0 n = 12
[   18.773826] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.773830] c0 Skip verify chain_partition[l_agdsp] while load verify l_ldsp partition... 
[   18.773833] c0 n = 13
[   18.773834] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.773838] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.773842] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.773846] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.773850] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.773854] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.773858] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   18.773863] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   18.773874] c0 read_is_device_unlocked() ret = 0 
[   18.774288] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   18.774291] c0 partition: vbmeta  guid_buf_size = 37 
[   18.774294] c0 guid: 1.0 
[   18.774301] c0 in avb_slot_verify, l:1626. 
[   18.774304] c0 in avb_slot_verify, l:1640. 
[   18.774307] c0 [kbc]avb_slot_verify result is 0.
[   18.774309] c0 [kbc]l_ldsp_avb_slot_verify result is OK.
[   18.774312] c0 [kbc]slot_data[0] is 0xe07c3050.
[   18.774315] c0 [kbc]slot_data[1] is 0x0.
[   18.774320] c0 [kbc]ret = 0
[   18.774414] c0 [kbc]call mem map:ns addr = 0x89620000 map len = 0x440000 
[   18.774584] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[   18.774588] c0 pAddr:
[   18.774590] c0 0000 2e 50 53 44 38 38 43 53 5f 53 30 30 00 4d 53 47
[   18.774605] c0 [kbc]avb_userdata_set() packed offset = 0x0
[   18.774608] c0 [kbc]start verify... 
[   18.774610] c0 [kbc]avb_check_kbc_image() name = l_gdsp
[   18.774614] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_gdsp
[   18.774617] c0 ### enter avb_slot_verify. ###
[   18.774625] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   18.774656] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c3a90
[   18.774660] c0 enter implement read_rollback_index().
[   18.774662] c0 read_is_device_unlocked() rollback_index_slot = 0 
[   18.774665] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.774668] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[   18.774676] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   18.774680] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774685] c0 Skip verify chain_partition[boot] while load verify l_gdsp partition... 
[   18.774688] c0 n = 0
[   18.774689] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774693] c0 Skip verify chain_partition[dtbo] while load verify l_gdsp partition... 
[   18.774696] c0 n = 1
[   18.774698] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774701] c0 Skip verify chain_partition[init_boot] while load verify l_gdsp partition... 
[   18.774705] c0 n = 2
[   18.774706] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774710] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_gdsp partition... 
[   18.774713] c0 n = 3
[   18.774715] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774718] c0 Skip verify chain_partition[vbmeta_product] while load verify l_gdsp partition... 
[   18.774722] c0 n = 4
[   18.774723] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774727] c0 Skip verify chain_partition[vbmeta_system] while load verify l_gdsp partition... 
[   18.774730] c0 n = 5
[   18.774732] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774736] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_gdsp partition... 
[   18.774739] c0 n = 6
[   18.774740] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774744] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_gdsp partition... 
[   18.774748] c0 n = 7
[   18.774749] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774753] c0 Skip verify chain_partition[vendor_boot] while load verify l_gdsp partition... 
[   18.774756] c0 n = 8
[   18.774758] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774761] c0 Skip verify chain_partition[l_modem] while load verify l_gdsp partition... 
[   18.774764] c0 n = 9
[   18.774766] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774770] c0 Skip verify chain_partition[l_ldsp] while load verify l_gdsp partition... 
[   18.774772] c0 n = 10
[   18.774774] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.774778] c0 chain partition founded: n = 11
[   18.774782] c0 [kbc]read partition name: l_gdsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   18.774786] c0 [kbc]read footer
[   18.774787] c0 dump footer
[   18.774909] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   18.774926] c0 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
[   18.774941] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   18.774955] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.774971] c0 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   18.774977] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_gdsp, offset = 0x24e000, num_bytes = 0x840
[   18.774982] c0 [kbc]read certificate: img_addr = 0xe662e000
[   18.774985] c0 [kbc]read certificate: offset = 0x24e000
[   18.774989] c0 dump certificate
[   18.774991] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   18.775006] c0 [kbc]read_from_partition_kbc leave buf = 0xe07d3b68
[   18.781509] c0 partition: l_gdsp vbmeta_verify_ret is :0. 
[   18.781527] c0 enter implement read_rollback_index().
[   18.781529] c0 read_is_device_unlocked() rollback_index_slot = 13 
[   18.781532] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.781536] c0 Info: g_sprd_vboot_version.img_ver[13]= 0
[   18.781543] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   18.781547] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   18.781556] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   18.781559] c0 check dat cp
<   18.821217> E focaltech:ta:device: error at ff_device_probe[device.c:102]: failed to probe focaltech fingerprint device.
<   18.821238> E focaltech:ta:device: error at ff_device_probe[device.c:103]: 'Device not found'.
[   18.821565] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   18.821579] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   18.821583] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.821589] c0 Skip verify chain_partition[pm_sys] while load verify l_gdsp partition... 
[   18.821592] c0 n = 12
[   18.821594] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.821598] c0 Skip verify chain_partition[l_agdsp] while load verify l_gdsp partition... 
[   18.821601] c0 n = 13
[   18.821603] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.821607] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.821611] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.821614] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.821618] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.821623] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.821628] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   18.821633] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   18.821644] c0 read_is_device_unlocked() ret = 0 
[   18.822263] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   18.822271] c0 partition: vbmeta  guid_buf_size = 37 
[   18.822274] c0 guid: 1.0 
[   18.822280] c0 in avb_slot_verify, l:1626. 
[   18.822284] c0 in avb_slot_verify, l:1640. 
[   18.822288] c0 [kbc]avb_slot_verify result is 0.
[   18.822290] c0 [kbc]l_gdsp_avb_slot_verify result is OK.
[   18.822294] c0 [kbc]slot_data[0] is 0xe07c3050.
[   18.822297] c0 [kbc]slot_data[1] is 0x0.
[   18.822349] c0 [kbc]ret = 0
[   18.822474] c0 [kbc]call mem map:ns addr = 0x88040000 map len = 0x200000 
[   18.822564] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[   18.822568] c0 pAddr:
[   18.822570] c0 0000 df f8 94 d0 25 49 26 4a 26 4b 91 42 06 d0 9a 42
[   18.822585] c0 [kbc]avb_userdata_set() packed offset = 0x0
[   18.822587] c0 [kbc]start verify... 
[   18.822589] c0 [kbc]avb_check_kbc_image() name = pm_sys
[   18.822593] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = pm_sys
[   18.822596] c0 ### enter avb_slot_verify. ###
[   18.822603] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   18.822630] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c3a90
[   18.822635] c0 enter implement read_rollback_index().
[   18.822638] c0 read_is_device_unlocked() rollback_index_slot = 0 
[   18.822641] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.822644] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[   18.822652] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   18.822656] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822662] c0 Skip verify chain_partition[boot] while load verify pm_sys partition... 
[   18.822665] c0 n = 0
[   18.822666] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822670] c0 Skip verify chain_partition[dtbo] while load verify pm_sys partition... 
[   18.822673] c0 n = 1
[   18.822675] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822679] c0 Skip verify chain_partition[init_boot] while load verify pm_sys partition... 
[   18.822682] c0 n = 2
[   18.822683] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822687] c0 Skip verify chain_partition[vbmeta_odm] while load verify pm_sys partition... 
[   18.822690] c0 n = 3
[   18.822692] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822696] c0 Skip verify chain_partition[vbmeta_product] while load verify pm_sys partition... 
[   18.822699] c0 n = 4
[   18.822701] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822704] c0 Skip verify chain_partition[vbmeta_system] while load verify pm_sys partition... 
[   18.822708] c0 n = 5
[   18.822709] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822713] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify pm_sys partition... 
[   18.822716] c0 n = 6
[   18.822717] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822721] c0 Skip verify chain_partition[vbmeta_vendor] while load verify pm_sys partition... 
[   18.822724] c0 n = 7
[   18.822726] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822729] c0 Skip verify chain_partition[vendor_boot] while load verify pm_sys partition... 
[   18.822732] c0 n = 8
[   18.822734] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822737] c0 Skip verify chain_partition[l_modem] while load verify pm_sys partition... 
[   18.822740] c0 n = 9
[   18.822742] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822746] c0 Skip verify chain_partition[l_ldsp] while load verify pm_sys partition... 
[   18.822749] c0 n = 10
[   18.822750] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822754] c0 Skip verify chain_partition[l_gdsp] while load verify pm_sys partition... 
[   18.822757] c0 n = 11
[   18.822759] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.822762] c0 chain partition founded: n = 12
[   18.822766] c0 [kbc]read partition name: pm_sys, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   18.822770] c0 [kbc]read footer
[   18.822771] c0 dump footer
[   18.822774] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   18.822789] c0 0010 00 0b a0 00 00 00 00 00 00 0b a0 00 00 00 00 00
[   18.822803] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   18.822818] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.822834] c0 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   18.822837] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: pm_sys, offset = 0xba000, num_bytes = 0x840
[   18.822843] c0 [kbc]read certificate: img_addr = 0xe662e000
[   18.822846] c0 [kbc]read certificate: offset = 0xba000
[   18.822849] c0 dump certificate
[   18.822851] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   18.822866] c0 [kbc]read_from_partition_kbc leave buf = 0xe07d3b68
[   18.841809] c0 partition: pm_sys vbmeta_verify_ret is :0. 
[   18.841826] c0 enter implement read_rollback_index().
[   18.841828] c0 read_is_device_unlocked() rollback_index_slot = 14 
[   18.841832] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   18.841836] c0 Info: g_sprd_vboot_version.img_ver[14]= 0
[   18.841844] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   18.841848] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   18.841856] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   18.841858] c0 check dat cp
[   18.847106] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   18.847118] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   18.847123] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   18.847129] c0 Skip verify chain_partition[l_agdsp] while load verify pm_sys partition... 
[   18.847133] c0 n = 13
[   18.847135] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.847138] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.847142] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.847146] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.847149] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.847153] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   18.847158] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   18.847163] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   18.847174] c0 read_is_device_unlocked() ret = 0 
[   18.847718] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   18.847725] c0 partition: vbmeta  guid_buf_size = 37 
[   18.847728] c0 guid: 1.0 
[   18.847735] c0 in avb_slot_verify, l:1626. 
[   18.847739] c0 in avb_slot_verify, l:1640. 
[   18.847742] c0 [kbc]avb_slot_verify result is 0.
[   18.847744] c0 [kbc]pm_sys_avb_slot_verify result is OK.
[   18.847747] c0 [kbc]slot_data[0] is 0xe07c3050.
[   18.847750] c0 [kbc]slot_data[1] is 0x0.
[   18.847755] c0 [kbc]ret = 0
[   18.847789] c0 [kbc]verify success. 
[   18.847791] c0 version:
[   18.847793] c0 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847809] c0 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847824] c0 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847839] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847853] c0 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847868] c0 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847883] c0 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847898] c0 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   18.847912] c0 0080 00 00 00 00 00 00 00 00
[   18.847921] c0 kbc_v2_dump_version 
[   18.847923] c0 imgver[0] = 0x0 
[   18.847925] c0 imgver[1] = 0x0 
[   18.847927] c0 imgver[2] = 0x0 
[   18.847930] c0 imgver[3] = 0x0 
[   18.847932] c0 imgver[4] = 0x0 
[   18.847934] c0 imgver[5] = 0x0 
[   18.847936] c0 imgver[6] = 0x0 
[   18.847938] c0 imgver[7] = 0x0 
[   18.847940] c0 imgver[8] = 0x0 
[   18.847943] c0 imgver[9] = 0x0 
[   18.847945] c0 imgver[10] = 0x0 
[   18.847947] c0 imgver[11] = 0x0 
[   18.847949] c0 imgver[12] = 0x0 
[   18.847951] c0 imgver[13] = 0x0 
[   18.847954] c0 imgver[14] = 0x0 
[   18.847956] c0 imgver[15] = 0x0 
[   18.847958] c0 imgver[16] = 0x0 
[   18.847960] c0 imgver[17] = 0x0 
[   18.847962] c0 imgver[18] = 0x0 
[   18.847965] c0 imgver[19] = 0x0 
[   18.847967] c0 imgver[20] = 0x0 
[   18.847969] c0 imgver[21] = 0x0 
[   18.847972] c0 imgver[22] = 0x0 
[   18.847974] c0 imgver[23] = 0x0 
[   18.847976] c0 imgver[24] = 0x0 
[   18.847978] c0 imgver[25] = 0x0 
[   18.847980] c0 imgver[26] = 0x0 
[   18.847983] c0 imgver[27] = 0x0 
[   18.847985] c0 imgver[28] = 0x0 
[   18.847987] c0 imgver[29] = 0x0 
[   18.847989] c0 imgver[30] = 0x0 
[   18.847991] c0 imgver[31] = 0x0 
<   18.848001> trusty_kernelbootcp: 185: TA:kbc_verify_all_avb2() ret = 0
[   18.848159] c0 enter SEC_KBC_GET_VERSION
[   18.848164] c0 reset update version flag
<   18.848166> version
<   18.848172> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848181> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848189> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848197> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848204> 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848212> 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848220> 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848228> 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   18.848235> 0080 00 00 00 00 00 00 00 00
<   18.848242> trusty_kernelbootcp: 193: TA:update version flag = 0
[   18.848249] c0 enter SEC_KBC_START_CP
[   18.848254] c0 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[   18.848257] c0 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[   18.848261] c0 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[   18.848265] c0 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[   18.848268] c0 dump_table() flag = 31 
[   18.848271] c0 dump_table() is_packed = 0 
[   18.848273] c0 kbc_start_cp() enter MODEM_IMG
[   18.848275] c0 reg_addr = 0xffffffffe61cc330
[   18.848282] c0 before reg = 2010101
[   18.848287] c0 after  reg = 10101
[   18.848289] c0 sleep 50ms start 
[   18.856432] c4 tam_load_request:1513: load look up com.android.trusty.faceid
[   18.856445] c4 handle_conn_req:412: failed (-2) to send response
[   18.862529] c4 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   18.862543] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   18.862570] c4 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   18.862573] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   18.862577] c4 ta_manager_verify_img:447: RSA_hash
[   18.863509] c0 ta_manager_verify_img:506: RSA_verify
[   18.865459] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   18.865471] c0 trusty_app:(32) start 0xffffffffe07c4000 size 0x00015000
[   18.865482] c0 trusty_app: whitelist.table 0x0, size: 0
[   18.865486] c0 trusty_app 16 uuid: 0xf4bc36e6 0x8ec2 0x46e2 0xa82e 0xf7cb6cdc6f72
[   18.865495] c0 trusty_app 0xffffffffe07d9178: stack_sz=0x2000
[   18.865498] c0 trusty_app 0xffffffffe07d9178: heap_sz=0x6000
[   18.865502] c0 trusty_app 0xffffffffe07d9178: one_shot=0x0
[   18.865505] c0 trusty_app 0xffffffffe07d9178: keep_alive=0x0
[   18.865508] c0 trusty_app 0xffffffffe07d9178: flags=0x1c
[   18.865512] c0 ta_manager_write_ta:985: enter tam anti rollback
[   18.865519] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   18.865524] c0 ta_manager_write_ta:997: tam anti rollback ok
[   18.865527] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   18.865532] c0 trusty_tapp_init:
[   18.865736] c0 trusty_app 16: code: start 0x00008000 end 0x000189cc
[   18.865742] c0 trusty_app 16: data: start 0x00019000 end 0x0001a000
[   18.865747] c0 trusty_app 16: bss:                end 0x0001941c
[   18.865751] c0 trusty_app 16: brk:  start 0x0001a000 end 0x00020000
[   18.865755] c0 trusty_app 16: entry 0x0000b430
[   18.865795] c0 tam_port_publish:1496: publish port com.android.trusty.faceid
[   18.865810] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.faceid accomplished!
[   18.898290] c0 sleep end 
[   18.898298] c0 reg_addr = 0xffffffffe61cc818
[   18.898306] c0 before reg = 0006
[   18.898313] c0 after  reg = 0004
[   18.898315] c0 sleep 50ms start 
[   18.960136] c0 sleep end 
[   18.960144] c0 reg_addr = 0xffffffffe61ccb98
[   18.960151] c0 before reg = 4800
[   18.960157] c0 after  reg = 4000
[   18.960159] c0 sleep 50ms start 
[   19.011192] c0 sleep end 
[   19.011200] c0 reg_addr = 0xffffffffe61dc174
[   19.011207] c0 before reg = 0400
[   19.011214] c0 after  reg = 0000
[   19.011217] c0 sleep 50ms start 
<   19.041507> E focaltech:ta:device: error at ff_device_probe[device.c:102]: failed to probe focaltech fingerprint device.
<   19.041526> E focaltech:ta:device: error at ff_device_probe[device.c:103]: 'Device not found'.
[   19.061135] c0 sleep end 
[   19.061143] c0 reg_addr = 0xffffffffe61dc08c
[   19.061151] c0 before reg = 0001
[   19.061157] c0 after  reg = 0000
[   19.061159] c0 sleep 50ms start SP_IMG
[   19.074252] c6 tam_disc_notify:1554: port =  com.android.trusty.focalfp
[   19.074270] c6 ipc_port_kill:1034: kill (com.android.trusty.focalfp) 's TA 
<   19.074321> E focaltech:ta:entry: error at focalfp_chan_handler[trustlet_entry.c:353]: closed by peer.
<   19.074335> E focaltech:ta:entry: error at main[trustlet_entry.c:532]: wait_any(..) = -1.
[   19.074417] c6 exit called, thread 0xffffffffe076c8a8, name trusty_tapp_2_4c2cd891-219d-43d
[   19.074422] c6 tam_thread_exit_notify:1422: tam_thread_exit_notify, id:15
[   19.074427] c6 tam_unload_request:1373:  com.android.trusty.focalfp, id: 15
[   19.074433] c6 ta_manager_unload_thread_fun:1351: app id 15 exit,tam going to unloader app ......
[   19.074438] c6 ta_manager_unload_thread_fun:1358: will free trusty_app path com.android.trusty.focalfp
[   19.111296] c0 sleep end 
[   19.111304] c0 kbc_start_cp() leave 
<   19.111312> trusty_kernelbootcp: 199: TA:SEC_KBC_START_CP() ret = 0
<   19.111327> trusty_kernelbootcp: 202: TA:SEC_PREPARE_FIREWALL_DATA() ret = 0
<   19.111342> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
<   20.047498> ss: block_device_tipc_prodnv_init: prodnv device's block size 4040 block count 1048576
<   20.058710> fs_init_from_super:603: super block: files at 0 free at 2
<   20.058745> fs_init_from_super:605: probe super block's files block [0] failed, fs is corrupted.
<   20.059462> fs_init_from_super:634: loaded super block version 2
<   20.059501> ss: block_device_tipc_prodnv_init: create port com.android.trusty.storage.client.tn success
<  104.044188> storage_client: 316: storage_open_file: invalid  rc = -2 
<  104.044213> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<  104.044459> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  104.044475> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 
<  104.059703> storage_client: 316: storage_open_file: invalid  rc = -2 
<  104.059727> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<  104.059985> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  104.060000> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 

trusty_keymasterapp/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  143.614600> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  143.614614> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  143.614832> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  143.614842> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  143.615300> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  143.615311> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  143.615328> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  143.615336> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  143.715093> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  143.715105> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  143.715248> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  143.715255> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  143.715588> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  143.715595> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  143.715605> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  143.715611> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  145.156557> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  145.156571> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  145.156806> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  145.156816> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  145.157277> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  145.157287> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  145.157305> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  145.157312> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  145.278661> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  145.278671> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  145.278809> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  145.278816> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  145.279115> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  145.279121> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  145.279131> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  145.279136> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  151.434908> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  151.434922> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  151.435160> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  151.435170> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  151.435632> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  151.435643> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  151.435660> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  151.435668> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  151.613595> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  151.613609> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  151.613840> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  151.613849> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  151.614293> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  151.614303> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  151.614320> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  151.614328> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  158.935959> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  158.935972> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  158.936395> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  158.936408> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  158.936993> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  158.937006> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  158.937031> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  158.937039> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  159.158753> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  159.158767> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  159.158999> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  159.159008> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  159.159460> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  159.159470> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  159.159487> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  159.159495> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  166.626000> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  166.626014> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  166.626249> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  166.626259> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  166.626705> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  166.626716> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  166.626734> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  166.626741> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  166.690025> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  166.690036> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  166.690177> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  166.690184> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  166.690502> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  166.690508> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  166.690518> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  166.690524> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  175.956592> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  175.956606> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  175.956838> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  175.956847> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  175.957302> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  175.957313> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  175.957331> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  175.957339> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  176.051750> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  176.051764> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  176.052277> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  176.052292> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  176.052972> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  176.053318> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  176.053362> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  176.053370> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  186.114665> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  186.114676> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  186.114815> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  186.114822> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  186.115121> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  186.115128> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  186.115137> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  186.115142> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  186.213219> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  186.213232> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  186.213651> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  186.213664> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  186.214134> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  186.214145> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  186.214168> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  186.214175> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  195.073661> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  195.073673> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  195.073905> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  195.073916> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  195.074356> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  195.074365> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  195.074379> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  195.074386> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  195.139593> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  195.139606> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  195.139841> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  195.139850> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  195.140523> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  195.140538> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  195.140566> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  195.140573> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  203.260722> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  203.260738> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  203.262199> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  203.262216> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  203.263355> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  203.263371> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  203.263403> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  203.263411> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  203.350458> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  203.350473> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  203.350711> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  203.350721> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  203.351189> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  203.351201> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  203.351222> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  203.351229> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  204.392646> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  204.392661> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  204.392892> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  204.392902> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  204.393356> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  204.393367> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  204.393383> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  204.393391> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  204.522418> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  204.522433> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  204.522672> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  204.522681> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  204.523160> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  204.523171> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  204.523190> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  204.523198> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  214.262208> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  214.262222> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  214.262461> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  214.262470> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  214.262936> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  214.262947> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  214.262963> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  214.262970> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  214.385495> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  214.385509> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  214.385747> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  214.385756> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  214.386231> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  214.386242> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  214.386260> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  214.386268> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  216.911436> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  216.911447> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  216.911587> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  216.911594> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  216.911909> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  216.911916> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  216.911926> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  216.911932> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  217.017317> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  217.017331> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  217.017575> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  217.017584> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  217.018043> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  217.018054> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  217.018072> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  217.018079> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  220.406728> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  220.406744> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  220.407146> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  220.407159> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  220.407644> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  220.407655> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  220.407673> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  220.407681> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  220.489554> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  220.489568> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  220.489803> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  220.489812> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  220.490268> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  220.490278> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  220.490296> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  220.490303> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  233.385850> trusty_gatekeeper_ta: 135: handle_request  GK_DELETE_ALL_USERS
<  233.385866> trusty_gatekeeper_ta: 132: DeriveMasterKey
<  233.385911> trusty_gatekeeper_ta: 134: DeriveMasterKey open rc:1002
<  233.385929> libhwkey: 148: data compare failed
<  233.386185> trusty_gatekeeper_ta: 158: Master[31]:62  Master[0]:252
<  233.386198> trusty_gatekeeper_ta: 228: DeleteAllUsers >>>>>>>>>>
<  233.386333> trusty_gatekeeper_ta: 232: DeleteAllUsers <<<<<<<<<<<
<  233.386560> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  233.386573> ss: disconnect whith 38ba0cdcdf0e11e49869233fb6ae4795 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  241.074484> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  241.074499> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  241.074731> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  241.074740> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  241.075207> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  241.075217> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  241.075233> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  241.075241> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  241.247069> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  241.247084> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  241.247319> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  241.247328> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  241.247815> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  241.247824> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  241.247840> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  241.247848> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
[  248.685905] c4 tam_load_request:1513: load look up com.android.trusty.widevine
[  248.685919] c4 handle_conn_req:412: failed (-2) to send response
[  248.735584] c5 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[  248.735599] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[  248.756232] c4 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[  248.756246] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[  248.756251] c4 ta_manager_verify_img:447: RSA_hash
[  248.770090] c0 ta_manager_verify_img:506: RSA_verify
[  248.772113] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[  248.772125] c0 trusty_app:(32) start 0xffffffffe094c000 size 0x001d4000
[  248.772346] c0 trusty_app: whitelist.table 0x0, size: 0
[  248.772349] c0 trusty_app 17 uuid: 0x81af0b44 0x41f0 0x11e7 0xa919 0x92ebcb67fe33
[  248.772357] c0 trusty_app 0xffffffffe076ca28: stack_sz=0x10000
[  248.772360] c0 trusty_app 0xffffffffe076ca28: heap_sz=0x800000
[  248.772364] c0 trusty_app 0xffffffffe076ca28: one_shot=0x0
[  248.772367] c0 trusty_app 0xffffffffe076ca28: keep_alive=0x1
[  248.772370] c0 trusty_app 0xffffffffe076ca28: flags=0x1c
[  248.772373] c0 ta_manager_write_ta:985: enter tam anti rollback
[  248.772380] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[  248.772384] c0 ta_manager_write_ta:997: tam anti rollback ok
[  248.772387] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[  248.772391] c0 trusty_tapp_init:
[  248.774015] c0 trusty_app 17: code: start 0x00008000 end 0x000a7ed4
[  248.774022] c0 trusty_app 17: data: start 0x000a8000 end 0x001d9000
[  248.774026] c0 trusty_app 17: bss:                end 0x001d8624
[  248.774030] c0 trusty_app 17: brk:  start 0x001d9000 end 0x009d9000
[  248.774034] c0 trusty_app 17: entry 0x0002f09c
[  248.774089] c0 tam_port_publish:1501:  other port com.android.trusty.sec.widevine
[  248.774110] c0 tam_port_publish:1496: publish port com.android.trusty.widevine
[  248.774123] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.widevine accomplished!
<  248.789063> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<  248.789323> ss-ipc: 185: do_disconnect ev->handle ox3f6
<  248.789338> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<  248.820412> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  248.820428> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<  248.820443> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  248.820450> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
[  248.827060] c6 tam_disc_notify:1554: port =  com.android.trusty.widevine
[  248.827069] c6 tam_disc_notify:1572: Keep alive TA will not disconnect.
<  248.945531> trusty_unisoc_oemcrypto [E]: [WTPI_LoadRootOfTrust]161: widevine.ReadWidevineDeviceID failed len=0
<  248.945717> ss-ipc: 185: do_disconnect ev->handle ox3f6
<  248.945727> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<  248.962493> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  248.962516> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
<  248.962532> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  248.962541> ss: disconnect whith 81af0b4441f011e7a91992ebcb67fe33 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  249.785342> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  249.785357> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  249.785596> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  249.785606> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  249.787879> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  249.787898> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  249.787937> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  249.787945> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  249.883075> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  249.883090> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  249.883340> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  249.883350> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  249.883953> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  249.883966> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  249.883992> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  249.884000> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  261.133010> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.133023> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  261.133258> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.133267> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  261.133731> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.133742> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  261.133759> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  261.133766> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  261.244261> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.244273> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  261.244417> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.244424> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  261.244741> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.244748> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  261.244758> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  261.244764> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  261.393397> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.393410> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  261.393640> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.393650> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  261.394096> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.394107> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  261.394123> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  261.394131> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  261.514321> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.514331> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  261.514472> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.514479> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  261.514796> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.514803> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  261.514812> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  261.514818> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  261.644862> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.644873> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  261.645016> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.645023> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  261.645342> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  261.645350> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  261.645360> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  261.645366> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  283.836470> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  283.836481> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  283.836620> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  283.836626> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  283.836922> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  283.836929> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  283.836939> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  283.836945> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  283.959834> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  283.959845> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  283.959985> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  283.959992> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  283.960447> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  283.960456> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  283.960471> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  283.960477> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  304.853999> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  304.854012> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  304.854174> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  304.854182> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  304.854557> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  304.854564> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  304.854576> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  304.854583> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 125: Before storage_open_session_async
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<  304.925876> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  304.925889> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  304.926049> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  304.926057> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 333: Failed to read attestation chain quantity from keybox.
<  304.926411> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  304.926419> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
<  304.926430> ss-ipc: 185: do_disconnect ev->handle ox3f3
<  304.926437> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
