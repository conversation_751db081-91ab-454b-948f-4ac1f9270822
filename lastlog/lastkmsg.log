

(null)
t: Untracked pid 12325 received signal 15
<14>[ 1157.839053][ T1@C3] init: Untracked pid 12325 did not have an associated service entry and will not be reaped
<14>[ 1157.849704][ T1@C1] init: Untracked pid 2562 received signal 15
<14>[ 1157.849787][ T1@C1] init: Untracked pid 2562 did not have an associated service entry and will not be reaped
<14>[ 1157.890308][ T1@C4] init: Untracked pid 12257 received signal 15
<14>[ 1157.890385][ T1@C4] init: Untracked pid 12257 did not have an associated service entry and will not be reaped
<4>[ 1157.988312][T3515@C4] preemptoff_warn: C4 T:<3515>DFacilitator-1 D:50.952ms F:1157.937351s
<4>[ 1157.988426][T3515@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1157.988426][T3515@C4] trace_preempt_off+0x74/0x7c
<4>[ 1157.988426][T3515@C4] preempt_count_add+0xe4/0x108
<4>[ 1157.988426][T3515@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1157.988426][T3515@C4] zap_pte_range+0x100/0x588
<4>[ 1157.988426][T3515@C4] unmap_page_range+0x194/0x27c
<4>[ 1157.988454][T3515@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1157.988454][T3515@C4] trace_preempt_on+0x74/0x7c
<4>[ 1157.988454][T3515@C4] preempt_count_sub+0xbc/0xec
<4>[ 1157.988454][T3515@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1157.988454][T3515@C4] zap_pte_range+0x4f8/0x588
<4>[ 1157.988454][T3515@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.028780][T3515@C4] preemptoff_warn: C4 T:<3515>DFacilitator-1 D:40.302ms F:1157.988471s
<4>[ 1158.028989][T3515@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1158.028989][T3515@C4] trace_preempt_off+0x74/0x7c
<4>[ 1158.028989][T3515@C4] preempt_count_add+0xe4/0x108
<4>[ 1158.028989][T3515@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.028989][T3515@C4] zap_pte_range+0x100/0x588
<4>[ 1158.028989][T3515@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.029017][T3515@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1158.029017][T3515@C4] trace_preempt_on+0x74/0x7c
<4>[ 1158.029017][T3515@C4] preempt_count_sub+0xbc/0xec
<4>[ 1158.029017][T3515@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.029017][T3515@C4] zap_pte_range+0x4f8/0x588
<4>[ 1158.029017][T3515@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.044384][T10003@C2] preemptoff_warn: C2 T:<10003>GmsDynamite D:42.707ms F:1158.001672s
<4>[ 1158.044479][T10003@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1158.044479][T10003@C2] trace_preempt_off+0x74/0x7c
<4>[ 1158.044479][T10003@C2] preempt_count_add+0xe4/0x108
<4>[ 1158.044479][T10003@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.044479][T10003@C2] zap_pte_range+0x100/0x588
<4>[ 1158.044479][T10003@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.044507][T10003@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1158.044507][T10003@C2] trace_preempt_on+0x74/0x7c
<4>[ 1158.044507][T10003@C2] preempt_count_sub+0xbc/0xec
<4>[ 1158.044507][T10003@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.044507][T10003@C2] zap_pte_range+0x4f8/0x588
<4>[ 1158.044507][T10003@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.075232][T3515@C4] preemptoff_warn: C4 T:<3515>DFacilitator-1 D:32.862ms F:1158.042360s
<4>[ 1158.075345][T3515@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1158.075345][T3515@C4] trace_preempt_off+0x74/0x7c
<4>[ 1158.075345][T3515@C4] preempt_count_add+0xe4/0x108
<4>[ 1158.075345][T3515@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.075345][T3515@C4] zap_pte_range+0x100/0x588
<4>[ 1158.075345][T3515@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.075374][T3515@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1158.075374][T3515@C4] trace_preempt_on+0x74/0x7c
<4>[ 1158.075374][T3515@C4] preempt_count_sub+0xbc/0xec
<4>[ 1158.075374][T3515@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.075374][T3515@C4] zap_pte_range+0x4f8/0x588
<4>[ 1158.075374][T3515@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.076472][T10003@C2] preemptoff_warn: C2 T:<10003>GmsDynamite D:31.336ms F:1158.045133s
<4>[ 1158.076520][T10003@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1158.076520][T10003@C2] trace_preempt_off+0x74/0x7c
<4>[ 1158.076520][T10003@C2] preempt_count_add+0xe4/0x108
<4>[ 1158.076520][T10003@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.076520][T10003@C2] zap_pte_range+0x100/0x588
<4>[ 1158.076520][T10003@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.076547][T10003@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1158.076547][T10003@C2] trace_preempt_on+0x74/0x7c
<4>[ 1158.076547][T10003@C2] preempt_count_sub+0xbc/0xec
<4>[ 1158.076547][T10003@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.076547][T10003@C2] zap_pte_range+0x4f8/0x588
<4>[ 1158.076547][T10003@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.140526][T10003@C2] preemptoff_warn: C2 T:<10003>GmsDynamite D:51.834ms F:1158.088686s
<4>[ 1158.140636][T10003@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1158.140636][T10003@C2] trace_preempt_off+0x74/0x7c
<4>[ 1158.140636][T10003@C2] preempt_count_add+0xe4/0x108
<4>[ 1158.140636][T10003@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.140636][T10003@C2] zap_pte_range+0x100/0x588
<4>[ 1158.140636][T10003@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.140665][T10003@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1158.140665][T10003@C2] trace_preempt_on+0x74/0x7c
<4>[ 1158.140665][T10003@C2] preempt_count_sub+0xbc/0xec
<4>[ 1158.140665][T10003@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.140665][T10003@C2] zap_pte_range+0x4f8/0x588
<4>[ 1158.140665][T10003@C2] unmap_page_range+0x194/0x27c
<14>[ 1158.219633][ T1@C5] init: Untracked pid 12538 received signal 15
<14>[ 1158.219718][ T1@C5] init: Untracked pid 12538 did not have an associated service entry and will not be reaped
<14>[ 1158.272207][ T1@C0] init: Untracked pid 11154 received signal 15
<14>[ 1158.272293][ T1@C0] init: Untracked pid 11154 did not have an associated service entry and will not be reaped
<6>[ 1158.289798][T12082@C4] binder: undelivered death notification, b400007a6f757160
<14>[ 1158.289890][ T1@C5] init: Service 'media.swcodec' (pid 953) received signal 15
<14>[ 1158.289946][ T1@C5] init: Sending signal 9 to service 'media.swcodec' (pid 953) process group...
<14>[ 1158.291477][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1046/pid_953
<14>[ 1158.295917][ T1@C5] init: Untracked pid 2988 received signal 15
<14>[ 1158.296531][ T1@C5] init: Untracked pid 2988 did not have an associated service entry and will not be reaped
<14>[ 1158.323262][ T1@C0] init: Untracked pid 11282 received signal 15
<14>[ 1158.323333][ T1@C0] init: Untracked pid 11282 did not have an associated service entry and will not be reaped
<6>[ 1158.325843][T11510@C5] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1158.366911][ T1@C2] init: Untracked pid 3382 received signal 15
<14>[ 1158.366982][ T1@C2] init: Untracked pid 3382 did not have an associated service entry and will not be reaped
<4>[ 1158.370762][T10003@C3] preemptoff_warn: C3 T:<10003>GmsDynamite D:42.239ms F:1158.328517s
<4>[ 1158.370852][T10003@C3] preemptoff_warn: C3 disabled preempt at:
<4>[ 1158.370852][T10003@C3] trace_preempt_off+0x74/0x7c
<4>[ 1158.370852][T10003@C3] preempt_count_add+0xe4/0x108
<4>[ 1158.370852][T10003@C3] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.370852][T10003@C3] zap_pte_range+0x100/0x588
<4>[ 1158.370852][T10003@C3] unmap_page_range+0x194/0x27c
<4>[ 1158.370879][T10003@C3] preemptoff_warn: C3 enabled preempt at:
<4>[ 1158.370879][T10003@C3] trace_preempt_on+0x74/0x7c
<4>[ 1158.370879][T10003@C3] preempt_count_sub+0xbc/0xec
<4>[ 1158.370879][T10003@C3] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.370879][T10003@C3] zap_pte_range+0x4f8/0x588
<4>[ 1158.370879][T10003@C3] unmap_page_range+0x194/0x27c
<14>[ 1158.376320][ T1@C4] init: Untracked pid 2029 received signal 15
<14>[ 1158.376376][ T1@C4] init: Untracked pid 2029 did not have an associated service entry and will not be reaped
<14>[ 1158.407949][ T1@C4] init: Untracked pid 3077 received signal 15
<14>[ 1158.408033][ T1@C4] init: Untracked pid 3077 did not have an associated service entry and will not be reaped
<14>[ 1158.419333][ T1@C0] init: Service 'update_engine' (pid 968) exited with status 0
<14>[ 1158.419403][ T1@C0] init: Sending signal 9 to service 'update_engine' (pid 968) process group...
<14>[ 1158.420814][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_968
<6>[ 1158.482582][T12082@C4] binder: release 12438:12438 transaction 456102 out, still active
<6>[ 1158.482663][T12082@C4] binder: undelivered TRANSACTION_COMPLETE
<6>[ 1158.482837][T12082@C4] binder: release 12438:12511 transaction 456022 out, still active
<6>[ 1158.482864][T12082@C4] binder: undelivered TRANSACTION_COMPLETE
<14>[ 1158.491987][ T1@C0] init: Untracked pid 12438 received signal 15
<14>[ 1158.492077][ T1@C0] init: Untracked pid 12438 did not have an associated service entry and will not be reaped
<14>[ 1158.527816][ T1@C0] init: Untracked pid 10836 received signal 15
<14>[ 1158.527899][ T1@C0] init: Untracked pid 10836 did not have an associated service entry and will not be reaped
<14>[ 1158.530451][ T1@C0] init: Untracked pid 2022 received signal 15
<14>[ 1158.530523][ T1@C0] init: Untracked pid 2022 did not have an associated service entry and will not be reaped
<14>[ 1158.553785][ T1@C4] init: Untracked pid 12364 received signal 15
<14>[ 1158.553867][ T1@C4] init: Untracked pid 12364 did not have an associated service entry and will not be reaped
<14>[ 1158.596751][ T1@C4] init: Untracked pid 12386 received signal 15
<14>[ 1158.596836][ T1@C4] init: Untracked pid 12386 did not have an associated service entry and will not be reaped
<4>[ 1158.641317][T3208@C4] preemptoff_warn: C4 T:<3208>Profile Saver D:31.517ms F:1158.609792s
<4>[ 1158.641441][T3208@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1158.641441][T3208@C4] trace_preempt_off+0x74/0x7c
<4>[ 1158.641441][T3208@C4] preempt_count_add+0xe4/0x108
<4>[ 1158.641441][T3208@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.641441][T3208@C4] zap_pte_range+0x100/0x588
<4>[ 1158.641441][T3208@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.641471][T3208@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1158.641471][T3208@C4] trace_preempt_on+0x74/0x7c
<4>[ 1158.641471][T3208@C4] preempt_count_sub+0xbc/0xec
<4>[ 1158.641471][T3208@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.641471][T3208@C4] zap_pte_range+0x4f8/0x588
<4>[ 1158.641471][T3208@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.696993][T4383@C2] preemptoff_warn: C2 T:<4383>Firebase Backgr D:35.588ms F:1158.661398s
<4>[ 1158.697106][T4383@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1158.697106][T4383@C2] trace_preempt_off+0x74/0x7c
<4>[ 1158.697106][T4383@C2] preempt_count_add+0xe4/0x108
<4>[ 1158.697106][T4383@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.697106][T4383@C2] zap_pte_range+0x100/0x588
<4>[ 1158.697106][T4383@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.697135][T4383@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1158.697135][T4383@C2] trace_preempt_on+0x74/0x7c
<4>[ 1158.697135][T4383@C2] preempt_count_sub+0xbc/0xec
<4>[ 1158.697135][T4383@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.697135][T4383@C2] zap_pte_range+0x4f8/0x588
<4>[ 1158.697135][T4383@C2] unmap_page_range+0x194/0x27c
<4>[ 1158.702493][T3208@C4] preemptoff_warn: C4 T:<3208>Profile Saver D:49.452ms F:1158.653033s
<4>[ 1158.702590][T3208@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1158.702590][T3208@C4] trace_preempt_off+0x74/0x7c
<4>[ 1158.702590][T3208@C4] preempt_count_add+0xe4/0x108
<4>[ 1158.702590][T3208@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.702590][T3208@C4] zap_pte_range+0x100/0x588
<4>[ 1158.702590][T3208@C4] unmap_page_range+0x194/0x27c
<4>[ 1158.702619][T3208@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1158.702619][T3208@C4] trace_preempt_on+0x74/0x7c
<4>[ 1158.702619][T3208@C4] preempt_count_sub+0xbc/0xec
<4>[ 1158.702619][T3208@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.702619][T3208@C4] zap_pte_range+0x4f8/0x588
<4>[ 1158.702619][T3208@C4] unmap_page_range+0x194/0x27c
<14>[ 1158.709688][ T1@C1] init: Untracked pid 9561 received signal 15
<14>[ 1158.709762][ T1@C1] init: Untracked pid 9561 did not have an associated service entry and will not be reaped
<4>[ 1158.833199][T3515@C7] preemptoff_warn: C7 T:<3515>DFacilitator-1 D:32.671ms F:1158.800521s
<4>[ 1158.833288][T3515@C7] preemptoff_warn: C7 disabled preempt at:
<4>[ 1158.833288][T3515@C7] trace_preempt_off+0x74/0x7c
<4>[ 1158.833288][T3515@C7] preempt_count_add+0xe4/0x108
<4>[ 1158.833288][T3515@C7] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.833288][T3515@C7] zap_pte_range+0x100/0x588
<4>[ 1158.833288][T3515@C7] unmap_page_range+0x194/0x27c
<4>[ 1158.833308][T3515@C7] preemptoff_warn: C7 enabled preempt at:
<4>[ 1158.833308][T3515@C7] trace_preempt_on+0x74/0x7c
<4>[ 1158.833308][T3515@C7] preempt_count_sub+0xbc/0xec
<4>[ 1158.833308][T3515@C7] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.833308][T3515@C7] zap_pte_range+0x4f8/0x588
<4>[ 1158.833308][T3515@C7] unmap_page_range+0x194/0x27c
<14>[ 1158.840154][ T1@C3] init: Untracked pid 9764 received signal 15
<14>[ 1158.840208][ T1@C3] init: Untracked pid 9764 did not have an associated service entry and will not be reaped
<6>[ 1158.876289][T604@C4] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1158.876365][T604@C4] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1158.876396][T604@C4] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1158.876496][T604@C4] [Audio:DSPDUMP] audio_dsp_release
<14>[ 1158.876501][ T1@C2] init: Untracked pid 11160 received signal 15
<14>[ 1158.876554][ T1@C2] init: Untracked pid 11160 did not have an associated service entry and will not be reaped
<6>[ 1158.881399][T10497@C2] binder: undelivered death notification, b400007a6f758080
<14>[ 1158.894253][ T1@C2] init: Service 'vendor.audio-hal' (pid 604) received signal 15
<14>[ 1158.894329][ T1@C2] init: Sending signal 9 to service 'vendor.audio-hal' (pid 604) process group...
<14>[ 1158.895820][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_604
<14>[ 1158.899287][ T1@C2] init: Untracked pid 3065 received signal 15
<14>[ 1158.899340][ T1@C2] init: Untracked pid 3065 did not have an associated service entry and will not be reaped
<4>[ 1158.905811][T10003@C1] preemptoff_warn: C1 T:<10003>GmsDynamite D:39.210ms F:1158.866595s
<4>[ 1158.905899][T10003@C1] preemptoff_warn: C1 disabled preempt at:
<4>[ 1158.905899][T10003@C1] trace_preempt_off+0x74/0x7c
<4>[ 1158.905899][T10003@C1] preempt_count_add+0xe4/0x108
<4>[ 1158.905899][T10003@C1] _raw_spin_lock+0x2c/0x9c
<4>[ 1158.905899][T10003@C1] zap_pte_range+0x100/0x588
<4>[ 1158.905899][T10003@C1] unmap_page_range+0x194/0x27c
<4>[ 1158.905928][T10003@C1] preemptoff_warn: C1 enabled preempt at:
<4>[ 1158.905928][T10003@C1] trace_preempt_on+0x74/0x7c
<4>[ 1158.905928][T10003@C1] preempt_count_sub+0xbc/0xec
<4>[ 1158.905928][T10003@C1] _raw_spin_unlock+0x2c/0x54
<4>[ 1158.905928][T10003@C1] zap_pte_range+0x4f8/0x588
<4>[ 1158.905928][T10003@C1] unmap_page_range+0x194/0x27c
<6>[ 1159.074724][T3515@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<4>[ 1159.096748][T10467@C7] preemptoff_warn: C7 T:<10467>Jit thread pool D:55.173ms F:1159.041570s
<4>[ 1159.096836][T10467@C7] preemptoff_warn: C7 disabled preempt at:
<4>[ 1159.096836][T10467@C7] trace_preempt_off+0x74/0x7c
<4>[ 1159.096836][T10467@C7] preempt_count_add+0xe4/0x108
<4>[ 1159.096836][T10467@C7] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.096836][T10467@C7] zap_pte_range+0x100/0x588
<4>[ 1159.096836][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.096855][T10467@C7] preemptoff_warn: C7 enabled preempt at:
<4>[ 1159.096855][T10467@C7] trace_preempt_on+0x74/0x7c
<4>[ 1159.096855][T10467@C7] preempt_count_sub+0xbc/0xec
<4>[ 1159.096855][T10467@C7] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.096855][T10467@C7] zap_pte_range+0x4f8/0x588
<4>[ 1159.096855][T10467@C7] unmap_page_range+0x194/0x27c
<14>[ 1159.147745][ T1@C2] init: Untracked pid 11223 received signal 15
<14>[ 1159.147835][ T1@C2] init: Untracked pid 11223 did not have an associated service entry and will not be reaped
<14>[ 1159.162213][ T1@C2] init: Untracked pid 4302 received signal 15
<14>[ 1159.162301][ T1@C2] init: Untracked pid 4302 did not have an associated service entry and will not be reaped
<14>[ 1159.174347][ T1@C2] init: Untracked pid 2807 received signal 15
<14>[ 1159.174437][ T1@C2] init: Untracked pid 2807 did not have an associated service entry and will not be reaped
<4>[ 1159.197383][T10467@C7] preemptoff_warn: C7 T:<10467>Jit thread pool D:47.355ms F:1159.150020s
<4>[ 1159.197474][T10467@C7] preemptoff_warn: C7 disabled preempt at:
<4>[ 1159.197474][T10467@C7] trace_preempt_off+0x74/0x7c
<4>[ 1159.197474][T10467@C7] preempt_count_add+0xe4/0x108
<4>[ 1159.197474][T10467@C7] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.197474][T10467@C7] zap_pte_range+0x100/0x588
<4>[ 1159.197474][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.197493][T10467@C7] preemptoff_warn: C7 enabled preempt at:
<4>[ 1159.197493][T10467@C7] trace_preempt_on+0x74/0x7c
<4>[ 1159.197493][T10467@C7] preempt_count_sub+0xbc/0xec
<4>[ 1159.197493][T10467@C7] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.197493][T10467@C7] zap_pte_range+0x4f8/0x588
<4>[ 1159.197493][T10467@C7] unmap_page_range+0x194/0x27c
<14>[ 1159.233452][ T1@C2] init: Waiting for 103 pids to be reaped took 3000ms with 2 of them still running
<14>[ 1159.271830][ T1@C2] init: Still running: 717
<14>[ 1159.271889][ T1@C2] init: Name:	surfaceflinger
<14>[ 1159.271918][ T1@C2] init: State:	Z (zombie)
<14>[ 1159.271944][ T1@C2] init: Tgid:	717
<14>[ 1159.271973][ T1@C2] init: Ngid:	0
<14>[ 1159.271999][ T1@C2] init: Pid:	717
<14>[ 1159.272025][ T1@C2] init: PPid:	1
<14>[ 1159.272052][ T1@C2] init: TracerPid:	0
<14>[ 1159.272078][ T1@C2] init: Uid:	1000	1000	1000	1000
<14>[ 1159.272104][ T1@C2] init: Gid:	1003	1003	1003	1003
<14>[ 1159.272131][ T1@C2] init: FDSize:	0
<14>[ 1159.272157][ T1@C2] init: Groups:	1026 3009 
<14>[ 1159.272184][ T1@C2] init: Threads:	2
<14>[ 1159.272210][ T1@C2] init: SigQ:	3/13301
<14>[ 1159.272236][ T1@C2] init: SigPnd:	0000000000000000
<14>[ 1159.272263][ T1@C2] init: ShdPnd:	0000000000004000
<14>[ 1159.272290][ T1@C2] init: SigBlk:	0000000080000000
<14>[ 1159.272317][ T1@C2] init: SigIgn:	0000002000001000
<14>[ 1159.272343][ T1@C2] init: SigCgt:	0000004c400084f8
<14>[ 1159.272371][ T1@C2] init: CapInh:	0000000000800000
<14>[ 1159.272397][ T1@C2] init: CapPrm:	0000000000800000
<14>[ 1159.272423][ T1@C2] init: CapEff:	0000000000800000
<14>[ 1159.272450][ T1@C2] init: CapBnd:	0000000000800000
<14>[ 1159.272477][ T1@C2] init: CapAmb:	0000000000800000
<14>[ 1159.272504][ T1@C2] init: NoNewPrivs:	0
<14>[ 1159.272530][ T1@C2] init: Seccomp:	0
<14>[ 1159.272558][ T1@C2] init: Seccomp_filters:	0
<14>[ 1159.272585][ T1@C2] init: Speculation_Store_Bypass:	vulnerable
<14>[ 1159.272611][ T1@C2] init: SpeculationIndirectBranch:	unknown
<14>[ 1159.272638][ T1@C2] init: Cpus_allowed:	c0
<14>[ 1159.272664][ T1@C2] init: Cpus_allowed_list:	6-7
<14>[ 1159.272690][ T1@C2] init: Mems_allowed:	1
<14>[ 1159.272717][ T1@C2] init: Mems_allowed_list:	0
<14>[ 1159.272743][ T1@C2] init: voluntary_ctxt_switches:	22109
<14>[ 1159.272769][ T1@C2] init: nonvoluntary_ctxt_switches:	851
<14>[ 1159.272795][ T1@C2] init: 
<14>[ 1159.273559][ T1@C2] init: Still running: 560
<14>[ 1159.273605][ T1@C2] init: Name:	main
<14>[ 1159.273633][ T1@C2] init: State:	Z (zombie)
<14>[ 1159.273661][ T1@C2] init: Tgid:	560
<14>[ 1159.273687][ T1@C2] init: Ngid:	0
<14>[ 1159.273713][ T1@C2] init: Pid:	560
<14>[ 1159.273739][ T1@C2] init: PPid:	1
<14>[ 1159.273765][ T1@C2] init: TracerPid:	0
<14>[ 1159.273793][ T1@C2] init: Uid:	0	0	0	0
<14>[ 1159.273820][ T1@C2] init: Gid:	0	0	0	0
<14>[ 1159.273847][ T1@C2] init: FDSize:	0
<14>[ 1159.273875][ T1@C2] init: Groups:	1065 3009 
<14>[ 1159.273901][ T1@C2] init: Threads:	2
<14>[ 1159.273927][ T1@C2] init: SigQ:	7/13301
<14>[ 1159.273953][ T1@C2] init: SigPnd:	0000000000000000
<14>[ 1159.273979][ T1@C2] init: ShdPnd:	0000000000014000
<14>[ 1159.274006][ T1@C2] init: SigBlk:	0000000080001204
<14>[ 1159.274034][ T1@C2] init: SigIgn:	0000002000000001
<14>[ 1159.274060][ T1@C2] init: SigCgt:	0000004e400184f8
<14>[ 1159.274087][ T1@C2] init: CapInh:	0000000000000000
<14>[ 1159.274113][ T1@C2] init: CapPrm:	000001ffffffffff
<14>[ 1159.276119][ T1@C2] init: CapEff:	000001ffffffffff
<14>[ 1159.276164][ T1@C2] init: CapBnd:	000001ffffffffff
<14>[ 1159.276192][ T1@C2] init: CapAmb:	0000000000000000
<14>[ 1159.276219][ T1@C2] init: NoNewPrivs:	0
<14>[ 1159.276246][ T1@C2] init: Seccomp:	0
<14>[ 1159.276273][ T1@C2] init: Seccomp_filters:	0
<14>[ 1159.276299][ T1@C2] init: Speculation_Store_Bypass:	vulnerable
<14>[ 1159.276327][ T1@C2] init: SpeculationIndirectBranch:	unknown
<14>[ 1159.276353][ T1@C2] init: Cpus_allowed:	ff
<14>[ 1159.276380][ T1@C2] init: Cpus_allowed_list:	0-7
<14>[ 1159.276406][ T1@C2] init: Mems_allowed:	1
<14>[ 1159.276432][ T1@C2] init: Mems_allowed_list:	0
<14>[ 1159.276458][ T1@C2] init: voluntary_ctxt_switches:	1576
<14>[ 1159.276484][ T1@C2] init: nonvoluntary_ctxt_switches:	4374
<14>[ 1159.276511][ T1@C2] init: 
<11>[ 1159.276805][ T1@C2] init: [service-misbehaving] : service 'zygote_secondary' is still running 3000ms after receiving SIGTERM
<11>[ 1159.276953][ T1@C2] init: [service-misbehaving] : service 'surfaceflinger' is still running 3000ms after receiving SIGTERM
<14>[ 1159.277076][ T1@C2] init: Stopping 234 services by sending SIGKILL
<14>[ 1159.277205][ T1@C2] init: Sending signal 9 to service 'surfaceflinger' (pid 717) process group...
<4>[ 1159.277324][ T1@C2] Signal_Debug: Req_proc is init, pid=1, Tar_proc is surfaceflinger, pid=717
<4>[ 1159.277346][ T1@C2] Signal_Debug: Tar_proc_group is surfaceflinger, tgid=717, sig=9
<4>[ 1159.278232][ T1@C2] Signal_Debug: Req_proc is init, pid=1, Tar_proc is surfaceflinger, pid=717
<4>[ 1159.278252][ T1@C2] Signal_Debug: Tar_proc_group is surfaceflinger, tgid=717, sig=9
<4>[ 1159.351830][T10467@C7] preemptoff_warn: C7 T:<10467>Jit thread pool D:31.834ms F:1159.319991s
<4>[ 1159.351916][T10467@C7] preemptoff_warn: C7 disabled preempt at:
<4>[ 1159.351916][T10467@C7] trace_preempt_off+0x74/0x7c
<4>[ 1159.351916][T10467@C7] preempt_count_add+0xe4/0x108
<4>[ 1159.351916][T10467@C7] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.351916][T10467@C7] zap_pte_range+0x100/0x588
<4>[ 1159.351916][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.351935][T10467@C7] preemptoff_warn: C7 enabled preempt at:
<4>[ 1159.351935][T10467@C7] trace_preempt_on+0x74/0x7c
<4>[ 1159.351935][T10467@C7] preempt_count_sub+0xbc/0xec
<4>[ 1159.351935][T10467@C7] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.351935][T10467@C7] zap_pte_range+0x4f8/0x588
<4>[ 1159.351935][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.355557][T1994@C6] preemptoff_warn: C6 T:<1994>binder:717_4 D:30.264ms F:1159.325291s
<4>[ 1159.355591][T1994@C6] preemptoff_warn: C6 disabled preempt at:
<4>[ 1159.355591][T1994@C6] trace_preempt_off+0x74/0x7c
<4>[ 1159.355591][T1994@C6] preempt_count_add+0xe4/0x108
<4>[ 1159.355591][T1994@C6] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.355591][T1994@C6] zap_pte_range+0x100/0x588
<4>[ 1159.355591][T1994@C6] unmap_page_range+0x194/0x27c
<4>[ 1159.355609][T1994@C6] preemptoff_warn: C6 enabled preempt at:
<4>[ 1159.355609][T1994@C6] trace_preempt_on+0x74/0x7c
<4>[ 1159.355609][T1994@C6] preempt_count_sub+0xbc/0xec
<4>[ 1159.355609][T1994@C6] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.355609][T1994@C6] zap_pte_range+0x4f8/0x588
<4>[ 1159.355609][T1994@C6] unmap_page_range+0x194/0x27c
<4>[ 1159.386179][T10467@C7] preemptoff_warn: C7 T:<10467>Jit thread pool D:34.181ms F:1159.351992s
<4>[ 1159.386256][T10467@C7] preemptoff_warn: C7 disabled preempt at:
<4>[ 1159.386256][T10467@C7] trace_preempt_off+0x74/0x7c
<4>[ 1159.386256][T10467@C7] preempt_count_add+0xe4/0x108
<4>[ 1159.386256][T10467@C7] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.386256][T10467@C7] zap_pte_range+0x100/0x588
<4>[ 1159.386256][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.386274][T10467@C7] preemptoff_warn: C7 enabled preempt at:
<4>[ 1159.386274][T10467@C7] trace_preempt_on+0x74/0x7c
<4>[ 1159.386274][T10467@C7] preempt_count_sub+0xbc/0xec
<4>[ 1159.386274][T10467@C7] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.386274][T10467@C7] zap_pte_range+0x4f8/0x588
<4>[ 1159.386274][T10467@C7] unmap_page_range+0x194/0x27c
<6>[ 1159.458985][T10003@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<4>[ 1159.527901][T10467@C7] preemptoff_warn: C7 T:<10467>Jit thread pool D:88.183ms F:1159.439711s
<4>[ 1159.527978][T10467@C7] preemptoff_warn: C7 disabled preempt at:
<4>[ 1159.527978][T10467@C7] trace_preempt_off+0x74/0x7c
<4>[ 1159.527978][T10467@C7] preempt_count_add+0xe4/0x108
<4>[ 1159.527978][T10467@C7] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.527978][T10467@C7] zap_pte_range+0x100/0x588
<4>[ 1159.527978][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.527996][T10467@C7] preemptoff_warn: C7 enabled preempt at:
<4>[ 1159.527996][T10467@C7] trace_preempt_on+0x74/0x7c
<4>[ 1159.527996][T10467@C7] preempt_count_sub+0xbc/0xec
<4>[ 1159.527996][T10467@C7] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.527996][T10467@C7] zap_pte_range+0x4f8/0x588
<4>[ 1159.527996][T10467@C7] unmap_page_range+0x194/0x27c
<4>[ 1159.548663][T10004@C2] preemptoff_warn: C2 T:<10004>pool-3-thread-3 D:35.314ms F:1159.513341s
<4>[ 1159.548766][T10004@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1159.548766][T10004@C2] trace_preempt_off+0x74/0x7c
<4>[ 1159.548766][T10004@C2] preempt_count_add+0xe4/0x108
<4>[ 1159.548766][T10004@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.548766][T10004@C2] zap_pte_range+0x100/0x588
<4>[ 1159.548766][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.548795][T10004@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1159.548795][T10004@C2] trace_preempt_on+0x74/0x7c
<4>[ 1159.548795][T10004@C2] preempt_count_sub+0xbc/0xec
<4>[ 1159.548795][T10004@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.548795][T10004@C2] zap_pte_range+0x4f8/0x588
<4>[ 1159.548795][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.549981][T9295@C5] preemptoff_warn: C5 T:<9295>MetricEventLogg D:34.811ms F:1159.515166s
<4>[ 1159.550036][T9295@C5] preemptoff_warn: C5 disabled preempt at:
<4>[ 1159.550036][T9295@C5] trace_preempt_off+0x74/0x7c
<4>[ 1159.550036][T9295@C5] preempt_count_add+0xe4/0x108
<4>[ 1159.550036][T9295@C5] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.550036][T9295@C5] zap_pte_range+0x100/0x588
<4>[ 1159.550036][T9295@C5] unmap_page_range+0x194/0x27c
<4>[ 1159.550064][T9295@C5] preemptoff_warn: C5 enabled preempt at:
<4>[ 1159.550064][T9295@C5] trace_preempt_on+0x74/0x7c
<4>[ 1159.550064][T9295@C5] preempt_count_sub+0xbc/0xec
<4>[ 1159.550064][T9295@C5] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.550064][T9295@C5] zap_pte_range+0x4f8/0x588
<4>[ 1159.550064][T9295@C5] unmap_page_range+0x194/0x27c
<4>[ 1159.612250][T9295@C5] preemptoff_warn: C5 T:<9295>MetricEventLogg D:33.972ms F:1159.578270s
<4>[ 1159.612363][T9295@C5] preemptoff_warn: C5 disabled preempt at:
<4>[ 1159.612363][T9295@C5] trace_preempt_off+0x74/0x7c
<4>[ 1159.612363][T9295@C5] preempt_count_add+0xe4/0x108
<4>[ 1159.612363][T9295@C5] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.612363][T9295@C5] zap_pte_range+0x100/0x588
<4>[ 1159.612363][T9295@C5] unmap_page_range+0x194/0x27c
<4>[ 1159.612392][T9295@C5] preemptoff_warn: C5 enabled preempt at:
<4>[ 1159.612392][T9295@C5] trace_preempt_on+0x74/0x7c
<4>[ 1159.612392][T9295@C5] preempt_count_sub+0xbc/0xec
<4>[ 1159.612392][T9295@C5] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.612392][T9295@C5] zap_pte_range+0x4f8/0x588
<4>[ 1159.612392][T9295@C5] unmap_page_range+0x194/0x27c
<4>[ 1159.677780][T10004@C2] preemptoff_warn: C2 T:<10004>pool-3-thread-3 D:33.877ms F:1159.643896s
<4>[ 1159.677874][T10004@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1159.677874][T10004@C2] trace_preempt_off+0x74/0x7c
<4>[ 1159.677874][T10004@C2] preempt_count_add+0xe4/0x108
<4>[ 1159.677874][T10004@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.677874][T10004@C2] zap_pte_range+0x100/0x588
<4>[ 1159.677874][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.677904][T10004@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1159.677904][T10004@C2] trace_preempt_on+0x74/0x7c
<4>[ 1159.677904][T10004@C2] preempt_count_sub+0xbc/0xec
<4>[ 1159.677904][T10004@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.677904][T10004@C2] zap_pte_range+0x4f8/0x588
<4>[ 1159.677904][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.717050][T10004@C2] preemptoff_warn: C2 T:<10004>pool-3-thread-3 D:31.056ms F:1159.685986s
<4>[ 1159.717160][T10004@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1159.717160][T10004@C2] trace_preempt_off+0x74/0x7c
<4>[ 1159.717160][T10004@C2] preempt_count_add+0xe4/0x108
<4>[ 1159.717160][T10004@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.717160][T10004@C2] zap_pte_range+0x100/0x588
<4>[ 1159.717160][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.717188][T10004@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1159.717188][T10004@C2] trace_preempt_on+0x74/0x7c
<4>[ 1159.717188][T10004@C2] preempt_count_sub+0xbc/0xec
<4>[ 1159.717188][T10004@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.717188][T10004@C2] zap_pte_range+0x4f8/0x588
<4>[ 1159.717188][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.753303][T10004@C2] preemptoff_warn: C2 T:<10004>pool-3-thread-3 D:36.091ms F:1159.717205s
<4>[ 1159.753408][T10004@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1159.753408][T10004@C2] trace_preempt_off+0x74/0x7c
<4>[ 1159.753408][T10004@C2] preempt_count_add+0xe4/0x108
<4>[ 1159.753408][T10004@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.753408][T10004@C2] zap_pte_range+0x100/0x588
<4>[ 1159.753408][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.753437][T10004@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1159.753437][T10004@C2] trace_preempt_on+0x74/0x7c
<4>[ 1159.753437][T10004@C2] preempt_count_sub+0xbc/0xec
<4>[ 1159.753437][T10004@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.753437][T10004@C2] zap_pte_range+0x4f8/0x588
<4>[ 1159.753437][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.810308][T10004@C2] preemptoff_warn: C2 T:<10004>pool-3-thread-3 D:38.916ms F:1159.771385s
<4>[ 1159.810409][T10004@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1159.810409][T10004@C2] trace_preempt_off+0x74/0x7c
<4>[ 1159.810409][T10004@C2] preempt_count_add+0xe4/0x108
<4>[ 1159.810409][T10004@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.810409][T10004@C2] zap_pte_range+0x100/0x588
<4>[ 1159.810409][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.810438][T10004@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1159.810438][T10004@C2] trace_preempt_on+0x74/0x7c
<4>[ 1159.810438][T10004@C2] preempt_count_sub+0xbc/0xec
<4>[ 1159.810438][T10004@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.810438][T10004@C2] zap_pte_range+0x4f8/0x588
<4>[ 1159.810438][T10004@C2] unmap_page_range+0x194/0x27c
<4>[ 1159.890190][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:30.068ms F:1159.860113s
<4>[ 1159.890304][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1159.890304][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1159.890304][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1159.890304][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.890304][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1159.890304][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1159.890333][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1159.890333][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1159.890333][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1159.890333][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.890333][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1159.890333][T10004@C4] unmap_page_range+0x194/0x27c
<6>[ 1159.915377][T9295@C5] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<4>[ 1159.983166][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:79.478ms F:1159.903679s
<4>[ 1159.983275][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1159.983275][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1159.983275][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1159.983275][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1159.983275][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1159.983275][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1159.983304][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1159.983304][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1159.983304][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1159.983304][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1159.983304][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1159.983304][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.036569][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:40.431ms F:1159.996129s
<4>[ 1160.036688][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.036688][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.036688][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.036688][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.036688][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.036688][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.036718][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.036718][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.036718][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.036718][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.036718][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.036718][T10004@C4] unmap_page_range+0x194/0x27c
<6>[ 1160.044092][T12324@C5] binder: release 2202:9547 transaction 456099 out, still active
<6>[ 1160.044145][T12324@C5] binder: undelivered TRANSACTION_COMPLETE
<4>[ 1160.094079][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:35.184ms F:1160.058887s
<4>[ 1160.094194][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.094194][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.094194][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.094194][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.094194][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.094194][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.094223][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.094223][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.094223][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.094223][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.094223][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.094223][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.125115][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:30.867ms F:1160.094239s
<4>[ 1160.125217][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.125217][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.125217][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.125217][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.125217][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.125217][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.125245][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.125245][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.125245][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.125245][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.125245][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.125245][T10004@C4] unmap_page_range+0x194/0x27c
<6>[ 1160.154867][T12599@C1] binder: undelivered death notification, b400007a6f76d580
<4>[ 1160.207836][T3963@C5] preemptoff_warn: C5 T:<3963>Thread-6 D:30.440ms F:1160.177387s
<4>[ 1160.207945][T3963@C5] preemptoff_warn: C5 disabled preempt at:
<4>[ 1160.207945][T3963@C5] trace_preempt_off+0x74/0x7c
<4>[ 1160.207945][T3963@C5] preempt_count_add+0xe4/0x108
<4>[ 1160.207945][T3963@C5] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.207945][T3963@C5] zap_pte_range+0x100/0x588
<4>[ 1160.207945][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.207975][T3963@C5] preemptoff_warn: C5 enabled preempt at:
<4>[ 1160.207975][T3963@C5] trace_preempt_on+0x74/0x7c
<4>[ 1160.207975][T3963@C5] preempt_count_sub+0xbc/0xec
<4>[ 1160.207975][T3963@C5] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.207975][T3963@C5] zap_pte_range+0x4f8/0x588
<4>[ 1160.207975][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.278902][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:30.264ms F:1160.248628s
<4>[ 1160.279017][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.279017][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.279017][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.279017][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.279017][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.279017][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.279046][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.279046][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.279046][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.279046][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.279046][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.279046][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.290895][T3963@C5] preemptoff_warn: C5 T:<3963>Thread-6 D:82.899ms F:1160.207993s
<4>[ 1160.290943][T3963@C5] preemptoff_warn: C5 disabled preempt at:
<4>[ 1160.290943][T3963@C5] trace_preempt_off+0x74/0x7c
<4>[ 1160.290943][T3963@C5] preempt_count_add+0xe4/0x108
<4>[ 1160.290943][T3963@C5] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.290943][T3963@C5] zap_pte_range+0x100/0x588
<4>[ 1160.290943][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.290971][T3963@C5] preemptoff_warn: C5 enabled preempt at:
<4>[ 1160.290971][T3963@C5] trace_preempt_on+0x74/0x7c
<4>[ 1160.290971][T3963@C5] preempt_count_sub+0xbc/0xec
<4>[ 1160.290971][T3963@C5] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.290971][T3963@C5] zap_pte_range+0x4f8/0x588
<4>[ 1160.290971][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.313127][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:34.048ms F:1160.279074s
<4>[ 1160.313197][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.313197][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.313197][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.313197][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.313197][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.313197][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.313225][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.313225][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.313225][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.313225][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.313225][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.313225][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.322083][T10534@C2] preemptoff_warn: C2 T:<10534>GmsDynamite D:35.281ms F:1160.286799s
<4>[ 1160.322130][T10534@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1160.322130][T10534@C2] trace_preempt_off+0x74/0x7c
<4>[ 1160.322130][T10534@C2] preempt_count_add+0xe4/0x108
<4>[ 1160.322130][T10534@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.322130][T10534@C2] zap_pte_range+0x100/0x588
<4>[ 1160.322130][T10534@C2] unmap_page_range+0x194/0x27c
<4>[ 1160.322157][T10534@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1160.322157][T10534@C2] trace_preempt_on+0x74/0x7c
<4>[ 1160.322157][T10534@C2] preempt_count_sub+0xbc/0xec
<4>[ 1160.322157][T10534@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.322157][T10534@C2] zap_pte_range+0x4f8/0x588
<4>[ 1160.322157][T10534@C2] unmap_page_range+0x194/0x27c
<6>[ 1160.323043][T12611@C0] binder: undelivered transaction 447105, process died.
<6>[ 1160.323081][T12611@C0] binder: undelivered transaction 447202, process died.
<6>[ 1160.323834][T12611@C0] binder: undelivered transaction 446189, process died.
<4>[ 1160.377290][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:31.119ms F:1160.346162s
<4>[ 1160.377396][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.377396][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.377396][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.377396][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.377396][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.377396][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.377426][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.377426][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.377426][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.377426][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.377426][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.377426][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.378607][T10534@C2] preemptoff_warn: C2 T:<10534>GmsDynamite D:31.294ms F:1160.347311s
<4>[ 1160.378649][T10534@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1160.378649][T10534@C2] trace_preempt_off+0x74/0x7c
<4>[ 1160.378649][T10534@C2] preempt_count_add+0xe4/0x108
<4>[ 1160.378649][T10534@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.378649][T10534@C2] zap_pte_range+0x100/0x588
<4>[ 1160.378649][T10534@C2] unmap_page_range+0x194/0x27c
<4>[ 1160.378676][T10534@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1160.378676][T10534@C2] trace_preempt_on+0x74/0x7c
<4>[ 1160.378676][T10534@C2] preempt_count_sub+0xbc/0xec
<4>[ 1160.378676][T10534@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.378676][T10534@C2] zap_pte_range+0x4f8/0x588
<4>[ 1160.378676][T10534@C2] unmap_page_range+0x194/0x27c
<4>[ 1160.414755][T3963@C5] preemptoff_warn: C5 T:<3963>Thread-6 D:86.631ms F:1160.328114s
<4>[ 1160.414861][T3963@C5] preemptoff_warn: C5 disabled preempt at:
<4>[ 1160.414861][T3963@C5] trace_preempt_off+0x74/0x7c
<4>[ 1160.414861][T3963@C5] preempt_count_add+0xe4/0x108
<4>[ 1160.414861][T3963@C5] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.414861][T3963@C5] zap_pte_range+0x100/0x588
<4>[ 1160.414861][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.414890][T3963@C5] preemptoff_warn: C5 enabled preempt at:
<4>[ 1160.414890][T3963@C5] trace_preempt_on+0x74/0x7c
<4>[ 1160.414890][T3963@C5] preempt_count_sub+0xbc/0xec
<4>[ 1160.414890][T3963@C5] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.414890][T3963@C5] zap_pte_range+0x4f8/0x588
<4>[ 1160.414890][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.487454][T10004@C4] preemptoff_warn: C4 T:<10004>pool-3-thread-3 D:35.157ms F:1160.452289s
<4>[ 1160.487568][T10004@C4] preemptoff_warn: C4 disabled preempt at:
<4>[ 1160.487568][T10004@C4] trace_preempt_off+0x74/0x7c
<4>[ 1160.487568][T10004@C4] preempt_count_add+0xe4/0x108
<4>[ 1160.487568][T10004@C4] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.487568][T10004@C4] zap_pte_range+0x100/0x588
<4>[ 1160.487568][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.487597][T10004@C4] preemptoff_warn: C4 enabled preempt at:
<4>[ 1160.487597][T10004@C4] trace_preempt_on+0x74/0x7c
<4>[ 1160.487597][T10004@C4] preempt_count_sub+0xbc/0xec
<4>[ 1160.487597][T10004@C4] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.487597][T10004@C4] zap_pte_range+0x4f8/0x588
<4>[ 1160.487597][T10004@C4] unmap_page_range+0x194/0x27c
<4>[ 1160.493644][T3963@C5] preemptoff_warn: C5 T:<3963>Thread-6 D:45.359ms F:1160.448280s
<4>[ 1160.493707][T3963@C5] preemptoff_warn: C5 disabled preempt at:
<4>[ 1160.493707][T3963@C5] trace_preempt_off+0x74/0x7c
<4>[ 1160.493707][T3963@C5] preempt_count_add+0xe4/0x108
<4>[ 1160.493707][T3963@C5] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.493707][T3963@C5] zap_pte_range+0x100/0x588
<4>[ 1160.493707][T3963@C5] unmap_page_range+0x194/0x27c
<4>[ 1160.493734][T3963@C5] preemptoff_warn: C5 enabled preempt at:
<4>[ 1160.493734][T3963@C5] trace_preempt_on+0x74/0x7c
<4>[ 1160.493734][T3963@C5] preempt_count_sub+0xbc/0xec
<4>[ 1160.493734][T3963@C5] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.493734][T3963@C5] zap_pte_range+0x4f8/0x588
<4>[ 1160.493734][T3963@C5] unmap_page_range+0x194/0x27c
<6>[ 1160.513400][T3064@C1] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<4>[ 1160.682457][T3963@C0] preemptoff_warn: C0 T:<3963>Thread-6 D:86.396ms F:1160.596054s
<4>[ 1160.682567][T3963@C0] preemptoff_warn: C0 disabled preempt at:
<4>[ 1160.682567][T3963@C0] trace_preempt_off+0x74/0x7c
<4>[ 1160.682567][T3963@C0] preempt_count_add+0xe4/0x108
<4>[ 1160.682567][T3963@C0] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.682567][T3963@C0] zap_pte_range+0x100/0x588
<4>[ 1160.682567][T3963@C0] unmap_page_range+0x194/0x27c
<4>[ 1160.682596][T3963@C0] preemptoff_warn: C0 enabled preempt at:
<4>[ 1160.682596][T3963@C0] trace_preempt_on+0x74/0x7c
<4>[ 1160.682596][T3963@C0] preempt_count_sub+0xbc/0xec
<4>[ 1160.682596][T3963@C0] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.682596][T3963@C0] zap_pte_range+0x4f8/0x588
<4>[ 1160.682596][T3963@C0] unmap_page_range+0x194/0x27c
<4>[ 1160.745613][T3963@C0] preemptoff_warn: C0 T:<3963>Thread-6 D:52.666ms F:1160.692938s
<4>[ 1160.745725][T3963@C0] preemptoff_warn: C0 disabled preempt at:
<4>[ 1160.745725][T3963@C0] trace_preempt_off+0x74/0x7c
<4>[ 1160.745725][T3963@C0] preempt_count_add+0xe4/0x108
<4>[ 1160.745725][T3963@C0] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.745725][T3963@C0] zap_pte_range+0x100/0x588
<4>[ 1160.745725][T3963@C0] unmap_page_range+0x194/0x27c
<4>[ 1160.745754][T3963@C0] preemptoff_warn: C0 enabled preempt at:
<4>[ 1160.745754][T3963@C0] trace_preempt_on+0x74/0x7c
<4>[ 1160.745754][T3963@C0] preempt_count_sub+0xbc/0xec
<4>[ 1160.745754][T3963@C0] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.745754][T3963@C0] zap_pte_range+0x4f8/0x588
<4>[ 1160.745754][T3963@C0] unmap_page_range+0x194/0x27c
<6>[ 1160.795218][T1994@C1] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<4>[ 1160.798415][T3963@C0] preemptoff_warn: C0 T:<3963>Thread-6 D:52.637ms F:1160.745771s
<4>[ 1160.798499][T3963@C0] preemptoff_warn: C0 disabled preempt at:
<4>[ 1160.798499][T3963@C0] trace_preempt_off+0x74/0x7c
<4>[ 1160.798499][T3963@C0] preempt_count_add+0xe4/0x108
<4>[ 1160.798499][T3963@C0] _raw_spin_lock+0x2c/0x9c
<4>[ 1160.798499][T3963@C0] zap_pte_range+0x100/0x588
<4>[ 1160.798499][T3963@C0] unmap_page_range+0x194/0x27c
<4>[ 1160.798528][T3963@C0] preemptoff_warn: C0 enabled preempt at:
<4>[ 1160.798528][T3963@C0] trace_preempt_on+0x74/0x7c
<4>[ 1160.798528][T3963@C0] preempt_count_sub+0xbc/0xec
<4>[ 1160.798528][T3963@C0] _raw_spin_unlock+0x2c/0x54
<4>[ 1160.798528][T3963@C0] zap_pte_range+0x4f8/0x588
<4>[ 1160.798528][T3963@C0] unmap_page_range+0x194/0x27c
<14>[ 1160.818946][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_717
<14>[ 1160.820363][ T1@C2] init: Sending signal 9 to service 'zygote_secondary' (pid 560) process group...
<14>[ 1160.821768][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_560
<14>[ 1160.823772][ T1@C2] init: Service 'zygote_secondary' (pid 560) received signal 15
<14>[ 1160.823836][ T1@C2] init: Sending signal 9 to service 'zygote_secondary' (pid 560) process group...
<11>[ 1160.824143][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_560/cgroup.kill: No such file or directory
<11>[ 1160.824303][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_560/cgroup.procs: No such file or directory
<3>[ 1160.833218][T12599@C1] |__c2e     mmc0: 1076    5   28   46   24    1    0    0    0    0    0    0    0    0
<3>[ 1160.833279][T12599@C1] |__d2e     mmc0: 1023    6   27   60   30    1    0    0    0    0    0    0    0    0
<3>[ 1160.833303][T12599@C1] |__blocks  mmc0:    0    0    0    0 1033   32   32   25   25    0    0    0    0    0
<3>[ 1160.833326][T12599@C1] |__speed   mmc0: r= 8.40M/s, w= 24.8M/s, r_blk= 16008, w_blk= 776
<14>[ 1160.839302][ T1@C2] init: Service 'surfaceflinger' (pid 717) received signal 15
<14>[ 1160.839384][ T1@C2] init: Sending signal 9 to service 'surfaceflinger' (pid 717) process group...
<4>[ 1160.839470][ T1@C2] Signal_Debug: Req_proc is init, pid=1, Tar_proc is surfaceflinger, pid=717
<4>[ 1160.839492][ T1@C2] Signal_Debug: Tar_proc_group is surfaceflinger, tgid=717, sig=9
<11>[ 1160.839751][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_717/cgroup.kill: No such file or directory
<11>[ 1160.839913][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_717/cgroup.procs: No such file or directory
<4>[ 1160.839953][ T1@C2] Signal_Debug: Req_proc is init, pid=1, Tar_proc is surfaceflinger, pid=717
<4>[ 1160.839969][ T1@C2] Signal_Debug: Tar_proc_group is surfaceflinger, tgid=717, sig=9
<14>[ 1160.849026][ T1@C2] init: Untracked pid 1999 received signal 15
<14>[ 1160.849094][ T1@C2] init: Untracked pid 1999 did not have an associated service entry and will not be reaped
<14>[ 1160.850510][ T1@C2] init: Untracked pid 2000 received signal 15
<14>[ 1160.850582][ T1@C2] init: Untracked pid 2000 did not have an associated service entry and will not be reaped
<14>[ 1160.851317][ T1@C2] init: Untracked pid 2202 received signal 15
<14>[ 1160.851374][ T1@C2] init: Untracked pid 2202 did not have an associated service entry and will not be reaped
<14>[ 1160.852643][ T1@C2] init: Untracked pid 2285 received signal 15
<14>[ 1160.852695][ T1@C2] init: Untracked pid 2285 did not have an associated service entry and will not be reaped
<14>[ 1160.853555][ T1@C2] init: Untracked pid 2745 received signal 15
<14>[ 1160.853612][ T1@C2] init: Untracked pid 2745 did not have an associated service entry and will not be reaped
<14>[ 1160.854417][ T1@C2] init: Untracked pid 3026 received signal 15
<14>[ 1160.854465][ T1@C2] init: Untracked pid 3026 did not have an associated service entry and will not be reaped
<14>[ 1160.855159][ T1@C2] init: Untracked pid 3042 received signal 15
<14>[ 1160.855201][ T1@C2] init: Untracked pid 3042 did not have an associated service entry and will not be reaped
<14>[ 1160.855666][ T1@C2] init: Untracked pid 4402 received signal 15
<14>[ 1160.855705][ T1@C2] init: Untracked pid 4402 did not have an associated service entry and will not be reaped
<14>[ 1160.856326][ T1@C2] init: Untracked pid 4687 received signal 15
<14>[ 1160.856364][ T1@C2] init: Untracked pid 4687 did not have an associated service entry and will not be reaped
<14>[ 1160.857000][ T1@C2] init: Untracked pid 7177 received signal 15
<14>[ 1160.857039][ T1@C2] init: Untracked pid 7177 did not have an associated service entry and will not be reaped
<14>[ 1160.857607][ T1@C2] init: Untracked pid 8887 received signal 15
<14>[ 1160.857644][ T1@C2] init: Untracked pid 8887 did not have an associated service entry and will not be reaped
<14>[ 1160.858182][ T1@C2] init: Untracked pid 9387 received signal 15
<14>[ 1160.858222][ T1@C2] init: Untracked pid 9387 did not have an associated service entry and will not be reaped
<14>[ 1160.858736][ T1@C2] init: Untracked pid 9795 received signal 15
<14>[ 1160.858773][ T1@C2] init: Untracked pid 9795 did not have an associated service entry and will not be reaped
<14>[ 1160.859418][ T1@C2] init: Untracked pid 10658 received signal 15
<14>[ 1160.859455][ T1@C2] init: Untracked pid 10658 did not have an associated service entry and will not be reaped
<14>[ 1160.859972][ T1@C2] init: Untracked pid 11874 received signal 15
<14>[ 1160.860009][ T1@C2] init: Untracked pid 11874 did not have an associated service entry and will not be reaped
<14>[ 1160.861161][ T1@C2] init: Untracked pid 2009 received signal 15
<14>[ 1160.861198][ T1@C2] init: Untracked pid 2009 did not have an associated service entry and will not be reaped
<14>[ 1160.861455][ T1@C2] init: Untracked pid 2010 received signal 15
<14>[ 1160.861490][ T1@C2] init: Untracked pid 2010 did not have an associated service entry and will not be reaped
<14>[ 1160.862512][ T1@C2] init: Untracked pid 2011 received signal 15
<14>[ 1160.862549][ T1@C2] init: Untracked pid 2011 did not have an associated service entry and will not be reaped
<14>[ 1160.862812][ T1@C2] init: Untracked pid 2012 received signal 15
<14>[ 1160.862848][ T1@C2] init: Untracked pid 2012 did not have an associated service entry and will not be reaped
<14>[ 1160.863094][ T1@C2] init: Untracked pid 2188 received signal 15
<14>[ 1160.863128][ T1@C2] init: Untracked pid 2188 did not have an associated service entry and will not be reaped
<14>[ 1160.864599][ T1@C5] init: Calling /system/bin/vdc volume abort_fuse
<6>[ 1160.890983][T10004@C5] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<15>[ 1160.959558][T12612@C6] vdc: Waited 0ms for vold
<36>[ 1160.981751][T308@C1] type=1400 audit(1735701455.211:621): avc:  denied  { create } for  comm="binder:322_4" name="abort.tmp" scontext=u:r:vold:s0 tcontext=u:object_r:fusectlfs:s0 tclass=file permissive=0
<14>[ 1160.991999][ T1@C6] init: Calling /system/bin/vdc volume shutdown
<6>[ 1161.016821][T1982@C2] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[ 1161.037496][T1982@C1] unisoc_userlog: userlog_release
<15>[ 1161.051726][T12613@C7] vdc: Waited 0ms for vold
<6>[ 1161.066444][T410@C3] binder_alloc: 1213: binder_alloc_buf, no vma
<4>[ 1161.066507][T410@C3] binder_transaction: 1 callbacks suppressed
<6>[ 1161.066514][T410@C3] binder: 322:410 transaction failed 29189/-3, size 104-0 line 3397
<6>[ 1161.144587][T12324@C5] binder: undelivered death notification, b400007a6f7834d0
<6>[ 1161.144653][T12324@C5] binder: undelivered death notification, b400007a6f7c5f20
<6>[ 1161.149898][T12324@C5] binder: release 1213:2800 transaction 456102 in, still active
<6>[ 1161.149954][T12324@C5] binder: send failed reply for transaction 456102, target dead
<6>[ 1161.150561][T12324@C5] binder: release 1213:3899 transaction 456099 in, still active
<6>[ 1161.150586][T12324@C5] binder: send failed reply for transaction 456099, target dead
<6>[ 1161.150715][T12324@C5] binder: release 1213:3911 transaction 456022 in, still active
<6>[ 1161.150739][T12324@C5] binder: send failed reply for transaction 456022, target dead
<4>[ 1161.179603][T3963@C0] preemptoff_warn: C0 T:<3963>Thread-6 D:30.020ms F:1161.149576s
<4>[ 1161.179706][T3963@C0] preemptoff_warn: C0 disabled preempt at:
<4>[ 1161.179706][T3963@C0] trace_preempt_off+0x74/0x7c
<4>[ 1161.179706][T3963@C0] preempt_count_add+0xe4/0x108
<4>[ 1161.179706][T3963@C0] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.179706][T3963@C0] zap_pte_range+0x100/0x588
<4>[ 1161.179706][T3963@C0] unmap_page_range+0x194/0x27c
<4>[ 1161.179737][T3963@C0] preemptoff_warn: C0 enabled preempt at:
<4>[ 1161.179737][T3963@C0] trace_preempt_on+0x74/0x7c
<4>[ 1161.179737][T3963@C0] preempt_count_sub+0xbc/0xec
<4>[ 1161.179737][T3963@C0] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.179737][T3963@C0] zap_pte_range+0x4f8/0x588
<4>[ 1161.179737][T3963@C0] unmap_page_range+0x194/0x27c
<6>[ 1161.187983][T12324@C5] binder: undelivered death notification, b400007a6f77f6f0
<6>[ 1161.188040][T12324@C5] binder: undelivered death notification, b400007a6f7d3bd0
<6>[ 1161.188067][T12324@C5] binder: undelivered death notification, b400007a6f76f1b0
<6>[ 1161.188093][T12324@C5] binder: undelivered death notification, b400007a6f757420
<6>[ 1161.188119][T12324@C5] binder: undelivered death notification, b400007a6f8a6cd0
<14>[ 1161.223458][ T1@C6] init: Sending signal 9 to service 'vold' (pid 322) process group...
<4>[ 1161.297690][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:63.521ms F:1161.234158s
<4>[ 1161.297850][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.297850][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.297850][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.297850][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.297850][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.297850][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.297923][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.297923][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.297923][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.297923][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.297923][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.297923][T3963@C2] unmap_page_range+0x194/0x27c
<14>[ 1161.327340][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_322
<4>[ 1161.328794][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:30.826ms F:1161.297962s
<4>[ 1161.329008][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.329008][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.329008][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.329008][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.329008][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.329008][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.329070][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.329070][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.329070][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.329070][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.329070][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.329070][T3963@C2] unmap_page_range+0x194/0x27c
<14>[ 1161.329902][ T1@C6] init: Stopping 4 services by sending SIGKILL
<14>[ 1161.330038][ T1@C6] init: Sending signal 9 to service 'adbd' (pid 757) process group...
<4>[ 1161.367362][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:38.237ms F:1161.329118s
<4>[ 1161.367503][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.367503][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.367503][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.367503][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.367503][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.367503][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.367578][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.367578][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.367578][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.367578][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.367578][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.367578][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.435879][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:68.254ms F:1161.367621s
<4>[ 1161.435990][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.435990][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.435990][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.435990][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.435990][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.435990][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.436063][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.436063][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.436063][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.436063][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.436063][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.436063][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.483243][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:38.040ms F:1161.445195s
<4>[ 1161.483379][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.483379][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.483379][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.483379][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.483379][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.483379][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.483451][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.483451][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.483451][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.483451][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.483451][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.483451][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.551605][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:68.109ms F:1161.483493s
<4>[ 1161.551704][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.551704][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.551704][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.551704][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.551704][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.551704][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.551778][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.551778][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.551778][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.551778][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.551778][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.551778][T3963@C2] unmap_page_range+0x194/0x27c
<6>[ 1161.807241][T776@C1] musb-sprd 64900000.usb: musb_sprd_runtime_resume: enter
<6>[ 1161.807294][T776@C1] musb-sprd 64900000.usb: musb_sprd_resume: enter
<6>[ 1161.833474][T776@C1] musb-hdrc musb-hdrc.1.auto: musb runtime resume
<6>[ 1161.833681][T776@C1] musb-sprd 64900000.usb: sprd_musb_disable: enter
<6>[ 1161.833700][T776@C1] sprd_musb_try_idle enter, otg->state 0.
<6>[ 1161.833709][T776@C1] sprd_musb_try_idle enter, otg->state 0.
<14>[ 1161.836252][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_757
<14>[ 1161.837918][ T1@C6] init: Sending signal 9 to service 'tombstoned' (pid 453) process group...
<14>[ 1161.854132][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1058/pid_453
<14>[ 1161.855772][ T1@C6] init: Sending signal 9 to service 'console' (pid 289) process group...
<4>[ 1161.875402][T3963@C2] preemptoff_warn: C2 T:<3963>Thread-6 D:41.559ms F:1161.833838s
<4>[ 1161.875493][T3963@C2] preemptoff_warn: C2 disabled preempt at:
<4>[ 1161.875493][T3963@C2] trace_preempt_off+0x74/0x7c
<4>[ 1161.875493][T3963@C2] preempt_count_add+0xe4/0x108
<4>[ 1161.875493][T3963@C2] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.875493][T3963@C2] zap_pte_range+0x100/0x588
<4>[ 1161.875493][T3963@C2] unmap_page_range+0x194/0x27c
<4>[ 1161.875521][T3963@C2] preemptoff_warn: C2 enabled preempt at:
<4>[ 1161.875521][T3963@C2] trace_preempt_on+0x74/0x7c
<4>[ 1161.875521][T3963@C2] preempt_count_sub+0xbc/0xec
<4>[ 1161.875521][T3963@C2] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.875521][T3963@C2] zap_pte_range+0x4f8/0x588
<4>[ 1161.875521][T3963@C2] unmap_page_range+0x194/0x27c
<14>[ 1161.876366][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_2000/pid_289
<14>[ 1161.877965][ T1@C6] init: Sending signal 9 to service 'logd' (pid 282) process group...
<4>[ 1161.924787][T3963@C0] preemptoff_warn: C0 T:<3963>Thread-6 D:30.068ms F:1161.894710s
<4>[ 1161.924939][T3963@C0] preemptoff_warn: C0 disabled preempt at:
<4>[ 1161.924939][T3963@C0] trace_preempt_off+0x74/0x7c
<4>[ 1161.924939][T3963@C0] preempt_count_add+0xe4/0x108
<4>[ 1161.924939][T3963@C0] _raw_spin_lock+0x2c/0x9c
<4>[ 1161.924939][T3963@C0] zap_pte_range+0x100/0x588
<4>[ 1161.924939][T3963@C0] unmap_page_range+0x194/0x27c
<4>[ 1161.924979][T3963@C0] preemptoff_warn: C0 enabled preempt at:
<4>[ 1161.924979][T3963@C0] trace_preempt_on+0x74/0x7c
<4>[ 1161.924979][T3963@C0] preempt_count_sub+0xbc/0xec
<4>[ 1161.924979][T3963@C0] _raw_spin_unlock+0x2c/0x54
<4>[ 1161.924979][T3963@C0] zap_pte_range+0x4f8/0x588
<4>[ 1161.924979][T3963@C0] unmap_page_range+0x194/0x27c
<14>[ 1161.930276][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1036/pid_282
<14>[ 1161.932400][ T1@C1] init: Subcontext received signal 15
<14>[ 1161.932469][ T1@C1] init: Subcontext did not have an associated service entry and will not be reaped
<14>[ 1161.933190][ T1@C1] init: Service 'logd' (pid 282) received signal 9
<14>[ 1161.933344][ T1@C1] init: Sending signal 9 to service 'logd' (pid 282) process group...
<11>[ 1161.933664][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1036/pid_282/cgroup.kill: No such file or directory
<11>[ 1161.933827][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1036/pid_282/cgroup.procs: No such file or directory
<14>[ 1161.938430][ T1@C1] init: Service 'console' (pid 289) received signal 9
<14>[ 1161.938478][ T1@C1] init: Sending signal 9 to service 'console' (pid 289) process group...
<11>[ 1161.938741][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_2000/pid_289/cgroup.kill: No such file or directory
<11>[ 1161.938895][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_2000/pid_289/cgroup.procs: No such file or directory
<14>[ 1161.942045][ T1@C2] init: Service 'vold' (pid 322) received signal 9
<14>[ 1161.942093][ T1@C2] init: Sending signal 9 to service 'vold' (pid 322) process group...
<11>[ 1161.942347][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_322/cgroup.kill: No such file or directory
<11>[ 1161.942498][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_322/cgroup.procs: No such file or directory
<11>[ 1161.942559][ T1@C2] init: Service vold has 'reboot_on_failure' option and failed, shutting down system.
<14>[ 1161.946210][ T1@C2] init: Service 'tombstoned' (pid 453) received signal 9
<14>[ 1161.946257][ T1@C2] init: Sending signal 9 to service 'tombstoned' (pid 453) process group...
<11>[ 1161.946512][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1058/pid_453/cgroup.kill: No such file or directory
<11>[ 1161.946664][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1058/pid_453/cgroup.procs: No such file or directory
<14>[ 1161.957155][ T1@C2] init: Service 'adbd' (pid 757) received signal 9
<14>[ 1161.957212][ T1@C2] init: Sending signal 9 to service 'adbd' (pid 757) process group...
<11>[ 1161.957515][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_757/cgroup.kill: No such file or directory
<11>[ 1161.957670][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_757/cgroup.procs: No such file or directory
<14>[ 1161.961491][ T1@C2] init: Untracked pid 1213 received signal 15
<14>[ 1161.961537][ T1@C2] init: Untracked pid 1213 did not have an associated service entry and will not be reaped
<14>[ 1161.963580][ T1@C2] init: Untracked pid 2384 received signal 15
<14>[ 1161.963623][ T1@C2] init: Untracked pid 2384 did not have an associated service entry and will not be reaped
<14>[ 1161.964615][ T1@C2] init: Untracked pid 2855 received signal 15
<14>[ 1161.964654][ T1@C2] init: Untracked pid 2855 did not have an associated service entry and will not be reaped
<14>[ 1161.965716][ T1@C2] init: Untracked pid 10221 received signal 15
<14>[ 1161.965756][ T1@C2] init: Untracked pid 10221 did not have an associated service entry and will not be reaped
<14>[ 1161.966692][ T1@C2] init: sync() before umount...
<14>[ 1162.113566][ T1@C1] init: sync() before umount took146ms
<14>[ 1162.115830][ T1@C1] init: swapoff() start...
<6>[ 1162.223122][T116@C2] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[ 1162.223187][T116@C2] WCN BASE: tx:at+loopcheck=1162216,279439
<6>[ 1162.223187][T116@C2] 
<6>[ 1162.223357][T116@C2] WCN BASE: mdbg_tx_cb, chn:0
<6>[ 1162.230919][T364@C1] WCN BASE: : rx:loopcheck_ack:ap_send=1162216,cp2_bootup=279439,cp2_send=1162673
<6>[ 1162.230919][T364@C1] 
<6>[ 1162.231055][T116@C2] WCN BASE: loopcheck(176) 2025-01-01_03:17:36.466646552
<4>[ 1162.231682][T366@C3] mdbg_ring_write: 125 callbacks suppressed
<6>[ 1162.231696][T366@C3] WCN BASE: mdbg_ring_write totallen:1950176 write:1024 wp:00000000445f7842 rp:000000005e0a8200 [rate:11829]
<6>[ 1162.458901][T12599@C1] musb-hdrc musb-hdrc.1.auto: musb runtime suspend
<6>[ 1162.459295][T12599@C1] musb-sprd 64900000.usb: enter into idle mode
<6>[ 1163.081269][T12599@C1] musb-sprd 64900000.usb: musb_sprd_runtime_suspend: enter
<6>[ 1163.081358][T12599@C1] musb-sprd 64900000.usb: musb_sprd_suspend: enter
<6>[ 1165.590136][    C2] |_mmcblk0    total complete 3272 requests
<6>[ 1165.590193][    C2] |_mmcblk0    R complete:   3159 request    34256 sectors
<6>[ 1165.590208][    C2] |_mmcblk0    R i2i[  8- 8192ms]: 2839   83  115  115    7    0    0    0    0    0    0    0
<6>[ 1165.590237][    C2] |_mmcblk0    R i2c[  8- 8192ms]: 2966  149   44    0    0    0    0    0    0    0    0    0
<6>[ 1165.590263][    C2] |_mmcblk0    W complete:     99 request     7496 sectors
<6>[ 1165.590275][    C2] |_mmcblk0    W i2i[  8- 8192ms]:   83   13    3    0    0    0    0    0    0    0    0    0
<6>[ 1165.590300][    C2] |_mmcblk0    W i2c[  8- 8192ms]:   94    1    0    4    0    0    0    0    0    0    0    0
<6>[ 1165.590326][    C2] |_mmcblk0    F complete:     10 request        0 sectors
<6>[ 1165.590338][    C2] |_mmcblk0    F i2i[  8- 8192ms]:   10    0    0    0    0    0    0    0    0    0    0    0
<6>[ 1165.590363][    C2] |_mmcblk0    F i2c[  8- 8192ms]:    9    1    0    0    0    0    0    0    0    0    0    0
<6>[ 1165.590387][    C2] |_mmcblk0    D complete:      4 request      176 sectors
<6>[ 1165.590398][    C2] |_mmcblk0    D i2i[  8- 8192ms]:    3    1    0    0    0    0    0    0    0    0    0    0
<6>[ 1165.590421][    C2] |_mmcblk0    D i2c[  8- 8192ms]:    4    0    0    0    0    0    0    0    0    0    0    0
<6>[ 1165.590444][    C2] |_loop       total complete 3014 requests
<6>[ 1165.590453][    C2] |_loop       R complete:   3014 request    27160 sectors
<6>[ 1165.590464][    C2] |_loop       R i2i[  8- 8192ms]: 3014    0    0    0    0    0    0    0    0    0    0    0
<6>[ 1165.590487][    C2] |_loop       R i2c[  8- 8192ms]: 2425  219  161  113   92    4    0    0    0    0    0    0
<3>[ 1165.592300][T12251@C2] |__i2s     mmc0: 3063   21   41   89   38    2    1    0    0    0    0    0    0    0
<3>[ 1165.592347][T12251@C2] |__i2e     mmc0: 2918   34   71  154   74    2    3    0    0    0    0    0    0    0
<3>[ 1165.592373][T12251@C2] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 1, cmdq_cnt= 0
<3>[ 1165.592389][T12251@C2] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<3>[ 1165.832683][T12599@C1] |__c2e     mmc0: 2268    1    0    1    0    0    0    0    0    0    0    0    0    0
<3>[ 1165.832793][T12599@C1] |__d2e     mmc0: 2214    6    4    6    0    0    1    0    0    0    0    0    0    0
<3>[ 1165.832854][T12599@C1] |__blocks  mmc0:    4    0    0    0 2177   16   11    8    7    3    4    1    0    0
<3>[ 1165.833011][T12599@C1] |__speed   mmc0: r= 18.62M/s, w= 64.26M/s, r_blk= 19104, w_blk= 6720
<3>[ 1166.059471][T10601@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=268914,calib_resistance_vol=-86,vol_adc_mv=269
<6>[ 1166.064042][T10601@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sprd normal_cap_diff = -1, adjust_step = 641, temp_cap = 1000, smooth_cap_diff = -10, *cap = 991, smooth_soc = 983, smooth_soc_decimal = 0
<6>[ 1166.064279][T10601@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 49958208, start_work_clbcnt = 49958208, cur_clbcnt = 49131735, cur_1000ma_adc = 1380, vol_1000mv_adc = 690, calib_resist = 10000
<6>[ 1166.064305][T10601@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 1000, init_mah = 0, normal_cap = 983, data->cc_mah = -83, Tbat = 314, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[ 1166.064330][T10601@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 4384000, vbatt_mv = 4375, vbat_cur_ma = -86, vbat_avg_mv = 4373, vbat_cur_avg_ma = -102, absolute_charger_mode = 0, full_percent = 0
<6>[ 1166.064355][T10601@C7] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 983, cycle = 15
<14>[ 1166.567664][ T1@C5] init: swapoff() took 4451ms
<6>[ 1166.568953][ T1@C5] zram0: detected capacity change from 4695536 to 0
<14>[ 1166.611385][ T1@C6] init: Ready to unmount apexes. So far shutdown sequence took 11957ms
<14>[ 1166.812566][T12614@C6] apexd: Started. subcommand = --unmount-all
<14>[ 1166.814960][T12614@C7] apexd-unmount-all: Populating APEX database from mounts...
<14>[ 1166.817506][T12614@C7] apexd-unmount-all: Found "/apex/com.android.btservices@352090000" backed by file /system/apex/com.android.btservices.apex
<14>[ 1166.817880][T12614@C7] apexd-unmount-all: Found "/apex/com.android.wifi@351610000" backed by file /system/apex/com.google.android.wifi.apex
<14>[ 1166.818240][T12614@C7] apexd-unmount-all: Found "/apex/com.android.adbd@351010000" backed by file /system/apex/com.google.android.adbd.apex
<14>[ 1166.818585][T12614@C7] apexd-unmount-all: Found "/apex/com.android.media.swcodec@351504000" backed by file /system/apex/com.google.android.media.swcodec.apex
<14>[ 1166.818912][T12614@C7] apexd-unmount-all: Found "/apex/com.android.nfcservices@352090000" backed by file /system/apex/com.android.nfcservices.apex
<14>[ 1166.819232][T12614@C7] apexd-unmount-all: Found "/apex/com.android.virt@2" backed by file /system/apex/com.android.virt.apex
<14>[ 1166.819571][T12614@C7] apexd-unmount-all: Found "/apex/com.android.tzdata@351400020" backed by file /system/apex/com.google.android.tzdata6.apex
<14>[ 1166.819905][T12614@C7] apexd-unmount-all: Found "/apex/com.android.conscrypt@351412000" backed by file /system/apex/com.google.android.conscrypt.apex
<14>[ 1166.820244][T12614@C7] apexd-unmount-all: Found "/apex/com.android.i18n@1" backed by file /system/apex/com.android.i18n.apex
<14>[ 1166.820562][T12614@C7] apexd-unmount-all: Found "/apex/com.android.runtime@1" backed by file /system/apex/com.android.runtime.apex
<14>[ 1166.820940][T12614@C7] apexd-unmount-all: Found "/apex/com.android.cellbroadcast@351511000" backed by file /system/apex/com.google.android.cellbroadcast.apex
<14>[ 1166.821285][T12614@C7] apexd-unmount-all: Found "/apex/com.android.neuralnetworks@351010040" backed by file /system/apex/com.google.android.neuralnetworks.apex
<14>[ 1166.821631][T12614@C7] apexd-unmount-all: Found "/apex/com.android.appsearch@351412000" backed by file /system/apex/com.google.android.appsearch.apex
<14>[ 1166.821966][T12614@C7] apexd-unmount-all: Found "/apex/com.android.profiling@352090000" backed by file /system/apex/com.android.profiling.apex
<14>[ 1166.822304][T12614@C7] apexd-unmount-all: Found "/apex/com.android.apex.cts.shim@1" backed by file /system/apex/com.android.apex.cts.shim.apex
<14>[ 1166.822669][T12614@C7] apexd-unmount-all: Found "/apex/com.android.configinfrastructure@351010000" backed by file /system/apex/com.google.android.configinfrastructure.apex
<14>[ 1166.823288][T12614@C7] apexd-unmount-all: Found "/apex/com.android.devicelock@342410000" backed by file /system/apex/com.google.android.devicelock.apex
<14>[ 1166.823647][T12614@C7] apexd-unmount-all: Found "/apex/com.android.adservices@351537040" backed by file /system/apex/com.google.android.adservices.apex
<14>[ 1166.823983][T12614@C7] apexd-unmount-all: Found "/apex/com.android.healthfitness@351511060" backed by file /system/apex/com.google.android.healthfitness.apex
<14>[ 1166.824317][T12614@C7] apexd-unmount-all: Found "/apex/com.android.ipsec@351410000" backed by file /system/apex/com.google.android.ipsec.apex
<14>[ 1166.824641][T12614@C7] apexd-unmount-all: Found "/apex/com.android.media@351504000" backed by file /system/apex/com.google.android.media.apex
<14>[ 1166.825120][T12614@C7] apexd-unmount-all: Found "/apex/com.android.rkpd@351310000" backed by file /system/apex/com.google.android.rkpd.apex
<14>[ 1166.825475][T12614@C7] apexd-unmount-all: Found "/apex/com.android.os.statsd@351610000" backed by file /system/apex/com.google.android.os.statsd.apex
<14>[ 1166.825850][T12614@C7] apexd-unmount-all: Found "/apex/com.android.extservices@351538083" backed by file /system/apex/com.google.android.extservices_tplus.apex
<14>[ 1166.826178][T12614@C7] apexd-unmount-all: Found "/apex/com.android.vndk.v33@1" backed by file /system_ext/apex/com.android.vndk.v33.apex
<14>[ 1166.826514][T12614@C7] apexd-unmount-all: Found "/apex/com.android.uwb@351310040" backed by file /system/apex/com.google.android.uwb.apex
<14>[ 1166.826856][T12614@C7] apexd-unmount-all: Found "/apex/com.android.mediaprovider@351613160" backed by file /system/apex/com.google.android.mediaprovider.apex
<14>[ 1166.827183][T12614@C7] apexd-unmount-all: Found "/apex/com.android.art@351610080" backed by file /system/apex/com.google.android.art.apex
<14>[ 1166.827534][T12614@C7] apexd-unmount-all: Found "/apex/com.android.permission@351610020" backed by file /system/apex/com.google.android.permission.apex
<14>[ 1166.827867][T12614@C7] apexd-unmount-all: Found "/apex/com.android.compos@2" backed by file /system_ext/apex/com.android.compos.apex
<14>[ 1166.828208][T12614@C7] apexd-unmount-all: Found "/apex/com.google.mainline.primary.libs@351165000" backed by file /system/apex/com.google.mainline.primary.libs.apex
<14>[ 1166.828545][T12614@C7] apexd-unmount-all: Found "/apex/com.android.sdkext@351415000" backed by file /system/apex/com.google.android.sdkext.apex
<14>[ 1166.828933][T12614@C7] apexd-unmount-all: Found "/apex/com.android.resolv@351510000" backed by file /system/apex/com.google.android.resolv.apex
<14>[ 1166.829272][T12614@C7] apexd-unmount-all: Found "/apex/com.android.tethering@351510080" backed by file /system/apex/com.google.android.tethering.apex
<14>[ 1166.829611][T12614@C7] apexd-unmount-all: Found "/apex/com.android.ondevicepersonalization@351541000" backed by file /system/apex/com.google.android.ondevicepersonalization.apex
<14>[ 1166.829933][T12614@C7] apexd-unmount-all: Found "/apex/com.android.scheduling@351010000" backed by file /system/apex/com.google.android.scheduling.apex
<14>[ 1166.830320][T12614@C7] apexd-unmount-all: 36 packages restored.
<14>[ 1166.830388][T12614@C7] apexd-unmount-all: Unmounting /system/apex/com.google.android.adbd.apex mounted on /apex/com.android.adbd@351010000
<14>[ 1166.860850][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.adservices.apex mounted on /apex/com.android.adservices@351537040
<14>[ 1166.906009][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.android.apex.cts.shim.apex mounted on /apex/com.android.apex.cts.shim@1
<14>[ 1166.947470][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.appsearch.apex mounted on /apex/com.android.appsearch@351412000
<14>[ 1166.987799][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.art.apex mounted on /apex/com.android.art@351610080
<14>[ 1167.034070][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.android.btservices.apex mounted on /apex/com.android.btservices@352090000
<14>[ 1167.075728][T12614@C7] apexd-unmount-all: Unmounting /system/apex/com.google.android.cellbroadcast.apex mounted on /apex/com.android.cellbroadcast@351511000
<14>[ 1167.121868][T12614@C6] apexd-unmount-all: Unmounting /system_ext/apex/com.android.compos.apex mounted on /apex/com.android.compos@2
<14>[ 1167.155675][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.configinfrastructure.apex mounted on /apex/com.android.configinfrastructure@351010000
<14>[ 1167.188591][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.conscrypt.apex mounted on /apex/com.android.conscrypt@351412000
<14>[ 1167.226140][T12614@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.devicelock.apex mounted on /apex/com.android.devicelock@342410000
<14>[ 1167.275946][T12614@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.extservices_tplus.apex mounted on /apex/com.android.extservices@351538083
<14>[ 1167.316275][T12614@C5] apexd-unmount-all: Unmounting /system/apex/com.google.android.healthfitness.apex mounted on /apex/com.android.healthfitness@351511060
<6>[ 1167.343666][T116@C1] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[ 1167.343772][T116@C1] WCN BASE: tx:at+loopcheck=1167337,279439
<6>[ 1167.343772][T116@C1] 
<6>[ 1167.344121][T116@C1] WCN BASE: mdbg_tx_cb, chn:0
<6>[ 1167.352277][T364@C1] WCN BASE: : rx:loopcheck_ack:ap_send=1167337,cp2_bootup=279439,cp2_send=1167793
<6>[ 1167.352277][T364@C1] 
<14>[ 1167.372090][T12614@C3] apexd-unmount-all: Unmounting /system/apex/com.android.i18n.apex mounted on /apex/com.android.i18n@1
<14>[ 1167.427355][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.ipsec.apex mounted on /apex/com.android.ipsec@351410000
<14>[ 1167.495949][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.media.apex mounted on /apex/com.android.media@351504000
<14>[ 1167.547041][T12614@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.media.swcodec.apex mounted on /apex/com.android.media.swcodec@351504000
<14>[ 1167.599460][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.mediaprovider.apex mounted on /apex/com.android.mediaprovider@351613160
<14>[ 1167.634871][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.neuralnetworks.apex mounted on /apex/com.android.neuralnetworks@351010040
<14>[ 1167.672465][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.android.nfcservices.apex mounted on /apex/com.android.nfcservices@352090000
<14>[ 1167.699873][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.ondevicepersonalization.apex mounted on /apex/com.android.ondevicepersonalization@351541000
<14>[ 1167.736855][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.os.statsd.apex mounted on /apex/com.android.os.statsd@351610000
<14>[ 1167.776321][T12614@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.permission.apex mounted on /apex/com.android.permission@351610020
<14>[ 1167.843392][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.android.profiling.apex mounted on /apex/com.android.profiling@352090000
<14>[ 1167.879732][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.resolv.apex mounted on /apex/com.android.resolv@351510000
<14>[ 1167.919907][T12614@C7] apexd-unmount-all: Unmounting /system/apex/com.google.android.rkpd.apex mounted on /apex/com.android.rkpd@351310000
<14>[ 1167.952477][T12614@C7] apexd-unmount-all: Unmounting /system/apex/com.android.runtime.apex mounted on /apex/com.android.runtime@1
<11>[ 1167.956164][T12614@C6] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.runtime: Device or resource busy
<14>[ 1167.956227][T12614@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.scheduling.apex mounted on /apex/com.android.scheduling@351010000
<14>[ 1167.983463][T12614@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.sdkext.apex mounted on /apex/com.android.sdkext@351415000
<14>[ 1168.020711][T12614@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.tethering.apex mounted on /apex/com.android.tethering@351510080
<14>[ 1168.067970][T12614@C1] apexd-unmount-all: Unmounting /system/apex/com.google.android.tzdata6.apex mounted on /apex/com.android.tzdata@351400020
<14>[ 1168.120195][T12614@C1] apexd-unmount-all: Unmounting /system/apex/com.google.android.uwb.apex mounted on /apex/com.android.uwb@351310040
<14>[ 1168.159125][T12614@C3] apexd-unmount-all: Unmounting /system/apex/com.android.virt.apex mounted on /apex/com.android.virt@2
<14>[ 1168.196345][T12614@C2] apexd-unmount-all: Unmounting /system_ext/apex/com.android.vndk.v33.apex mounted on /apex/com.android.vndk.v33@1
<11>[ 1168.203495][T12614@C1] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.vndk.v33: Device or resource busy
<14>[ 1168.203588][T12614@C1] apexd-unmount-all: Unmounting /system/apex/com.google.android.wifi.apex mounted on /apex/com.android.wifi@351610000
<14>[ 1168.251417][T12614@C0] apexd-unmount-all: Unmounting /system/apex/com.google.mainline.primary.libs.apex mounted on /apex/com.google.mainline.primary.libs@351165000
<14>[ 1168.319330][ T1@C6] apexd: apexd terminated by exit(1)
<14>[ 1168.319330][ T1@C6] 
<11>[ 1168.319815][ T1@C6] init: '/system/bin/apexd --unmount-all' failed : 256
<14>[ 1168.344616][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/data_ce/null/0 opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.345489][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/data_ce/null/0 opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.345541][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/ref_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.346234][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/ref_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.346280][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/cur_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.347006][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/cur_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.347031][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/storage_area opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.347607][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/storage_area opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.347631][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/misc_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.348309][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/misc_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.348463][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/misc_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.349066][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/misc_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.349090][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/data_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.349636][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/data_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.349660][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/data_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.350329][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/data_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1168.350372][ T1@C7] init: Unmounting /dev/block/dm-50:/data opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<6>[ 1168.361567][T152@C4] [SPRD_PDBG] #---------PDBG LIGHT SLEEP START---------#
<6>[ 1168.361710][T152@C4] [SPRD_PDBG] Pending Wakeup Sources: sipc-lte-mpm-5 
<6>[ 1168.361787][T152@C4] [SPRD_PDBG] [SLP_STATE] deep: 0xfffffffffffffffc, light: 0xfffffffffffffffc
<6>[ 1168.361859][T152@C4] [SPRD_PDBG] [EB_INFO  ] ap1: 0xfffffffffefffffe, ap2: 0xffffffffffffffff, aon1: 0xffffffe42003ef0a, aon2: 0xffffffff1200007c
<6>[ 1168.361914][T152@C4] [SPRD_PDBG] [PD_INFO  ] 0xfffffffffffffb70
<6>[ 1168.361977][T152@C4] [SPRD_PDBG] [DEEP_CNT ]     0,     0,   181,   142,   100,    17,   253, 
<6>[ 1168.362027][T152@C4] [SPRD_PDBG] [LIGHT_CNT]   120, 
<6>[ 1168.362079][T152@C4] [SPRD_PDBG] #---------PDBG LIGHT SLEEP END-----------#
<6>[ 1169.138594][T12082@C4] charger-manager charger-manager: vbat: 4374000, vbat_avg: 4374000, OCV: 4385000, ibat: -111000, ibat_avg: -104000, ibus: -22, vbus: 0, msoc: 983, chg_sts: 2, frce_full: 0, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 0, Tboard: 357, Tbatt: 314, thm_cur: 6000000, thm_pwr: 30000, is_fchg: 0, fchg_en: 0, tflush: 272, tperiod: 15
<6>[ 1169.141007][T12082@C4] charger-manager charger-manager: new_uisoc = 983, old_uisoc = 984, work_cycle = 15s, cap_one_time = 30s
<6>[ 1170.409252][    C6] sprd-mpm: smsg-3-5-rx is awake, awake_cnt = 1756
<6>[ 1170.409252][    C6] sbuf-3-5-0-rx is awake, awake_cnt = 1748
<6>[ 1170.409252][    C6] smsg-3-11-rx is awake, awake_cnt = 185
<6>[ 1170.409252][    C6] sbuf-3-11-0-rx is awake, awake_cnt = 177
<6>[ 1170.409252][    C6] 
<6>[ 1170.409366][    C6] sprd-mpm: smsg-5-5-rx is awake, awake_cnt = 9097
<6>[ 1170.409366][    C6] smsg-5-6-rx is awake, awake_cnt = 1010
<6>[ 1170.409366][    C6] sbuf-5-6-5-rx is awake, awake_cnt = 28
<6>[ 1170.409366][    C6] sbuf-5-6-4-rx is awake, awake_cnt = 397
<6>[ 1170.409366][    C6] sbuf-5-6-3-rx is awake, awake_cnt = 3
<6>[ 1170.409366][    C6] sbuf-5-6-2-rx is awake, awake_cnt = 57
<6>[ 1170.409366][    C6] sbuf-5-6-1-rx is awak
<6>[ 1170.409401][    C6] sprd-mpm: smsg-6-22-rx is awake, awake_cnt = 94
<6>[ 1170.409401][    C6] smsg-6-5-rx is awake, awake_cnt = 38999
<6>[ 1170.409401][    C6] sbuf-6-5-0-rx is awake, awake_cnt = 38997
<6>[ 1170.409401][    C6] smsg-6-4-rx is awake, awake_cnt = 11
<6>[ 1170.409401][    C6] sbuf-6-4-0-rx is awake, awake_cnt = 9
<6>[ 1170.409401][    C6] 
<3>[ 1170.835024][T12599@C1] |__c2e     mmc0: 2606    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[ 1170.835131][T12599@C1] |__d2e     mmc0:  638   83  553   16    0    0    0    0    0    0    0    0    0    0
<3>[ 1170.835195][T12599@C1] |__blocks  mmc0:  639    0    0    0  516   29   33   48   25    0    0    0    0    0
<3>[ 1170.835256][T12599@C1] |__speed   mmc0: r= 32.25M/s, w= 28.16M/s, r_blk= 13808, w_blk= 704
<6>[ 1172.463619][T116@C3] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[ 1172.463702][T116@C3] WCN BASE: tx:at+loopcheck=1172457,279439
<6>[ 1172.463702][T116@C3] 
<6>[ 1172.464051][T116@C3] WCN BASE: mdbg_tx_cb, chn:0
<6>[ 1172.472142][T364@C1] WCN BASE: : rx:loopcheck_ack:ap_send=1172457,cp2_bootup=279439,cp2_send=1172913
<6>[ 1172.472142][T364@C1] 
<14>[ 1173.710762][ T1@C6] init: Umounted /dev/block/dm-50:/data opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1173.710821][ T1@C6] init: Unmounting /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1173.927294][ T1@C6] init: Umounted /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1173.927360][ T1@C6] init: Unmounting /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1173.975272][ T1@C7] init: Umounted /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1173.975377][ T1@C7] init: Unmounting /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[ 1173.987408][ T1@C7] init: Umounted /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[ 1173.987474][ T1@C7] init: Unmounting /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1174.035143][ T1@C7] init: Umounted /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1174.035210][ T1@C7] init: Pause reboot monitor thread before fsck
<14>[ 1174.045603][T12576@C1] init: remaining_shutdown_time: 286
<14>[ 1174.139659][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.139659][ T1@C6] 
<14>[ 1174.139716][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.139716][ T1@C6] 
<14>[ 1174.139743][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.139743][ T1@C6] 
<14>[ 1174.139772][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.139772][ T1@C6] 
<14>[ 1174.139799][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.139799][ T1@C6] 
<14>[ 1174.139824][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.139824][ T1@C6] 
<14>[ 1174.139852][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.139852][ T1@C6] 
<14>[ 1174.139882][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.139882][ T1@C6] 
<14>[ 1174.139909][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.139909][ T1@C6] 
<14>[ 1174.139935][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.139935][ T1@C6] 
<14>[ 1174.139960][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.139960][ T1@C6] 
<14>[ 1174.139985][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.139985][ T1@C6] 
<14>[ 1174.140012][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.140012][ T1@C6] 
<14>[ 1174.140038][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.140038][ T1@C6] 
<14>[ 1174.140063][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000070c23ad030,cp1_version:0x2e6e9945
<14>[ 1174.140063][ T1@C6] 
<14>[ 1174.140089][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.140089][ T1@C6] 
<14>[ 1174.140114][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.140114][ T1@C6] 
<14>[ 1174.140139][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000070c23ac020,cp2_version:0x2e6e9944
<14>[ 1174.140139][ T1@C6] 
<14>[ 1174.140165][ T1@C6] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.140165][ T1@C6] 
<14>[ 1174.140189][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1160, prev: 0
<14>[ 1174.140189][ T1@C6] 
<14>[ 1174.140214][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.140214][ T1@C6] 
<14>[ 1174.140239][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.140239][ T1@C6] 
<14>[ 1174.140265][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.140265][ T1@C6] 
<14>[ 1174.234685][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.234685][ T1@C6] 
<14>[ 1174.234723][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.234723][ T1@C6] 
<14>[ 1174.234738][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.234738][ T1@C6] 
<14>[ 1174.234750][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.234750][ T1@C6] 
<14>[ 1174.234763][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.234763][ T1@C6] 
<14>[ 1174.234775][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.234775][ T1@C6] 
<14>[ 1174.234788][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.234788][ T1@C6] 
<14>[ 1174.234801][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.234801][ T1@C6] 
<14>[ 1174.234815][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.234815][ T1@C6] 
<14>[ 1174.234827][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.234827][ T1@C6] 
<14>[ 1174.234839][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.234839][ T1@C6] 
<14>[ 1174.234851][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.234851][ T1@C6] 
<14>[ 1174.234863][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.234863][ T1@C6] 
<14>[ 1174.234875][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.234875][ T1@C6] 
<14>[ 1174.234887][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb40000759e8ee010,cp1_version:0x2e6e9945
<14>[ 1174.234887][ T1@C6] 
<14>[ 1174.234899][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.234899][ T1@C6] 
<14>[ 1174.234912][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.234912][ T1@C6] 
<14>[ 1174.234924][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb40000759e8f0030,cp2_version:0x2e6e9944
<14>[ 1174.234924][ T1@C6] 
<14>[ 1174.234935][ T1@C6] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.234935][ T1@C6] 
<14>[ 1174.234947][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.234947][ T1@C6] 
<14>[ 1174.234959][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.234959][ T1@C6] 
<14>[ 1174.234971][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.234971][ T1@C6] 
<14>[ 1174.234983][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.234983][ T1@C6] 
<14>[ 1174.322087][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.322087][ T1@C6] 
<14>[ 1174.322126][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.322126][ T1@C6] 
<14>[ 1174.322140][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.322140][ T1@C6] 
<14>[ 1174.322153][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.322153][ T1@C6] 
<14>[ 1174.322165][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.322165][ T1@C6] 
<14>[ 1174.322177][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.322177][ T1@C6] 
<14>[ 1174.322191][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.322191][ T1@C6] 
<14>[ 1174.322203][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.322203][ T1@C6] 
<14>[ 1174.322217][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.322217][ T1@C6] 
<14>[ 1174.322229][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.322229][ T1@C6] 
<14>[ 1174.322241][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.322241][ T1@C6] 
<14>[ 1174.322253][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.322253][ T1@C6] 
<14>[ 1174.322265][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.322265][ T1@C6] 
<14>[ 1174.322278][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.322278][ T1@C6] 
<14>[ 1174.322290][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb40000735db31040,cp1_version:0x2e6e9945
<14>[ 1174.322290][ T1@C6] 
<14>[ 1174.322302][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.322302][ T1@C6] 
<14>[ 1174.322313][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.322313][ T1@C6] 
<14>[ 1174.322326][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb40000735db2e010,cp2_version:0x2e6e9944
<14>[ 1174.322326][ T1@C6] 
<14>[ 1174.322338][ T1@C6] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.322338][ T1@C6] 
<14>[ 1174.322349][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.322349][ T1@C6] 
<14>[ 1174.322361][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.322361][ T1@C6] 
<14>[ 1174.322373][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.322373][ T1@C6] 
<14>[ 1174.322385][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.322385][ T1@C6] 
<14>[ 1174.411299][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.411299][ T1@C7] 
<14>[ 1174.411339][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.411339][ T1@C7] 
<14>[ 1174.411352][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[ 1174.411352][ T1@C7] 
<14>[ 1174.411366][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.411366][ T1@C7] 
<14>[ 1174.411378][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[ 1174.411378][ T1@C7] 
<14>[ 1174.411391][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.411391][ T1@C7] 
<14>[ 1174.411404][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.411404][ T1@C7] 
<14>[ 1174.411418][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.411418][ T1@C7] 
<14>[ 1174.411431][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.411431][ T1@C7] 
<14>[ 1174.411443][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.411443][ T1@C7] 
<14>[ 1174.411455][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.411455][ T1@C7] 
<14>[ 1174.411467][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.411467][ T1@C7] 
<14>[ 1174.411480][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.411480][ T1@C7] 
<14>[ 1174.411492][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.411492][ T1@C7] 
<14>[ 1174.411504][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000077db3b3030,cp1_version:0x2e6e9945
<14>[ 1174.411504][ T1@C7] 
<14>[ 1174.411516][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.411516][ T1@C7] 
<14>[ 1174.411528][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.411528][ T1@C7] 
<14>[ 1174.411540][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000077db3b1010,cp2_version:0x2e6e9944
<14>[ 1174.411540][ T1@C7] 
<14>[ 1174.411553][ T1@C7] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.411553][ T1@C7] 
<14>[ 1174.411565][ T1@C7] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.411565][ T1@C7] 
<14>[ 1174.411577][ T1@C7] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.411577][ T1@C7] 
<14>[ 1174.411589][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.411589][ T1@C7] 
<14>[ 1174.411600][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[ 1174.411600][ T1@C7] 
<14>[ 1174.499647][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.499647][ T1@C6] 
<14>[ 1174.499685][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.499685][ T1@C6] 
<14>[ 1174.499699][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.499699][ T1@C6] 
<14>[ 1174.499713][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.499713][ T1@C6] 
<14>[ 1174.499726][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.499726][ T1@C6] 
<14>[ 1174.499738][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.499738][ T1@C6] 
<14>[ 1174.499751][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.499751][ T1@C6] 
<14>[ 1174.499765][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.499765][ T1@C6] 
<14>[ 1174.499780][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.499780][ T1@C6] 
<14>[ 1174.499793][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.499793][ T1@C6] 
<14>[ 1174.499805][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.499805][ T1@C6] 
<14>[ 1174.499817][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.499817][ T1@C6] 
<14>[ 1174.499829][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.499829][ T1@C6] 
<14>[ 1174.499841][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.499841][ T1@C6] 
<14>[ 1174.499853][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000076d8453040,cp1_version:0x2e6e9945
<14>[ 1174.499853][ T1@C6] 
<14>[ 1174.499866][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.499866][ T1@C6] 
<14>[ 1174.499878][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.499878][ T1@C6] 
<14>[ 1174.499890][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000076d8450010,cp2_version:0x2e6e9944
<14>[ 1174.499890][ T1@C6] 
<14>[ 1174.499902][ T1@C6] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.499902][ T1@C6] 
<14>[ 1174.499914][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.499914][ T1@C6] 
<14>[ 1174.499926][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.499926][ T1@C6] 
<14>[ 1174.499937][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.499937][ T1@C6] 
<14>[ 1174.499950][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.499950][ T1@C6] 
<14>[ 1174.591197][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.591197][ T1@C7] 
<14>[ 1174.591238][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.591238][ T1@C7] 
<14>[ 1174.591251][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[ 1174.591251][ T1@C7] 
<14>[ 1174.591264][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.591264][ T1@C7] 
<14>[ 1174.591277][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[ 1174.591277][ T1@C7] 
<14>[ 1174.591289][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.591289][ T1@C7] 
<14>[ 1174.591303][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.591303][ T1@C7] 
<14>[ 1174.591318][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.591318][ T1@C7] 
<14>[ 1174.591332][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.591332][ T1@C7] 
<14>[ 1174.591345][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.591345][ T1@C7] 
<14>[ 1174.591357][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.591357][ T1@C7] 
<14>[ 1174.591368][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.591368][ T1@C7] 
<14>[ 1174.591382][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.591382][ T1@C7] 
<14>[ 1174.591394][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.591394][ T1@C7] 
<14>[ 1174.591406][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000071b9650030,cp1_version:0x2e6e9945
<14>[ 1174.591406][ T1@C7] 
<14>[ 1174.591418][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.591418][ T1@C7] 
<14>[ 1174.591430][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.591430][ T1@C7] 
<14>[ 1174.591442][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000071b964e010,cp2_version:0x2e6e9944
<14>[ 1174.591442][ T1@C7] 
<14>[ 1174.591454][ T1@C7] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.591454][ T1@C7] 
<14>[ 1174.591466][ T1@C7] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.591466][ T1@C7] 
<14>[ 1174.591477][ T1@C7] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.591477][ T1@C7] 
<14>[ 1174.591489][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.591489][ T1@C7] 
<14>[ 1174.591501][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[ 1174.591501][ T1@C7] 
<14>[ 1174.686182][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.686182][ T1@C6] 
<14>[ 1174.686225][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.686225][ T1@C6] 
<14>[ 1174.686240][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.686240][ T1@C6] 
<14>[ 1174.686255][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.686255][ T1@C6] 
<14>[ 1174.686270][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.686270][ T1@C6] 
<14>[ 1174.686284][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.686284][ T1@C6] 
<14>[ 1174.686299][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.686299][ T1@C6] 
<14>[ 1174.686313][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.686313][ T1@C6] 
<14>[ 1174.686328][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.686328][ T1@C6] 
<14>[ 1174.686342][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.686342][ T1@C6] 
<14>[ 1174.686357][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.686357][ T1@C6] 
<14>[ 1174.686370][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.686370][ T1@C6] 
<14>[ 1174.686384][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.686384][ T1@C6] 
<14>[ 1174.686399][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.686399][ T1@C6] 
<14>[ 1174.686412][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400006eaa8cf010,cp1_version:0x2e6e9945
<14>[ 1174.686412][ T1@C6] 
<14>[ 1174.686426][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.686426][ T1@C6] 
<14>[ 1174.686440][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.686440][ T1@C6] 
<14>[ 1174.686453][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400006eaa8d2040,cp2_version:0x2e6e9944
<14>[ 1174.686453][ T1@C6] 
<14>[ 1174.686467][ T1@C6] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.686467][ T1@C6] 
<14>[ 1174.686481][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.686481][ T1@C6] 
<14>[ 1174.686494][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.686494][ T1@C6] 
<14>[ 1174.686508][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.686508][ T1@C6] 
<14>[ 1174.686522][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.686522][ T1@C6] 
<14>[ 1174.772656][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.772656][ T1@C7] 
<14>[ 1174.772697][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.772697][ T1@C7] 
<14>[ 1174.772710][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[ 1174.772710][ T1@C7] 
<14>[ 1174.772724][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.772724][ T1@C7] 
<14>[ 1174.772736][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[ 1174.772736][ T1@C7] 
<14>[ 1174.772748][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.772748][ T1@C7] 
<14>[ 1174.772762][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.772762][ T1@C7] 
<14>[ 1174.772775][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.772775][ T1@C7] 
<14>[ 1174.772789][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.772789][ T1@C7] 
<14>[ 1174.772802][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.772802][ T1@C7] 
<14>[ 1174.772814][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.772814][ T1@C7] 
<14>[ 1174.772826][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.772826][ T1@C7] 
<14>[ 1174.772838][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.772838][ T1@C7] 
<14>[ 1174.772850][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.772850][ T1@C7] 
<14>[ 1174.772907][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007a6fc0f020,cp1_version:0x2e6e9945
<14>[ 1174.772907][ T1@C7] 
<14>[ 1174.772921][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.772921][ T1@C7] 
<14>[ 1174.772933][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.772933][ T1@C7] 
<14>[ 1174.772945][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007a6fc11040,cp2_version:0x2e6e9944
<14>[ 1174.772945][ T1@C7] 
<14>[ 1174.772958][ T1@C7] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.772958][ T1@C7] 
<14>[ 1174.772970][ T1@C7] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.772970][ T1@C7] 
<14>[ 1174.772984][ T1@C7] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.772984][ T1@C7] 
<14>[ 1174.772996][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.772996][ T1@C7] 
<14>[ 1174.773009][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[ 1174.773009][ T1@C7] 
<14>[ 1174.863997][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.863997][ T1@C6] 
<14>[ 1174.864044][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.864044][ T1@C6] 
<14>[ 1174.864062][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.864062][ T1@C6] 
<14>[ 1174.864079][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.864079][ T1@C6] 
<14>[ 1174.864097][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.864097][ T1@C6] 
<14>[ 1174.864114][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.864114][ T1@C6] 
<14>[ 1174.864133][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.864133][ T1@C6] 
<14>[ 1174.864151][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[ 1174.864151][ T1@C6] 
<14>[ 1174.864170][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.864170][ T1@C6] 
<14>[ 1174.864186][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.864186][ T1@C6] 
<14>[ 1174.864202][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.864202][ T1@C6] 
<14>[ 1174.864218][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[ 1174.864218][ T1@C6] 
<14>[ 1174.864234][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.864234][ T1@C6] 
<14>[ 1174.864250][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x2e6e9945 ubc:0xb54800, vbc:0x158f73
<14>[ 1174.864250][ T1@C6] 
<14>[ 1174.864267][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb40000739d232020,cp1_version:0x2e6e9945
<14>[ 1174.864267][ T1@C6] 
<14>[ 1174.864282][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.864282][ T1@C6] 
<14>[ 1174.864299][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x2e6e9944 ubc:0xb54800, vbc:0x158fb1
<14>[ 1174.864299][ T1@C6] 
<14>[ 1174.864315][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb40000739d231010,cp2_version:0x2e6e9944
<14>[ 1174.864315][ T1@C6] 
<14>[ 1174.864331][ T1@C6] fsck.f2fs: Info: CKPT version = 2e6e9945
<14>[ 1174.864331][ T1@C6] 
<14>[ 1174.864347][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1160, prev: 1160
<14>[ 1174.864347][ T1@C6] 
<14>[ 1174.864362][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.864362][ T1@C6] 
<14>[ 1174.864378][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[ 1174.864378][ T1@C6] 
<14>[ 1174.864394][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.864394][ T1@C6] 
<5>[ 1174.938362][T91@C1] audit: type=1400 audit(1735701469.171:622): avc:  denied  { getattr } for  pid=12624 comm="fsck.f2fs" path="/sys/devices/platform/soc/soc:ap-apb/201d0000.sdio/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p48/partition" dev="sysfs" ino=38654 scontext=u:r:fsck:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
<14>[ 1174.952983][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1174.952983][ T1@C6] 
<14>[ 1174.953031][ T1@C6] fsck.f2fs: 	Info: can't find /sys, assuming normal block device
<14>[ 1174.953031][ T1@C6] 
<14>[ 1174.953052][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1174.953052][ T1@C6] 
<14>[ 1174.953071][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1174.953071][ T1@C6] 
<14>[ 1174.953091][ T1@C6] fsck.f2fs:   "5.15.0-139-generic"
<14>[ 1174.953091][ T1@C6] 
<14>[ 1174.953110][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1174.953110][ T1@C6] 
<14>[ 1174.953129][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.953129][ T1@C6] 
<14>[ 1174.953149][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1174.953149][ T1@C6] 
<14>[ 1174.953169][ T1@C6] fsck.f2fs: Info: superblock features = 499 :  encrypt extra_attr project_quota quota verity
<14>[ 1174.953169][ T1@C6] 
<14>[ 1174.953189][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1174.953189][ T1@C6] 
<14>[ 1174.953208][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1174.953208][ T1@C6] 
<14>[ 1174.953227][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1174.953227][ T1@C6] 
<14>[ 1174.953246][ T1@C6] fsck.f2fs: Info: total FS sectors = 1024000 (500 MB)
<14>[ 1174.953246][ T1@C6] 
<14>[ 1174.953265][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x31da8a23 ubc:0x18200, vbc:0xdb
<14>[ 1174.953265][ T1@C6] 
<14>[ 1174.953284][ T1@C6] fsck.f2fs: cp_addr: 207, cur_version:0x31da8a23 ubc:0x18200, vbc:0xdb
<14>[ 1174.953284][ T1@C6] 
<14>[ 1174.953304][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007884dcf020,cp1_version:0x31da8a23
<14>[ 1174.953304][ T1@C6] 
<14>[ 1174.953323][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x31da8a22 ubc:0x18200, vbc:0xdb
<14>[ 1174.953323][ T1@C6] 
<14>[ 1174.953342][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x31da8a22 ubc:0x18200, vbc:0xdb
<14>[ 1174.953342][ T1@C6] 
<14>[ 1174.953361][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007884dd0030,cp2_version:0x31da8a22
<14>[ 1174.953361][ T1@C6] 
<14>[ 1174.953380][ T1@C6] fsck.f2fs: Info: CKPT version = 31da8a23
<14>[ 1174.953380][ T1@C6] 
<14>[ 1174.953399][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1168, prev: 0
<14>[ 1174.953399][ T1@C6] 
<14>[ 1174.953418][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1174.953418][ T1@C6] 
<14>[ 1174.953438][ T1@C6] fsck.f2fs: Info: checkpoint state = c1 :  nat_bits crc unmount
<14>[ 1174.953438][ T1@C6] 
<14>[ 1174.953456][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1174.953456][ T1@C6] 
<5>[ 1175.020202][T91@C1] audit: type=1400 audit(1735701469.251:623): avc:  denied  { getattr } for  pid=12625 comm="fsck.f2fs" path="/sys/devices/platform/soc/soc:ap-apb/201d0000.sdio/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p47/partition" dev="sysfs" ino=38633 scontext=u:r:fsck:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
<14>[ 1175.034241][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1175.034241][ T1@C7] 
<14>[ 1175.034297][ T1@C7] fsck.f2fs: 	Info: can't find /sys, assuming normal block device
<14>[ 1175.034297][ T1@C7] 
<14>[ 1175.034324][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1175.034324][ T1@C7] 
<14>[ 1175.034351][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[ 1175.034351][ T1@C7] 
<14>[ 1175.034376][ T1@C7] fsck.f2fs:   "5.15.0-139-generic"
<14>[ 1175.034376][ T1@C7] 
<14>[ 1175.034401][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[ 1175.034401][ T1@C7] 
<14>[ 1175.034427][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1175.034427][ T1@C7] 
<14>[ 1175.034454][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1175.034454][ T1@C7] 
<14>[ 1175.034480][ T1@C7] fsck.f2fs: Info: superblock features = 499 :  encrypt extra_attr project_quota quota verity
<14>[ 1175.034480][ T1@C7] 
<14>[ 1175.034506][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1175.034506][ T1@C7] 
<14>[ 1175.034531][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[ 1175.034531][ T1@C7] 
<14>[ 1175.034557][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1175.034557][ T1@C7] 
<14>[ 1175.034581][ T1@C7] fsck.f2fs: Info: total FS sectors = 131072 (64 MB)
<14>[ 1175.034581][ T1@C7] 
<14>[ 1175.034606][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0x43da318b ubc:0x1000, vbc:0x12
<14>[ 1175.034606][ T1@C7] 
<14>[ 1175.034631][ T1@C7] fsck.f2fs: cp_addr: 207, cur_version:0x43da318b ubc:0x1000, vbc:0x12
<14>[ 1175.034631][ T1@C7] 
<14>[ 1175.034657][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000070dbb90010,cp1_version:0x43da318b
<14>[ 1175.034657][ T1@C7] 
<14>[ 1175.034682][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0x43da318a ubc:0x1000, vbc:0x12
<14>[ 1175.034682][ T1@C7] 
<14>[ 1175.034707][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0x43da318a ubc:0x1000, vbc:0x12
<14>[ 1175.034707][ T1@C7] 
<14>[ 1175.034732][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000070dbb92030,cp2_version:0x43da318a
<14>[ 1175.034732][ T1@C7] 
<14>[ 1175.034757][ T1@C7] fsck.f2fs: Info: CKPT version = 43da318b
<14>[ 1175.034757][ T1@C7] 
<14>[ 1175.034782][ T1@C7] fsck.f2fs: Info: version timestamp cur: 1168, prev: 0
<14>[ 1175.034782][ T1@C7] 
<14>[ 1175.034807][ T1@C7] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1175.034807][ T1@C7] 
<14>[ 1175.034832][ T1@C7] fsck.f2fs: Info: checkpoint state = c1 :  nat_bits crc unmount
<14>[ 1175.034832][ T1@C7] 
<14>[ 1175.034857][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[ 1175.034857][ T1@C7] 
<14>[ 1175.146948][ T1@C7] e2fsck: e2fsck 1.46.6 (1-Feb-2023)
<14>[ 1175.146948][ T1@C7] 
<14>[ 1175.146996][ T1@C7] e2fsck: productinfo: clean, 33/2560 files, 1415/2560 blocks
<14>[ 1175.146996][ T1@C7] 
<5>[ 1175.223058][T91@C1] audit: type=1400 audit(1735701469.455:624): avc:  denied  { getattr } for  pid=12627 comm="fsck.f2fs" path="/sys/devices/platform/soc/soc:ap-apb/201d0000.sdio/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p51/partition" dev="sysfs" ino=38723 scontext=u:r:fsck:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
<14>[ 1175.238009][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[ 1175.238009][ T1@C6] 
<14>[ 1175.238049][ T1@C6] fsck.f2fs: 	Info: can't find /sys, assuming normal block device
<14>[ 1175.238049][ T1@C6] 
<14>[ 1175.238062][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[ 1175.238062][ T1@C6] 
<14>[ 1175.238075][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[ 1175.238075][ T1@C6] 
<14>[ 1175.238087][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1175.238087][ T1@C6] 
<14>[ 1175.238100][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[ 1175.238100][ T1@C6] 
<14>[ 1175.238112][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1175.238112][ T1@C6] 
<14>[ 1175.238125][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00154-g26aa07240ba8-dirty"
<14>[ 1175.238125][ T1@C6] 
<14>[ 1175.238139][ T1@C6] fsck.f2fs: Info: superblock features = 499 :  encrypt extra_attr project_quota quota verity
<14>[ 1175.238139][ T1@C6] 
<14>[ 1175.238152][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[ 1175.238152][ T1@C6] 
<14>[ 1175.238165][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[ 1175.238165][ T1@C6] 
<14>[ 1175.238177][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[ 1175.238177][ T1@C6] 
<14>[ 1175.238188][ T1@C6] fsck.f2fs: Info: total FS sectors = 131072 (64 MB)
<14>[ 1175.238188][ T1@C6] 
<14>[ 1175.238200][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x130b13b5 ubc:0x1400, vbc:0x2a
<14>[ 1175.238200][ T1@C6] 
<14>[ 1175.238213][ T1@C6] fsck.f2fs: cp_addr: 205, cur_version:0x130b13b5 ubc:0x1400, vbc:0x2a
<14>[ 1175.238213][ T1@C6] 
<14>[ 1175.238225][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000072c863a020,cp1_version:0x130b13b5
<14>[ 1175.238225][ T1@C6] 
<14>[ 1175.238236][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x130b13b6 ubc:0x1400, vbc:0x2a
<14>[ 1175.238236][ T1@C6] 
<14>[ 1175.238249][ T1@C6] fsck.f2fs: cp_addr: 405, cur_version:0x130b13b6 ubc:0x1400, vbc:0x2a
<14>[ 1175.238249][ T1@C6] 
<14>[ 1175.238260][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000072c8639010,cp2_version:0x130b13b6
<14>[ 1175.238260][ T1@C6] 
<14>[ 1175.238272][ T1@C6] fsck.f2fs: Info: CKPT version = 130b13b6
<14>[ 1175.238272][ T1@C6] 
<14>[ 1175.238284][ T1@C6] fsck.f2fs: Info: version timestamp cur: 1169, prev: 0
<14>[ 1175.238284][ T1@C6] 
<14>[ 1175.238296][ T1@C6] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[ 1175.238296][ T1@C6] 
<14>[ 1175.238308][ T1@C6] fsck.f2fs: Info: checkpoint state = 1c5 :  trimmed nat_bits crc compacted_summary unmount
<14>[ 1175.238308][ T1@C6] 
<14>[ 1175.238319][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[ 1175.238319][ T1@C6] 
<14>[ 1175.238928][ T1@C6] init: Resume reboot monitor thread after fsck
<14>[ 1175.239304][ T1@C6] init: sync() after umount...
<14>[ 1175.239912][T12576@C1] init: shutdown_timeout_timespec.tv_sec: 1461
<14>[ 1175.240381][ T1@C6] init: sync() after umount took1ms
<11>[ 1175.240431][ T1@C6] init: umount timeout  umount_end_time20586umount_start_time13665
<3>[ 1175.240652][ T1@C6] shutdown_detect_check: shutdown_detect_timeout: umount timeout
<12>[ 1175.341227][ T1@C6] init: powerctl_shutdown_time_ms:20687:0
<14>[ 1175.341802][T12576@C1] init: remaining_shutdown_time: 286
<3>[ 1175.342518][ T1@C6] shutdown_detect_check: shutdown_detect_phase: shutdown  current phase systemcall
<14>[ 1175.342591][ T1@C6] init: Reboot ending, jumping to kernel
<3>[ 1175.342742][ T1@C6] failed to open '/dev/block/by-name/sd_klog':-2!


