[Builtin Hooks]
ktfmt = true

[Builtin Hooks Options]
ktfmt = --kotlinlang-style

[Tool Paths]
ktfmt = ${REPO_ROOT}/external/ktfmt/ktfmt.sh

[Hook Scripts]
checkstyle_hook = ${REPO_ROOT}/prebuilts/checkstyle/checkstyle.py --config_xml tools/checkstyle.xml --sha ${PREUPLOAD_COMMIT}

flag_hook = ${REPO_ROOT}/frameworks/base/packages/SystemUI/flag_check.py --msg=${PREUPLOAD_COMMIT_MESSAGE} --files=${PREUPLOAD_FILES} --project=${REPO_PATH}
