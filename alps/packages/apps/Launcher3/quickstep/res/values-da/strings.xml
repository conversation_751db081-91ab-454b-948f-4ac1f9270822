<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">PIN-kode</string>
    <string name="recent_task_option_freeform">Freeform</string>
    <string name="recent_task_option_desktop">Skrivebord</string>
    <string name="recents_empty_message">Ingen nylige emner</string>
    <string name="accessibility_app_usage_settings">Indstillinger for appbrug</string>
    <string name="recents_clear_all">Ryd alle</string>
    <string name="accessibility_recent_apps">Seneste apps</string>
    <string name="task_view_closed">Opgave afsluttet</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minut</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> tilbage i dag</string>
    <string name="title_app_suggestions">App forslag</string>
    <string name="all_apps_prediction_tip">Dine forventede apps</string>
    <string name="hotseat_edu_title_migrate">Få appforslag på den nederste række på din startskærm</string>
    <string name="hotseat_edu_title_migrate_landscape">Få appforslag på favoritrækken på din startskærm</string>
    <string name="hotseat_edu_message_migrate">Få nemt adgang til dine mest brugte apps direkte på startskærmen. Forslag vil ændre sig baseret på dine rutiner. Apps på nederste række flyttes op til din startskærm.</string>
    <string name="hotseat_edu_message_migrate_landscape">Få nemt adgang til dine mest brugte apps direkte på startskærmen. Forslag vil ændre sig baseret på dine rutiner. Apps i favoritrækken flyttes til din startskærm.</string>
    <string name="hotseat_edu_accept">Få appforslag</string>
    <string name="hotseat_edu_dismiss">Nej tak.</string>
    <string name="hotseat_prediction_settings">Indstillinger</string>
    <string name="hotseat_auto_enrolled">Mest brugte apps vises her, og ændres baseret på rutiner</string>
    <string name="hotseat_tip_no_empty_slots">Træk apps fra bundlinjen for at få appforslag</string>
    <string name="hotseat_tip_gaps_filled">Appforslag tilføjet til det tomme rum</string>
    <string name="hotsaet_tip_prediction_enabled">Appforslag aktiveret</string>
    <string name="hotsaet_tip_prediction_disabled">Appforslag deaktiveret</string>
    <string name="hotseat_prediction_content_description">Forventet app: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Rotér din enhed</string>
    <string name="gesture_tutorial_rotation_prompt">Rotér din enhed for afslutte bevægelsesnavigations-undervisningen</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Sørg for, at du stryger fra den yderste højre eller yderste venstre kant</string>
    <string name="back_gesture_feedback_cancelled">Sørg for, at du stryger fra højre eller venstre kant til midten af skærmen og giver slip</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Du lærte, hvordan du stryger fra højre for at gå tilbage. Dernæst kan du lære, hvordan du skifter apps.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Du har afsluttet gå tilbage-bevægelsen. Som det næste skal du lære, hvordan du skifter apps.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Du har afsluttet vejledningen for gå tilbage-bevægelsen</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Pas på, at du ikke stryger for tæt på bunden af skærmen</string>
    <string name="back_gesture_tutorial_confirm_subtitle">For at forandring gå tilbage-bevægelsens følsomhed skal du gå til Indstillinger</string>
    <string name="back_gesture_intro_title">Stryg for at gå tilbage</string>
    <string name="back_gesture_intro_subtitle">Du går tilbage til foregående skærmbillede ved at stryge fra venstre eller højre kant til midten af skærmen.</string>
    <string name="back_gesture_spoken_intro_subtitle">Du går tilbage til foregående skærmbillede ved at stryge med 2 fingre fra venstre eller højre kant til midten af skærmen.</string>
    <string name="back_gesture_tutorial_title">Tilbage</string>
    <string name="back_gesture_tutorial_subtitle">Stryg fra venstre eller højre kant til midten af skærmen</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Sørg for at stryge opad fra skærmens nederste kant</string>
    <string name="home_gesture_feedback_overview_detected">Sørg for, at du ikke holder pause, før du giver slip</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Sørg for, at du stryger lige op</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Du har afsluttet vejledningen for gå hjem-bevægelsen. Som det næste skal du lære, hvordan du går tilbage.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Du har afsluttet vejledningen for gå hjem-bevægelsen</string>
    <string name="home_gesture_intro_title">Stryg for at gå hjem</string>
    <string name="home_gesture_intro_subtitle">Stryg op fra bunden af din skærm. Denne gestus fører dig altid til startskærmen.</string>
    <string name="home_gesture_spoken_intro_subtitle">Stryg opad med 2 fingre fra bunden af skærmen. Denne bevægelse bringer dig altid til startskærmen.</string>
    <string name="home_gesture_tutorial_title">Gå til Start</string>
    <string name="home_gesture_tutorial_subtitle">Stryg opad fra bunden af din skærm</string>
    <string name="home_gesture_tutorial_success">Godt gjort!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Sørg for at stryge opad fra skærmens nederste kant</string>
    <string name="overview_gesture_feedback_home_detected">Prøv at holde vinduet længere, inden du slipper med fingeren</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Sørg for, at du stryger lige op, hold derefter pause</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Du lærte at bruge bevægelser. Gå til Indstillinger for at deaktivere bevægelser.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Du har afsluttet vejledningen for skift app-bevægelsen</string>
    <string name="overview_gesture_intro_title">Stryg for at skifte apps</string>
    <string name="overview_gesture_intro_subtitle">For at skifte mellem apps, swipe op fra nederst på din skærm og slip så.</string>
    <string name="overview_gesture_spoken_intro_subtitle">For at skifte mellem apps, swipe op med 2 fingre fra nederst på din skærm, hold og slip så.</string>
    <string name="overview_gesture_tutorial_title">Skift apps</string>
    <string name="overview_gesture_tutorial_subtitle">Stryg opad fra bunden af din skærm, hold fast og slip så</string>
    <string name="overview_gesture_tutorial_success">Godt gået!</string>
    <string name="gesture_tutorial_confirm_title">Alt klar</string>
    <string name="gesture_tutorial_action_button_label">"Udfør"</string>
    <string name="gesture_tutorial_action_button_label_settings">Indstillinger</string>
    <string name="gesture_tutorial_try_again">Prøv igen</string>
    <string name="gesture_tutorial_nice">Fint!</string>
    <string name="gesture_tutorial_step">Selvstudium <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Alt klar!</string>
    <string name="allset_hint">Stryg opad for at gå til Start</string>
    <string name="allset_button_hint">Rør ved Hjem-knappen for at komme til din startskærm</string>
    <string name="allset_description_generic">Du er klar til at begynde at bruge din <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Enhed</string>
    <string name="allset_navigation_settings"><annotation id="link">Indstillinger for systemnavigation</annotation></string>
    <string name="action_share">Del</string>
    <string name="action_screenshot">Skærmbillede</string>
    <string name="action_split">Deling</string>
    <string name="action_save_app_pair">Gem par af apps</string>
    <string name="toast_split_select_app">Tryk på en anden app for at brug delt skærm</string>
    <string name="toast_contextual_split_select_app">Vælg en anden app for at brug delt skærm</string>
    <string name="toast_split_select_app_cancel"><b>Annuller</b></string>
    <string name="toast_split_select_cont_desc">Gå ud af valget for delt skærm</string>
    <string name="toast_split_app_unsupported">Vælg en anden app for at brug delt skærm</string>
    <string name="blocked_by_policy">Denne handling er ikke tilladt af appen eller din organisation</string>
    <string name="split_widgets_not_supported">Widgets understøttes i øjeblikket ikke. Vælg en anden app.</string>
    <string name="skip_tutorial_dialog_title">Vil du springe navigationsstudiet over?</string>
    <string name="skip_tutorial_dialog_subtitle">Du kan finde dette senere i <xliff:g id="name">%1$s</xliff:g> app</string>
    <string name="gesture_tutorial_action_button_label_cancel">"Annuller"</string>
    <string name="gesture_tutorial_action_button_label_skip">"Spring over"</string>
    <string name="accessibility_rotate_button">Roter skærmen</string>
    <string name="taskbar_edu_a11y_title">Undervisning i proceslinjen</string>
    <string name="taskbar_edu_splitscreen">Træk en app ud til siden for at bruge 2 apps samtidigt</string>
    <string name="taskbar_edu_stashing">Stryg langsomt opad for at vise proceslinjen</string>
    <string name="taskbar_edu_suggestions">Få forslag til app baseret på din rutine</string>
    <string name="taskbar_edu_pinning">Et langt tryk på deleren fæstner proceslinjen</string>
    <string name="taskbar_edu_features">Gør mere med proceslinjen</string>
    <string name="taskbar_edu_pinning_title">Vis altid proceslinjen</string>
    <string name="taskbar_edu_pinning_standalone">For altid at få proceslinjen vist nederst på skærmen skal du give deleren et langt tryk.</string>
    <string name="taskbar_search_edu_title">Med et langt tryk på handlingsknappen kan du søge i, hvad du har på skærmen</string>
    <string name="taskbar_edu_search_disclosure">Dette produkt anvender den valgte del af din skærm til søgning. Google\'s <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Privatlivspolitik<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> og <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Tjenestevilkår<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> er gældende.</string>
    <string name="taskbar_edu_close">Luk</string>
    <string name="taskbar_edu_done">"Udfør"</string>
    <string name="taskbar_button_home">Startskærm</string>
    <string name="taskbar_button_a11y">Tilgængelighed</string>
    <string name="taskbar_button_back">"Tilbage"</string>
    <string name="taskbar_button_ime_switcher">IME switcher</string>
    <string name="taskbar_button_recents">Seneste</string>
    <string name="taskbar_button_notifications">Meddelelser</string>
    <string name="taskbar_button_quick_settings">Hurtige indstillinger</string>
    <string name="taskbar_a11y_title">Proceslinjen</string>
    <string name="taskbar_a11y_shown_title">Proceslinjen vist</string>
    <string name="taskbar_a11y_hidden_title">Proceslinjen skjult</string>
    <string name="taskbar_phone_a11y_title">Navigationslinje</string>
    <string name="always_show_taskbar">Vis altid proceslinjen</string>
    <string name="change_navigation_mode">Skift navigationstilstand</string>
    <string name="taskbar_divider_a11y_title">Proceslinje-deler</string>
    <string name="move_drop_target_top_or_left">Flyt til top/venstre</string>
    <string name="move_drop_target_bottom_or_right">Flyt til bund/højre</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Vis # app mere.}one{Vis # app mere.}other{Vis # apps mere.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Vis # computerprogram.}one{Vis # computerprogram.}other{Vis # computerprogrammer.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> og <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Boble</string>
    <string name="bubble_bar_overflow_description">Flyder over</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> fra <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> og <xliff:g id="bubble_count" example="4">%2$d</xliff:g> mere</string>
    <string name="app_name">Launcher3</string>
</resources>