<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Anclar</string>
    <string name="recent_task_option_freeform">Formato libre</string>
    <string name="recent_task_option_desktop">Escritorio</string>
    <string name="recents_empty_message">No hay elementos recientes</string>
    <string name="accessibility_app_usage_settings">Ajustes de uso de la aplicación</string>
    <string name="recents_clear_all">Bo<PERSON>r todo</string>
    <string name="accessibility_recent_apps">Aplicaciones recientes</string>
    <string name="task_view_closed">Tarea cerrada</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minuto</string>
    <string name="time_left_for_app">Hoy quedan <xliff:g id="time" example="7 minutes">%1$s</xliff:g></string>
    <string name="title_app_suggestions">Sugerencias de aplicaciones</string>
    <string name="all_apps_prediction_tip">Su predicción de aplicaciones</string>
    <string name="hotseat_edu_title_migrate">Obtenga sugerencias de aplicaciones en la fila inferior de la pantalla de inicio</string>
    <string name="hotseat_edu_title_migrate_landscape">Obtenga sugerencias de aplicaciones en la fila de favoritos de la pantalla de inicio</string>
    <string name="hotseat_edu_message_migrate">Acceda fácilmente a las aplicaciones que más utiliza desde la pantalla de inicio. Las sugerencias variarán en función de sus rutinas. Las aplicaciones de la fila inferior se desplazarán a la pantalla de inicio.</string>
    <string name="hotseat_edu_message_migrate_landscape">Acceda fácilmente a las aplicaciones que más utiliza desde la pantalla de inicio. Las sugerencias variarán en función de sus rutinas. Las aplicaciones de la fila de favoritos se desplazarán a la pantalla de inicio.</string>
    <string name="hotseat_edu_accept">Obtener sugerencias de aplicaciones</string>
    <string name="hotseat_edu_dismiss">No, gracias</string>
    <string name="hotseat_prediction_settings">Ajustes</string>
    <string name="hotseat_auto_enrolled">Las aplicaciones que más utiliza aparecerán aquí y variarán en función de sus rutinas</string>
    <string name="hotseat_tip_no_empty_slots">Arrastre las aplicaciones de la fila inferior para obtener sugerencias de aplicaciones</string>
    <string name="hotseat_tip_gaps_filled">Se han añadido sugerencias de aplicaciones al espacio vacío</string>
    <string name="hotsaet_tip_prediction_enabled">Las sugerencias de aplicaciones están habilitadas</string>
    <string name="hotsaet_tip_prediction_disabled">Las sugerencias de aplicaciones están deshabilitadas</string>
    <string name="hotseat_prediction_content_description">Aplicación prevista: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Gire el dispositivo</string>
    <string name="gesture_tutorial_rotation_prompt">Gire el dispositivo para completar el tutorial de navegación por gestos</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Asegúrese de deslizar el dedo desde la derecha o la izquierda</string>
    <string name="back_gesture_feedback_cancelled">Asegúrese de deslizar el dedo desde la derecha o la izquierda hasta el centro de la pantalla y suéltelo</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Ya sabe cómo deslizar el dedo desde la derecha para volver atrás. A continuación, descubra cómo cambiar de aplicación.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Ha completado el gesto de volver atrás. A continuación, aprenderá a cambiar de aplicación.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Ha completado el gesto de volver atrás</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Asegúrese de no deslizar el dedo demasiado cerca de la parte inferior de la pantalla</string>
    <string name="back_gesture_tutorial_confirm_subtitle">Para configurar la sensibilidad del gesto de volver atrás, vaya a Ajustes</string>
    <string name="back_gesture_intro_title">Deslice el dedo para volver atrás</string>
    <string name="back_gesture_intro_subtitle">Para volver a la última pantalla, deslice el dedo desde la izquierda o la derecha hasta el centro de la pantalla.</string>
    <string name="back_gesture_spoken_intro_subtitle">Para volver a la última pantalla, deslice dos dedos desde la izquierda o la derecha hasta el centro de la pantalla.</string>
    <string name="back_gesture_tutorial_title">Volver</string>
    <string name="back_gesture_tutorial_subtitle">Deslice el dedo desde la izquierda o la derecha hasta el centro de la pantalla</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Asegúrese de deslizar el dedo hacia arriba desde la parte inferior de la pantalla</string>
    <string name="home_gesture_feedback_overview_detected">Asegúrese de no hacer una pausa antes de soltar</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Asegúrese de deslizar el dedo hacia arriba</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Ha completado el gesto de ir al Inicio. Ahora aprenda a volver atrás.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Ha completado el gesto de ir a la pantalla de inicio</string>
    <string name="home_gesture_intro_title">Deslice el dedo para ir a la pantalla de inicio</string>
    <string name="home_gesture_intro_subtitle">Deslice el dedo hacia arriba desde la parte inferior de la pantalla. Este gesto le llevará siempre a la pantalla de inicio.</string>
    <string name="home_gesture_spoken_intro_subtitle">Deslice dos dedos hacia arriba desde la parte inferior de la pantalla. Este gesto le llevará siempre a la pantalla de inicio.</string>
    <string name="home_gesture_tutorial_title">Ir a Inicio</string>
    <string name="home_gesture_tutorial_subtitle">Deslice el dedo hacia arriba desde la parte inferior de la pantalla</string>
    <string name="home_gesture_tutorial_success">¡Buen trabajo!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Asegúrese de deslizar el dedo hacia arriba desde la parte inferior de la pantalla</string>
    <string name="overview_gesture_feedback_home_detected">Intente mantener la ventana durante más tiempo antes de soltarla</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Asegúrese de deslizar el dedo hacia arriba y, después, haga una pausa</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Ya sabe cómo utilizar los gestos. Para desactivarlos, vaya a Ajustes.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Ha completado el gesto de cambiar de aplicación</string>
    <string name="overview_gesture_intro_title">Deslice el dedo para cambiar de aplicación</string>
    <string name="overview_gesture_intro_subtitle">Para pasar de una aplicación a otra, deslice el dedo hacia arriba desde la parte inferior de la pantalla, manténgalo pulsado y suéltelo.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Para pasar de una aplicación a otra, deslice dos dedos hacia arriba desde la parte inferior de la pantalla, manténgalos pulsados y suéltelos.</string>
    <string name="overview_gesture_tutorial_title">Cambiar aplicaciones</string>
    <string name="overview_gesture_tutorial_subtitle">Deslice el dedo hacia arriba desde la parte inferior de la pantalla, manténgalo pulsado y suéltelo</string>
    <string name="overview_gesture_tutorial_success">¡Bien hecho!</string>
    <string name="gesture_tutorial_confirm_title">Todo listo</string>
    <string name="gesture_tutorial_action_button_label">Hecho</string>
    <string name="gesture_tutorial_action_button_label_settings">Ajustes</string>
    <string name="gesture_tutorial_try_again">Volver a intentar</string>
    <string name="gesture_tutorial_nice">Genial</string>
    <string name="gesture_tutorial_step">Tutorial <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Todo listo</string>
    <string name="allset_hint">Deslice el dedo hacia arriba para ir a la página de inicio</string>
    <string name="allset_button_hint">Toque el botón de Inicio para ir a la pantalla de inicio</string>
    <string name="allset_description_generic">Ya está todo listo para empezar a usar el <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Dispositivo</string>
    <string name="allset_navigation_settings"><annotation id="link">Ajustes de navegación del sistema</annotation></string>
    <string name="action_share">Compartir</string>
    <string name="action_screenshot">Captura de pantalla</string>
    <string name="action_split">Dividir</string>
    <string name="action_save_app_pair">Guardar sincronización de aplicaciones</string>
    <string name="toast_split_select_app">Toque otra aplicación para utilizar la pantalla dividida</string>
    <string name="toast_contextual_split_select_app">Elija otra aplicación para utilizar la pantalla dividida</string>
    <string name="toast_split_select_app_cancel"><b>Cancelar</b></string>
    <string name="toast_split_select_cont_desc">Salir de la selección de pantalla dividida</string>
    <string name="toast_split_app_unsupported">Elija otra aplicación para utilizar la pantalla dividida</string>
    <string name="blocked_by_policy">La aplicación o su organización no permiten la realización de esta acción</string>
    <string name="split_widgets_not_supported">En este momento no se admiten widgets. Seleccione otra aplicación.</string>
    <string name="skip_tutorial_dialog_title">¿Desea omitir el tutorial de navegación?</string>
    <string name="skip_tutorial_dialog_subtitle">Puede recuperarlo más tarde en la aplicación <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">Cancelar</string>
    <string name="gesture_tutorial_action_button_label_skip">Saltar</string>
    <string name="accessibility_rotate_button">Girar pantalla</string>
    <string name="taskbar_edu_a11y_title">Formación sobre la barra de tareas</string>
    <string name="taskbar_edu_splitscreen">Arrastre una aplicación hacia un lado para utilizar dos aplicaciones a la vez</string>
    <string name="taskbar_edu_stashing">Deslice lentamente el dedo hacia arriba para mostrar la barra de tareas</string>
    <string name="taskbar_edu_suggestions">Obtenga sugerencias de aplicaciones basadas en su rutina</string>
    <string name="taskbar_edu_pinning">Mantenga pulsado el divisor para fijar la barra de tareas</string>
    <string name="taskbar_edu_features">Haga más cosas con la barra de tareas</string>
    <string name="taskbar_edu_pinning_title">Mostrar siempre la barra de tareas</string>
    <string name="taskbar_edu_pinning_standalone">Para mostrar siempre la barra de tareas en la parte inferior de la pantalla, mantenga pulsado el divisor.</string>
    <string name="taskbar_search_edu_title">Mantenga pulsada la tecla de acción para realizar búsquedas en la pantalla</string>
    <string name="taskbar_edu_search_disclosure">Este producto usa la parte seleccionada de su pantalla para buscar. Se aplican la <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Política de privacidad<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> y los <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Términos del servicio<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> de Google.</string>
    <string name="taskbar_edu_close">Cerca</string>
    <string name="taskbar_edu_done">Hecho</string>
    <string name="taskbar_button_home">Escritorio</string>
    <string name="taskbar_button_a11y">Accesibilidad</string>
    <string name="taskbar_button_back">Anterior</string>
    <string name="taskbar_button_ime_switcher">Conmutador IME</string>
    <string name="taskbar_button_recents">Reciente</string>
    <string name="taskbar_button_notifications">Notificaciones</string>
    <string name="taskbar_button_quick_settings">Ajustes rápidos</string>
    <string name="taskbar_a11y_title">Barra de tareas</string>
    <string name="taskbar_a11y_shown_title">Barra de tareas visible</string>
    <string name="taskbar_a11y_hidden_title">Barra de tareas oculta</string>
    <string name="taskbar_phone_a11y_title">Barra de navegación</string>
    <string name="always_show_taskbar">Mostrar siempre la barra de tareas</string>
    <string name="change_navigation_mode">Cambiar modo de navegación</string>
    <string name="taskbar_divider_a11y_title">Divisor de la barra de tareas</string>
    <string name="move_drop_target_top_or_left">Mover hacia arriba/a la izquierda</string>
    <string name="move_drop_target_bottom_or_right">Mover hacia abajo/a la derecha</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Mostrar # aplicación más.}other{Mostrar # aplicaciones más.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Mostrar # aplicación para ordenadores.}other{Mostrar # aplicaciones para ordenadores.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> y <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Burbuja</string>
    <string name="bubble_bar_overflow_description">Desbordamiento</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> de <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> y <xliff:g id="bubble_count" example="4">%2$d</xliff:g> más</string>
    <string name="app_name">Launcher3</string>
</resources>