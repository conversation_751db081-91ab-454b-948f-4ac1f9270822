<?xml version="1.0" encoding="UTF-8"?>
<!-- 
/*
* Copyright (C) 2017 The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
 -->

<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin" msgid="7929860679018978258">"固定"</string>
    <string name="recent_task_option_freeform" msgid="48863056265284071">"フリーフォーム"</string>
    <string name="recent_task_option_desktop" msgid="8280879717125435668">"パソコン"</string>
    <string name="recents_empty_message" msgid="7040467240571714191">"最近のアイテムはありません"</string>
    <string name="accessibility_app_usage_settings" msgid="6312864233673544149">"アプリの使用状況の設定"</string>
    <string name="recents_clear_all" msgid="5328176793634888831">"すべてクリア"</string>
    <string name="accessibility_recent_apps" msgid="4058661986695117371">"最近使ったアプリ"</string>
    <string name="task_view_closed" msgid="9170038230110856166">"タスクを閉じました"</string>
    <string name="task_contents_description_with_remaining_time" msgid="4479688746574672685">"<xliff:g id="TASK_DESCRIPTION">%1$s</xliff:g>、<xliff:g id="REMAINING_TIME">%2$s</xliff:g>"</string>
    <string name="shorter_duration_less_than_one_minute" msgid="4722015666335015336">"1 分未満"</string>
    <string name="time_left_for_app" msgid="3111996412933644358">"今日はあと <xliff:g id="TIME">%1$s</xliff:g>です"</string>
    <string name="title_app_suggestions" msgid="4185902664111965088">"アプリの候補"</string>
    <string name="all_apps_prediction_tip" msgid="2672336544844936186">"予測されたアプリ"</string>
    <string name="hotseat_edu_title_migrate" msgid="306578144424489980">"ホーム画面の一番下にアプリの候補を表示できます"</string>
    <string name="hotseat_edu_title_migrate_landscape" msgid="3633942953997845243">"ホーム画面のお気に入りの行でアプリの候補を利用できます"</string>
    <string name="hotseat_edu_message_migrate" msgid="8927179260533775320">"ホーム画面で、使用頻度の高いアプリに簡単にアクセスできるようになります。アプリの候補はルーティンに応じて変わります。ホーム画面で今一番下の行にあるアプリは、一行上に移動します。"</string>
    <string name="hotseat_edu_message_migrate_landscape" msgid="4248943380443387697">"ホーム画面で、使用頻度の高いアプリに簡単にアクセスできるようになります。アプリの候補はルーティンに応じて変わります。お気に入りの行にあるアプリがホーム画面に移動します。"</string>
    <string name="hotseat_edu_accept" msgid="1611544083278999837">"アプリの候補を利用"</string>
    <string name="hotseat_edu_dismiss" msgid="2781161822780201689">"使用しない"</string>
    <string name="hotseat_prediction_settings" msgid="6246554993566070818">"設定"</string>
    <string name="hotseat_auto_enrolled" msgid="522100018967146807">"使用頻度の高いアプリがここに表示されます（ルーティンに応じて変わります）"</string>
    <string name="hotseat_tip_no_empty_slots" msgid="1325212677738179185">"一番下の行からアプリをドラッグするとアプリの候補が表示されます"</string>
    <string name="hotseat_tip_gaps_filled" msgid="3035673010274223538">"空いたスペースにアプリの候補が追加されます"</string>
    <string name="hotsaet_tip_prediction_enabled" msgid="2233554377501347650">"アプリの候補表示が有効です"</string>
    <string name="hotsaet_tip_prediction_disabled" msgid="1506426298884658491">"アプリの候補は無効です"</string>
    <string name="hotseat_prediction_content_description" msgid="4582028296938078419">"予測されたアプリ: <xliff:g id="TITLE">%1$s</xliff:g>"</string>
    <string name="gesture_tutorial_rotation_prompt_title" msgid="7537946781362766964">"デバイスを回転してください"</string>
    <string name="gesture_tutorial_rotation_prompt" msgid="1664493449851960691">"ジェスチャー ナビゲーションのチュートリアルを終了するには、デバイスを回転してください"</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge" msgid="4175100312909721217">"右端または左端からスワイプしてください"</string>
    <string name="back_gesture_feedback_cancelled" msgid="762621530959111290">"画面の右端または左端から中央に向かってスワイプし、指を離してください"</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up" msgid="9176400654037014471">"右側からスワイプして前の画面に戻る方法を学習しました。次は、アプリを切り替える方法を覚えましょう。"</string>
    <string name="back_gesture_feedback_complete_with_follow_up" msgid="8653374779579748392">"「戻る」操作を完了しました。次は、アプリを切り替える方法を覚えましょう。"</string>
    <string name="back_gesture_feedback_complete_without_follow_up" msgid="197189945858268342">"「戻る」操作を学習しました"</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar" msgid="9157480023651452969">"スワイプする際は画面の下部に近づきすぎないようにしましょう"</string>
    <string name="back_gesture_tutorial_confirm_subtitle" msgid="5181305411668713250">"「戻る」操作の感度を変更するには [設定] に移動します"</string>
    <string name="back_gesture_intro_title" msgid="19551256430224428">"スワイプで戻る"</string>
    <string name="back_gesture_intro_subtitle" msgid="7912576483031802797">"直前の画面に戻るには、画面の左端または右端から中央に向かってスワイプします。"</string>
    <string name="back_gesture_spoken_intro_subtitle" msgid="2162043199263088592">"直前の画面に戻るには、2 本の指で画面の左端または右端から中央に向かってスワイプします。"</string>
    <string name="back_gesture_tutorial_title" msgid="1944737946101059789">"戻る"</string>
    <string name="back_gesture_tutorial_subtitle" msgid="6639993416000920142">"画面の左端または右端から中央に向かってスワイプします"</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge" msgid="4816365433160895458">"画面の下端から上にスワイプしてください"</string>
    <string name="home_gesture_feedback_overview_detected" msgid="5177627157303895077">"指を離す前にいったん止めないでください"</string>
    <string name="home_gesture_feedback_wrong_swipe_direction" msgid="8328465201424027148">"まっすぐ上にスワイプしてください"</string>
    <string name="home_gesture_feedback_complete_with_follow_up" msgid="8766981412895888417">"「ホームに移動」操作を学習しました。次は、前の画面に戻る方法を覚えましょう。"</string>
    <string name="home_gesture_feedback_complete_without_follow_up" msgid="2978063221383413443">"「ホームに移動」操作を学習しました"</string>
    <string name="home_gesture_intro_title" msgid="836590312858441830">"スワイプでホームに戻る"</string>
    <string name="home_gesture_intro_subtitle" msgid="2632238748497975326">"画面を下から上にスワイプします。この操作でいつでもホーム画面に戻れます。"</string>
    <string name="home_gesture_spoken_intro_subtitle" msgid="1030987707382031750">"2 本の指で画面下部から上にスワイプします。この操作で常にホーム画面に戻ります。"</string>
    <string name="home_gesture_tutorial_title" msgid="3126834347496917376">"ホームに移動"</string>
    <string name="home_gesture_tutorial_subtitle" msgid="7245995490408668778">"画面を下から上にスワイプします"</string>
    <string name="home_gesture_tutorial_success" msgid="1736295017642244751">"よくできました！"</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge" msgid="6402349235265407385">"画面の下端から上にスワイプしてください"</string>
    <string name="overview_gesture_feedback_home_detected" msgid="663432226180397138">"ウィンドウをもう少し長く押してから指を離すようにしてみましょう"</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction" msgid="1191055451018584958">"まっすぐ上にスワイプしてから、いったん指を止めてください"</string>
    <string name="overview_gesture_feedback_complete_with_follow_up" msgid="3544611727467765026">"主なジェスチャーについて学びました。ジェスチャーを OFF にするには、設定に移動してください。"</string>
    <string name="overview_gesture_feedback_complete_without_follow_up" msgid="2903050864432331629">"「アプリの切り替え」操作を学習しました"</string>
    <string name="overview_gesture_intro_title" msgid="2902054412868489378">"スワイプでアプリを切り替える"</string>
    <string name="overview_gesture_intro_subtitle" msgid="4968091015637850859">"アプリを切り替えるには、画面を下から上にスワイプして長押しし、指を離します。"</string>
    <string name="overview_gesture_spoken_intro_subtitle" msgid="3853371838260201751">"アプリを切り替えるには、2 本の指で画面下部から上にスワイプしたまま長押しし、指を離します。"</string>
    <string name="overview_gesture_tutorial_title" msgid="4125835002668708720">"アプリの切り替え"</string>
    <string name="overview_gesture_tutorial_subtitle" msgid="5253549754058973071">"画面を下から上にスワイプして長押しし、指を離します"</string>
    <string name="overview_gesture_tutorial_success" msgid="1910267697807973076">"完了です！"</string>
    <string name="gesture_tutorial_confirm_title" msgid="6201516182040074092">"設定完了"</string>
    <string name="gesture_tutorial_action_button_label" msgid="6249846312991332122">"完了"</string>
    <string name="gesture_tutorial_action_button_label_settings" msgid="2923621047916486604">"設定"</string>
    <string name="gesture_tutorial_try_again" msgid="65962545858556697">"もう一度行ってください"</string>
    <string name="gesture_tutorial_nice" msgid="2936275692616928280">"その調子です！"</string>
    <string name="gesture_tutorial_step" msgid="1279786122817620968">"チュートリアル <xliff:g id="CURRENT">%1$d</xliff:g>/<xliff:g id="TOTAL">%2$d</xliff:g>"</string>
    <string name="allset_title" msgid="5021126669778966707">"設定完了"</string>
    <string name="allset_hint" msgid="459504134589971527">"ホームに移動するには上にスワイプします"</string>
    <string name="allset_button_hint" msgid="2395219947744706291">"ホームボタンをタップすると、ホーム画面に移動します"</string>
    <string name="allset_description_generic" msgid="5385500062202019855">"<xliff:g id="DEVICE">%1$s</xliff:g> を使用する準備ができました"</string>
    <string name="default_device_name" msgid="6660656727127422487">"デバイス"</string>
    <string name="allset_navigation_settings" msgid="4713404605961476027"><annotation id="link">"システム ナビゲーションの設定"</annotation></string>
    <string name="action_share" msgid="2648470652637092375">"共有"</string>
    <string name="action_screenshot" msgid="8171125848358142917">"スクリーンショット"</string>
    <string name="action_split" msgid="2098009717623550676">"分割"</string>
    <string name="action_save_app_pair" msgid="5974823919237645229">"アプリのペア設定保存"</string>
    <string name="toast_split_select_app" msgid="8464310533320556058">"分割画面を使用するには、他のアプリをタップします"</string>
    <string name="toast_contextual_split_select_app" msgid="433510957123687090">"分割画面を使用するには別のアプリを選択してください"</string>
    <string name="toast_split_select_app_cancel" msgid="1532690483356445639"><b>"キャンセル"</b></string>
    <string name="toast_split_select_cont_desc" msgid="2119685056059607602">"分割画面の選択を終了します"</string>
    <string name="toast_split_app_unsupported" msgid="2360229567007828914">"分割画面にするには、別のアプリを選択してください"</string>
    <string name="blocked_by_policy" msgid="2071401072261365546">"この操作はアプリまたは組織で許可されていません"</string>
    <string name="split_widgets_not_supported" msgid="1355743038053053866">"ウィジェットは現在サポートされていません。他のアプリを選択してください。"</string>
    <string name="skip_tutorial_dialog_title" msgid="2725643161260038458">"操作チュートリアルをスキップしますか？"</string>
    <string name="skip_tutorial_dialog_subtitle" msgid="544063326241955662">"チュートリアルは後から <xliff:g id="NAME">%1$s</xliff:g> アプリで確認できます"</string>
    <string name="gesture_tutorial_action_button_label_cancel" msgid="3809842569351264108">"キャンセル"</string>
    <string name="gesture_tutorial_action_button_label_skip" msgid="394452764989751960">"スキップ"</string>
    <string name="accessibility_rotate_button" msgid="4771825231336502943">"画面を回転"</string>
    <string name="taskbar_edu_a11y_title" msgid="5417986057866415355">"タスクバーの説明"</string>
    <string name="taskbar_edu_splitscreen" msgid="5605512479258053350">"アプリを横にドラッグすると 2 個のアプリを同時に使用できます"</string>
    <string name="taskbar_edu_stashing" msgid="5645461372669217294">"タスクバーを表示するには、上にゆっくりとスワイプします"</string>
    <string name="taskbar_edu_suggestions" msgid="8215044496435527982">"毎日の使用状況に基づいてアプリの候補が表示されます"</string>
    <string name="taskbar_edu_pinning" msgid="6708550858580071558">"分割線を長押ししてタスクバーを固定します"</string>
    <string name="taskbar_edu_features" msgid="3320337287472848162">"タスクバーの各種機能"</string>
    <string name="taskbar_edu_pinning_title" msgid="210102174154211712">"タスクバーを常に表示"</string>
    <string name="taskbar_edu_pinning_standalone" msgid="2636919474366410467">"タスクバーを画面下部に常に表示するには分割線を長押しします"</string>
    <string name="taskbar_search_edu_title" msgid="5569194922234364530">"画面上の内容を検索するには、アクションキーを長押ししてください"</string>
    <string name="taskbar_edu_search_disclosure" msgid="8734536088447779686">"このサービスは、検索する際に画面上で選択された箇所を使用します。Google の<xliff:g id="BEGIN_PRIVACY_LINK">&lt;a href="%1$s"&gt;</xliff:g>プライバシー ポリシー<xliff:g id="END_PRIVACY_LINK">&lt;/a&gt;</xliff:g>と<xliff:g id="BEGIN_TOS_LINK">&lt;a href="%2$s"&gt;</xliff:g>利用規約<xliff:g id="END_TOS_LINK">&lt;/a&gt;</xliff:g>が適用されます。"</string>
    <string name="taskbar_edu_close" msgid="887022990168191073">"閉じる"</string>
    <string name="taskbar_edu_done" msgid="6880178093977704569">"完了"</string>
    <string name="taskbar_button_home" msgid="2151398979630664652">"ホーム"</string>
    <string name="taskbar_button_a11y" msgid="5241161324875094465">"ユーザー補助"</string>
    <string name="taskbar_button_back" msgid="8558862226461164514">"戻る"</string>
    <string name="taskbar_button_ime_switcher" msgid="1730244360907588541">"IME の切り替え"</string>
    <string name="taskbar_button_recents" msgid="7273376136216613134">"最近"</string>
    <string name="taskbar_button_notifications" msgid="7471740351507357318">"通知"</string>
    <string name="taskbar_button_quick_settings" msgid="227662894293189391">"クイック設定"</string>
    <string name="taskbar_a11y_title" msgid="6432169809852243110">"タスクバー"</string>
    <string name="taskbar_a11y_shown_title" msgid="6842833581088937713">"タスクバー表示"</string>
    <string name="taskbar_a11y_hidden_title" msgid="9154903639589659284">"タスクバー非表示"</string>
    <string name="taskbar_phone_a11y_title" msgid="4933360237131229395">"ナビゲーション バー"</string>
    <string name="always_show_taskbar" msgid="3608801276107751229">"常にタスクバーを表示する"</string>
    <string name="change_navigation_mode" msgid="9088393078736808968">"ナビゲーション モードを変更"</string>
    <string name="taskbar_divider_a11y_title" msgid="6608690309720242080">"タスクバーの区切り"</string>
    <string name="move_drop_target_top_or_left" msgid="2988702185049595807">"上 / 左に移動"</string>
    <string name="move_drop_target_bottom_or_right" msgid="5431393418797620162">"下 / 右に移動"</string>
    <string name="quick_switch_overflow" msgid="6935266023013283353">"{count,plural, =1{他 # 件のアプリを表示できます。}other{他 # 件のアプリを表示できます。}}"</string>
    <string name="quick_switch_desktop" msgid="4834587349322698616">"{count,plural, =1{# 個のデスクトップ アプリが表示されます。}other{# 個のデスクトップ アプリが表示されます。}}"</string>
    <string name="quick_switch_split_task" msgid="5598194724255333896">"<xliff:g id="APP_NAME_1">%1$s</xliff:g> と <xliff:g id="APP_NAME_2">%2$s</xliff:g>"</string>
    <string name="bubble_bar_bubble_fallback_description" msgid="7811684548953452009">"ふきだし"</string>
    <string name="bubble_bar_overflow_description" msgid="8617628132733151708">"オーバーフロー"</string>
    <string name="bubble_bar_bubble_description" msgid="1882466152448446446">"<xliff:g id="NOTIFICATION_TITLE">%1$s</xliff:g>（<xliff:g id="APP_NAME">%2$s</xliff:g>）"</string>
    <string name="bubble_bar_description_multiple_bubbles" msgid="3922207715357143648">"<xliff:g id="BUBBLE_BAR_BUBBLE_DESCRIPTION">%1$s</xliff:g>、他 <xliff:g id="BUBBLE_COUNT">%2$d</xliff:g> 件"</string>
</resources>
