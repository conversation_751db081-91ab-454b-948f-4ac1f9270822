<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 Google Inc.

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">

    <SwitchPreference
        android:key="pref_add_icon_to_home"
        android:title="@string/auto_add_shortcuts_label"
        android:summary="@string/auto_add_shortcuts_description"
        android:defaultValue="true"  />

    <SwitchPreference
        android:key="pref_allowRotation"
        android:title="@string/allow_rotation_title"
        android:summary="@string/allow_rotation_desc"
        android:defaultValue="false"
        android:persistent="true" />

</PreferenceScreen>
