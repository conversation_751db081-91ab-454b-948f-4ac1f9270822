<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Przypnij</string>
    <string name="recent_task_option_freeform">Dowolny kształt</string>
    <string name="recent_task_option_desktop">Pulpit</string>
    <string name="recents_empty_message">Brak ostatnio używanych pozycji</string>
    <string name="accessibility_app_usage_settings">Ustawienia użytkowania aplikacji</string>
    <string name="recents_clear_all">W<PERSON><PERSON><PERSON><PERSON><PERSON> wszystko</string>
    <string name="accessibility_recent_apps">Ostatnio używane aplikacje</string>
    <string name="task_view_closed">Zadanie zamknięte</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 min</string>
    <string name="time_left_for_app">zostało dziś <xliff:g id="time" example="7 minutes">%1$s</xliff:g></string>
    <string name="title_app_suggestions">Propozycje aplikacji</string>
    <string name="all_apps_prediction_tip">Twoje przewidywane aplikacje</string>
    <string name="hotseat_edu_title_migrate">Otrzymuj propozycje w wierszu dolnym na ekranie głównym</string>
    <string name="hotseat_edu_title_migrate_landscape">Otrzymuj propozycje w wierszu ulubionych na ekranie głównym</string>
    <string name="hotseat_edu_message_migrate">Z łatwością uzyskuj dostęp do najczęściej używanych aplikacji z prawej strony ekranu głównego. Propozycje zależą od schematów użytkowania. Aplikacje w wierszu dolnym zostaną przeniesione do ekranu głównego.</string>
    <string name="hotseat_edu_message_migrate_landscape">Z łatwością uzyskuj dostęp do najczęściej używanych aplikacji z prawej strony ekranu głównego. Propozycje zależą od schematów użytkowania. Aplikacje w wierszu ulubionych zostaną przeniesione do ekranu głównego.</string>
    <string name="hotseat_edu_accept">Otrzymuj propozycje aplikacji</string>
    <string name="hotseat_edu_dismiss">Nie, dziękuję</string>
    <string name="hotseat_prediction_settings">Ustawienia</string>
    <string name="hotseat_auto_enrolled">Najczęściej używane aplikacje będą wyświetlane tutaj. Lista zależy od schematów użytkowania</string>
    <string name="hotseat_tip_no_empty_slots">Przeciągaj aplikacje z wiersza dolnego, aby otrzymywać propozycje aplikacji</string>
    <string name="hotseat_tip_gaps_filled">Propozycje aplikacji dodawane do pustej przestrzeni</string>
    <string name="hotsaet_tip_prediction_enabled">Propozycje aplikacji włączone</string>
    <string name="hotsaet_tip_prediction_disabled">Propozycje aplikacji są wyłączone</string>
    <string name="hotseat_prediction_content_description">Ograniczona aplikacja: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Obróć urządzenie</string>
    <string name="gesture_tutorial_rotation_prompt">Obróć urządzeniem, aby ukończyć samouczek nawigacji po gestach</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Upewnij się, że przeciągasz od prawej krawędzi do lewej krawędzi</string>
    <string name="back_gesture_feedback_cancelled">Upewnij się, że przeciągasz od prawej lub lewej krawędzi do środka ekranu i puszczasz ekran</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Już wiesz, jak cofać poprzez przeciąganie w prawo, a teraz nauczysz się, jak przełączać aplikacje.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Udało Ci się wykonać gest cofania. Teraz nauczysz się gestu przełączania aplikacji.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Udało Ci się wykonać gest cofania</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Upewnij się, że nie przeciągasz zbyt blisko dolnej krawędzi</string>
    <string name="back_gesture_tutorial_confirm_subtitle">Czułość gestu cofania zmienisz w aplikacji Ustawienia.</string>
    <string name="back_gesture_intro_title">Cofanie przeciąganiem do góry</string>
    <string name="back_gesture_intro_subtitle">Aby wrócić do poprzedniego ekranu, przeciągnij od lewej lub prawej krawędzi do środka ekranu.</string>
    <string name="back_gesture_spoken_intro_subtitle">Aby cofnąć do poprzedniego ekranu, przeciągnij 2 palcami od lewej lub prawej krawędzi do środka ekranu.</string>
    <string name="back_gesture_tutorial_title">Wróć</string>
    <string name="back_gesture_tutorial_subtitle">Przeciągnij od lewej do prawej krawędzi po środku ekranu</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Upewnij się, że przeciągasz do góry od dolnej krawędzi ekranu</string>
    <string name="home_gesture_feedback_overview_detected">Ruch musi być nieprzerwany</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Upewnij się, że przeciągasz prosto do góry</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Udało Ci się wykonać gest ekranu głównego. Teraz nauczysz się gestu cofania.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Udało Ci się wykonać gest ekranu głównego</string>
    <string name="home_gesture_intro_title">Przeciągnij, aby przejść do ekranu głównego</string>
    <string name="home_gesture_intro_subtitle">Przeciągnij do góry od dolnej krawędzi ekranu. Ten gest przenosi do ekranu głównego.</string>
    <string name="home_gesture_spoken_intro_subtitle">Przeciągnij do góry 2 palcami od dolnej krawędzi ekranu. Ten gest przenosi do ekranu głównego.</string>
    <string name="home_gesture_tutorial_title">Przejdź do ekranu głównego</string>
    <string name="home_gesture_tutorial_subtitle">Przeciągnij do góry od dołu ekranu</string>
    <string name="home_gesture_tutorial_success">Świetnie!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Upewnij się, że przeciągasz do góry od dolnej krawędzi ekranu</string>
    <string name="overview_gesture_feedback_home_detected">Przytrzymaj okno dłużej przed zwolnieniem</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Upewnij się, że przeciągasz prosto do góry, a następnie zatrzymujesz się</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Teraz znasz wszystkie gesty. Aby wyłączyć gesty, przejdź do aplikacji Ustawienia.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Udało Ci się wykonać gest przełączania aplikacji</string>
    <string name="overview_gesture_intro_title">Przeciągaj, aby przełączać aplikacje</string>
    <string name="overview_gesture_intro_subtitle">Aby przejść do innej aplikacji, przeciągnij do góry od dołu ekranu i przytrzymaj, a następnie zwolnij.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Aby przejść do innej aplikacji, przeciągnij 2 palcami do góry od dołu ekranu i przytrzymaj, a następnie zwolnij.</string>
    <string name="overview_gesture_tutorial_title">Przełączaj aplikacje</string>
    <string name="overview_gesture_tutorial_subtitle">Przeciągnij do góry od dołu ekranu, przytrzymaj, a następnie puść</string>
    <string name="overview_gesture_tutorial_success">Dobra robota!</string>
    <string name="gesture_tutorial_confirm_title">Udało się</string>
    <string name="gesture_tutorial_action_button_label">Gotowe</string>
    <string name="gesture_tutorial_action_button_label_settings">Ustawienia</string>
    <string name="gesture_tutorial_try_again">Ponów</string>
    <string name="gesture_tutorial_nice">Nieźle!</string>
    <string name="gesture_tutorial_step">Samouczek <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Wszystko gotowe!</string>
    <string name="allset_hint">Przeciągnij do góry, aby przejść do ekranu głównego</string>
    <string name="allset_button_hint">Dotknij przycisku ekranu głównego, aby przejść do ekranu głównego</string>
    <string name="allset_description_generic">Możesz śmiało korzystać z <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Urządzenie</string>
    <string name="allset_navigation_settings"><annotation id="link">Ustawienia nawigacji systemu</annotation></string>
    <string name="action_share">Udostępnij</string>
    <string name="action_screenshot">Zrzut ekranu</string>
    <string name="action_split">Podziel</string>
    <string name="action_save_app_pair">Zapisz parę z aplikacją</string>
    <string name="toast_split_select_app">Dotknij innej aplikacji, aby skorzystać z podzielonego ekranu</string>
    <string name="toast_contextual_split_select_app">Wybierz inną aplikację, aby skorzystać z podzielonego ekranu</string>
    <string name="toast_split_select_app_cancel"><b>Anuluj</b></string>
    <string name="toast_split_select_cont_desc">Wyjdź z wyboru na podzielonym ekranie</string>
    <string name="toast_split_app_unsupported">Wybierz inną aplikację, aby skorzystać z podzielonego ekranu</string>
    <string name="blocked_by_policy">Aplikacja lub organizacja nie pozwala na wykonywanie tego działania.</string>
    <string name="split_widgets_not_supported">Widżety obecnie nieobsługiwane. Wybierz inną aplikację.</string>
    <string name="skip_tutorial_dialog_title">Pominąć samouczek o nawigacji?</string>
    <string name="skip_tutorial_dialog_subtitle">Znajdziesz go później w aplikacji <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">Anuluj</string>
    <string name="gesture_tutorial_action_button_label_skip">Pomiń</string>
    <string name="accessibility_rotate_button">Obracanie ekranu</string>
    <string name="taskbar_edu_a11y_title">Edukacja o pasku narzędzi</string>
    <string name="taskbar_edu_splitscreen">Przeciągnij aplikację na bok, aby korzystać z 2 aplikacji jednocześnie</string>
    <string name="taskbar_edu_stashing">Powoli przeciągnij do góry, aby wyświetlić pasek narzędzi</string>
    <string name="taskbar_edu_suggestions">Odbieraj sugestie aplikacji dostosowane do swojego grafika</string>
    <string name="taskbar_edu_pinning">Przyciśnij i przytrzymaj przegródkę, aby przypiąć pasek narzędzi</string>
    <string name="taskbar_edu_features">Z paskiem narzędzi zrobisz więcej</string>
    <string name="taskbar_edu_pinning_title">Zawsze pokazuj pasek narzędzi</string>
    <string name="taskbar_edu_pinning_standalone">Aby pasek narzędzi był zawsze widoczny u dołu ekranu, naciśnij i przytrzymaj przegródkę.</string>
    <string name="taskbar_search_edu_title">Naciśnij i przytrzymaj przycisk działania, aby wyszukać zawartość ekranu</string>
    <string name="taskbar_edu_search_disclosure">Produkt wykorzystuje wybraną część ekranu do wyszukiwania. Obowiązują dokumenty <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Polityka Prywatności<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> oraz <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Warunki Usługi<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> firmy Google.</string>
    <string name="taskbar_edu_close">Duże</string>
    <string name="taskbar_edu_done">Gotowe</string>
    <string name="taskbar_button_home">Ekran główny</string>
    <string name="taskbar_button_a11y">Ułatwienia dostępu</string>
    <string name="taskbar_button_back">Wstecz</string>
    <string name="taskbar_button_ime_switcher">Przełącznik IME</string>
    <string name="taskbar_button_recents">Ostatnie</string>
    <string name="taskbar_button_notifications">Powiadomienia</string>
    <string name="taskbar_button_quick_settings">Szybkie ustawienia</string>
    <string name="taskbar_a11y_title">Pasek narzędzi</string>
    <string name="taskbar_a11y_shown_title">Pasek narzędzi widoczny</string>
    <string name="taskbar_a11y_hidden_title">Pasek narzędzi ukryty</string>
    <string name="taskbar_phone_a11y_title">Pasek nawigacyjny</string>
    <string name="always_show_taskbar">Zawsze pokazuj pasek narzędzi</string>
    <string name="change_navigation_mode">Zmień tryb nawigacji</string>
    <string name="taskbar_divider_a11y_title">Przegródka paska narzędzi</string>
    <string name="move_drop_target_top_or_left">Przesuń w górę / w lewo</string>
    <string name="move_drop_target_bottom_or_right">Przesuń do dołu / w prawo</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Pokaż jeszcze # aplikację.}few{Pokaż jeszcze # aplikacje.}many{Pokaż jeszcze # aplikacji.}other{Pokaż jeszcze # aplikacji.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Pokaż # aplikację komputerową.}few{Pokaż # aplikacje komputerowe.}many{Pokaż # aplikacji komputerowych.}other{Pokaż # aplikacji komputerowej.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> i <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Dymek</string>
    <string name="bubble_bar_overflow_description">Przepełnienie</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> z <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> i dodatkowo <xliff:g id="bubble_count" example="4">%2$d</xliff:g></string>
    <string name="app_name">Launcher3</string>
</resources>