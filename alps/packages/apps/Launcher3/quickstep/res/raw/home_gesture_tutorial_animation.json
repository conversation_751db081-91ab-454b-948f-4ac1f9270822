{"v": "5.10.0", "fr": 60, "ip": 0, "op": 1040, "w": 412, "h": 892, "nm": "SUW_Home_V03", "ddd": 0, "assets": [{"id": "comp_0", "nm": "Part03_Demonstration_Loop_V02", "fr": 60, "pfr": 1, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Y Movement", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"k": [{"s": [869.65], "t": 54, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [868.552], "t": 55, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [866.62], "t": 56, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [863.754], "t": 57, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [859.83], "t": 58, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [854.697], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [848.161], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [839.974], "t": 61, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [829.807], "t": 62, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [817.208], "t": 63, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [801.528], "t": 64, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [781.789], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [756.397], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [722.519], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [674.682], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [606.651], "t": 69, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [553.048], "t": 70, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [541], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [484.717], "t": 72, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [453.506], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [434.136], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [420.535], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [410.249], "t": 76, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [402.1], "t": 77, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [395.436], "t": 78, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [389.862], "t": 79, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [385.123], "t": 80, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [381.042], "t": 81, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [377.493], "t": 82, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [374.382], "t": 83, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [371.638], "t": 84, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [369.207], "t": 85, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [367.043], "t": 86, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [365.113], "t": 87, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [363.386], "t": 88, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [361.84], "t": 89, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [360.453], "t": 90, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [359.209], "t": 91, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [358.094], "t": 92, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [357.096], "t": 93, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [356.202], "t": 94, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [355.404], "t": 95, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.694], "t": 96, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.065], "t": 97, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.51], "t": 98, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.022], "t": 99, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.599], "t": 100, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.233], "t": 101, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.923], "t": 102, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.663], "t": 103, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.45], "t": 104, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.155], "t": 106, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.246], "t": 111, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.562], "t": 112, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.016], "t": 113, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.613], "t": 114, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.36], "t": 115, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.264], "t": 116, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [355.332], "t": 117, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [356.57], "t": 118, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [357.985], "t": 119, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [359.582], "t": 120, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [361.367], "t": 121, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [363.343], "t": 122, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [365.514], "t": 123, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [367.88], "t": 124, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [370.439], "t": 125, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [373.185], "t": 126, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [376.111], "t": 127, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [379.204], "t": 128, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [382.447], "t": 129, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [385.817], "t": 130, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [389.289], "t": 131, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [392.833], "t": 132, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [396.416], "t": 133, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [400.002], "t": 134, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [403.557], "t": 135, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [407.045], "t": 136, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [410.436], "t": 137, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [413.7], "t": 138, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [416.814], "t": 139, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [419.758], "t": 140, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [422.518], "t": 141, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [425.083], "t": 142, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [427.447], "t": 143, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [429.606], "t": 144, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [431.561], "t": 145, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [433.314], "t": 146, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [434.869], "t": 147, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [436.232], "t": 148, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [437.407], "t": 149, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [438.403], "t": 150, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [439.225], "t": 151, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [439.883], "t": 152, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [440.382], "t": 153, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [440.73], "t": 154, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [441.105], "t": 178, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [441.434], "t": 179, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [442.009], "t": 180, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [442.856], "t": 181, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [444.004], "t": 182, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [445.489], "t": 183, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [447.353], "t": 184, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [449.647], "t": 185, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [452.433], "t": 186, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [455.788], "t": 187, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [459.807], "t": 188, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [464.614], "t": 189, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [470.368], "t": 190, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [477.283], "t": 191, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [485.646], "t": 192, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [495.857], "t": 193, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [508.473], "t": 194, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [524.252], "t": 195, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [544.08], "t": 196, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [568.422], "t": 197, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [595.903], "t": 198, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [622.885], "t": 199, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [646.387], "t": 200, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [665.81], "t": 201, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [681.749], "t": 202, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [694.958], "t": 203, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [706.05], "t": 204, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [715.481], "t": 205, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [723.582], "t": 206, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [730.598], "t": 207, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [736.714], "t": 208, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [742.07], "t": 209, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [746.778], "t": 210, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [750.924], "t": 211, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [754.579], "t": 212, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [757.802], "t": 213, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [760.641], "t": 214, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [763.135], "t": 215, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [765.318], "t": 216, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [767.221], "t": 217, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [768.867], "t": 218, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [770.279], "t": 219, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [771.475], "t": 220, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [772.472], "t": 221, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [773.285], "t": 222, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [773.927], "t": 223, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [774.41], "t": 224, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [774.021], "t": 232, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [773.506], "t": 233, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [772.832], "t": 234, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [771.963], "t": 235, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [770.847], "t": 236, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [769.411], "t": 237, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [767.551], "t": 238, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [765.132], "t": 239, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [762.034], "t": 240, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [758.373], "t": 241, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [754.748], "t": 242, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [751.783], "t": 243, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [749.59], "t": 244, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [748.016], "t": 245, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [746.895], "t": 246, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [746.109], "t": 247, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [745.576], "t": 248, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [746.056], "t": 257, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [746.466], "t": 258, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [746.943], "t": 259, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [747.474], "t": 260, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [748.047], "t": 261, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [748.643], "t": 262, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [749.247], "t": 263, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [749.84], "t": 264, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [750.412], "t": 265, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [750.952], "t": 266, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [751.455], "t": 267, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [751.918], "t": 268, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [752.342], "t": 269, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [753.071], "t": 271, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [753.655], "t": 273, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [754.299], "t": 276, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Continue Y Position", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.7]}, "t": 71, "s": [0]}, {"i": {"x": [0.544], "y": [1]}, "o": {"x": [0.477], "y": [0]}, "t": 109, "s": [-190]}, {"i": {"x": [0.671], "y": [1]}, "o": {"x": [0.309], "y": [0]}, "t": 156, "s": [-100]}, {"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 177, "s": [-100]}, {"i": {"x": [0.424], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 227, "s": [20]}, {"i": {"x": [0.363], "y": [1]}, "o": {"x": [0.345], "y": [0]}, "t": 251, "s": [-10]}, {"t": 285, "s": [0]}], "ix": 1}}]}], "ip": 0, "op": 395, "st": -110, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -609, "s": [0]}, {"t": -599, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"k": [{"s": [869.65], "t": 54, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [868.552], "t": 55, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [866.62], "t": 56, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [863.754], "t": 57, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [859.83], "t": 58, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [854.697], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [848.161], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [839.974], "t": 61, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [829.807], "t": 62, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [817.208], "t": 63, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [801.528], "t": 64, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [781.789], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [756.397], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [722.519], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [674.682], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [606.651], "t": 69, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [553.048], "t": 70, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [541], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [484.717], "t": 72, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [453.506], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [434.136], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [420.535], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [410.249], "t": 76, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [402.1], "t": 77, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [395.436], "t": 78, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [389.862], "t": 79, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [385.123], "t": 80, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [381.042], "t": 81, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [377.493], "t": 82, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [374.382], "t": 83, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [371.638], "t": 84, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [369.207], "t": 85, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [367.043], "t": 86, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [365.113], "t": 87, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [363.386], "t": 88, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [361.84], "t": 89, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [360.453], "t": 90, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [359.209], "t": 91, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [358.094], "t": 92, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [357.096], "t": 93, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [356.202], "t": 94, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [355.404], "t": 95, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.694], "t": 96, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.065], "t": 97, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.51], "t": 98, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.022], "t": 99, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.599], "t": 100, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.233], "t": 101, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.923], "t": 102, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.663], "t": 103, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.45], "t": 104, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.155], "t": 106, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Continue Y Position", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.7]}, "t": 71, "s": [0]}, {"t": 109, "s": [-190]}], "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.32, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.75, -21.641], [21.75, -21.75], [21.75, -21.75]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 24, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 23.5], [21.75, -21.75], [21.75, -66.5]], "c": false}]}, {"i": {"x": 0.65, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 13.5], [21.75, -21.75], [21.75, -56.5]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.35, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 13.5], [21.75, -21.75], [21.75, -56.5]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.35, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 31.5], [21.75, -21.75], [21.75, -74.5]], "c": false}]}, {"t": 71, "s": [{"i": [[0, 0], [0, 26.75], [0, 0]], "o": [[0, 0], [0, -26.583], [0, 0]], "v": [[29.75, 31.5], [21.75, -21.75], [29.75, -74.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.44], "y": [0]}, "t": -59, "s": [54]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.418], "y": [0]}, "t": -4, "s": [58]}, {"i": {"x": [0.149], "y": [1]}, "o": {"x": [0.529], "y": [0]}, "t": 0, "s": [58]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 20, "s": [27]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 67, "s": [27]}, {"t": 86, "s": [58], "h": 1}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.28], "y": [0.77]}, "o": {"x": [0.87], "y": [0.12]}, "t": 69, "s": [0]}, {"t": 83, "s": [49.5]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.28], "y": [0.77]}, "o": {"x": [0.87], "y": [0.12]}, "t": 69, "s": [100]}, {"t": 83, "s": [50.5]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -465, "s": [0]}, {"t": -447, "s": [90]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Bend", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 82, "st": -610, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Gesture KO", "td": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 19, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 49, "s": [0]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 71, "s": [100]}, {"t": 80, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 1429, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rectangle Independent Corners", "np": 19, "mn": "Pseudo/0.16410552199068107", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Align", "mn": "Pseudo/0.16410552199068107-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}, {"ty": 6, "nm": "Size", "mn": "Pseudo/0.16410552199068107-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "w", "mn": "Pseudo/0.16410552199068107-0003", "ix": 3, "v": {"a": 0, "k": 414, "ix": 3}}, {"ty": 0, "nm": "h", "mn": "Pseudo/0.16410552199068107-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.8], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 53, "s": [581]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.312], "y": [4.051]}, "t": 71, "s": [910]}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 80, "s": [910]}, {"t": 110, "s": [581]}], "ix": 4}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Rounding", "mn": "Pseudo/0.16410552199068107-0006", "ix": 6, "v": 0}, {"ty": 0, "nm": "tl", "mn": "Pseudo/0.16410552199068107-0007", "ix": 7, "v": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 53, "s": [90]}, {"t": 71, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 80, "s": [200]}, {"t": 110, "s": [90]}], "ix": 7}}, {"ty": 0, "nm": "tr", "mn": "Pseudo/0.16410552199068107-0008", "ix": 8, "v": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 53, "s": [90]}, {"t": 71, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 80, "s": [200]}, {"t": 110, "s": [90]}], "ix": 8}}, {"ty": 0, "nm": "br", "mn": "Pseudo/0.16410552199068107-0009", "ix": 9, "v": {"a": 0, "k": 135, "ix": 9}}, {"ty": 0, "nm": "bl", "mn": "Pseudo/0.16410552199068107-0010", "ix": 10, "v": {"a": 0, "k": 135, "ix": 10}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Alignment", "mn": "Pseudo/0.16410552199068107-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "X Anchor %", "mn": "Pseudo/0.16410552199068107-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 0, "nm": "Y Anchor %", "mn": "Pseudo/0.16410552199068107-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "X Position", "mn": "Pseudo/0.16410552199068107-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15}}, {"ty": 0, "nm": "Y Position", "mn": "Pseudo/0.16410552199068107-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0017", "ix": 17, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.772, 0], [0, 0], [0, -49.772], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.772], [0, 0], [49.772, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.167], [-116.817, -581.35], [116.817, -581.35], [207, -491.167], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.09, 0], [0, 0], [0, -50.09], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.09], [0, 0], [50.09, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.689], [-116.241, -582.448], [116.241, -582.448], [207, -491.689], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.651, 0], [0, 0], [0, -50.651], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.651], [0, 0], [50.651, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.605], [-115.225, -584.38], [115.225, -584.38], [207, -492.605], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.485, 0], [0, 0], [0, -51.485], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -51.485], [0, 0], [51.485, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -493.959], [-113.713, -587.246], [113.713, -587.246], [207, -493.959], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-52.63, 0], [0, 0], [0, -52.63], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -52.63], [0, 0], [52.63, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.808], [-111.638, -591.17], [111.638, -591.17], [207, -495.808], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.132, 0], [0, 0], [0, -54.133], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -54.133], [0, 0], [54.133, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.219], [-108.916, -596.303], [108.916, -596.303], [207, -498.219], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.05, 0], [0, 0], [0, -56.05], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -56.05], [0, 0], [56.05, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -501.281], [-105.442, -602.839], [105.442, -602.839], [207, -501.281], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.455, 0], [0, 0], [0, -58.455], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.455], [0, 0], [58.455, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -505.109], [-101.083, -611.026], [101.083, -611.026], [207, -505.109], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.443, 0], [0, 0], [0, -61.443], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -61.443], [0, 0], [61.443, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -509.862], [-95.67, -621.193], [95.669, -621.193], [207, -509.862], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-65.136, 0], [0, 0], [0, -65.136], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -65.136], [0, 0], [65.136, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.772], [-88.979, -633.792], [88.979, -633.792], [207, -515.772], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.689, 0], [0, 0], [0, -69.689], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -69.689], [0, 0], [69.689, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.201], [-80.73, -649.472], [80.73, -649.472], [207, -523.201], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-75.292, 0], [0, 0], [0, -75.292], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -75.292], [0, 0], [75.292, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.789], [-70.578, -669.211], [70.578, -669.211], [207, -532.789], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-82.119, 0], [0, 0], [0, -82.119], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -82.119], [0, 0], [82.119, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -545.809], [-58.206, -694.603], [58.206, -694.603], [207, -545.809], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.124, 0], [0, 0], [0, -90.124], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -90.124], [0, 0], [90.124, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -565.184], [-43.703, -728.481], [43.703, -728.481], [207, -565.184], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-98.484, 0], [0, 0], [0, -98.484], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -98.484], [0, 0], [98.484, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -597.873], [-28.555, -776.318], [28.555, -776.318], [207, -597.873], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.33, 0], [0, 0], [0, -105.33], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.33], [0, 0], [105.33, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -653.5], [-16.151, -844.349], [16.151, -844.349], [207, -653.5], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.245, 0], [0, 0], [0, -109.245], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.245], [0, 0], [109.245, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -700.008], [-9.056, -897.952], [9.056, -897.952], [207, -700.008], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.387], [-7, -911.387], [7, -911.387], [207, -711.387], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.036], [-7, -911.036], [7, -911.036], [207, -711.036], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.362], [-7, -910.362], [7, -910.362], [207, -710.362], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 76, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 80, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.251, 0], [0, 0], [0, -110.251], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.251], [0, 0], [110.251, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -709.533], [-7.234, -909.299], [7.234, -909.299], [207, -709.533], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 81, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.855, 0], [0, 0], [0, -109.855], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.855], [0, 0], [109.855, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -708.106], [-7.951, -907.155], [7.951, -907.155], [207, -708.106], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.183, 0], [0, 0], [0, -109.183], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.183], [0, 0], [109.183, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -705.681], [-9.169, -903.511], [9.169, -903.511], [207, -705.681], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-108.224, 0], [0, 0], [0, -108.224], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -108.224], [0, 0], [108.224, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -702.223], [-10.906, -898.317], [10.906, -898.317], [207, -702.223], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-106.972, 0], [0, 0], [0, -106.972], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -106.972], [0, 0], [106.972, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -697.707], [-13.174, -891.533], [13.174, -891.533], [207, -697.707], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.423, 0], [0, 0], [0, -105.422], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.422], [0, 0], [105.423, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -692.117], [-15.983, -883.134], [15.983, -883.134], [207, -692.117], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-103.574, 0], [0, 0], [0, -103.574], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -103.574], [0, 0], [103.574, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -685.448], [-19.332, -873.116], [19.332, -873.116], [207, -685.448], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-101.431, 0], [0, 0], [0, -101.431], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -101.431], [0, 0], [101.431, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -677.717], [-23.215, -861.502], [23.215, -861.502], [207, -677.717], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-99.003, 0], [0, 0], [0, -99.003], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -99.003], [0, 0], [99.003, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -668.96], [-27.614, -848.347], [27.614, -848.347], [207, -668.96], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-96.309, 0], [0, 0], [0, -96.309], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -96.309], [0, 0], [96.309, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -659.241], [-32.495, -833.746], [32.495, -833.746], [207, -659.241], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-93.374, 0], [0, 0], [0, -93.374], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -93.374], [0, 0], [93.374, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -648.653], [-37.814, -817.839], [37.814, -817.839], [207, -648.653], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.232, 0], [0, 0], [0, -90.232], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -90.232], [0, 0], [90.232, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -637.317], [-43.507, -800.81], [43.507, -800.81], [207, -637.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-86.925, 0], [0, 0], [0, -86.925], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -86.925], [0, 0], [86.925, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -625.39], [-49.498, -782.892], [49.498, -782.892], [207, -625.39], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-83.505, 0], [0, 0], [0, -83.505], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -83.505], [0, 0], [83.505, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -613.051], [-55.696, -764.355], [55.696, -764.355], [207, -613.051], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-76.546, 0], [0, 0], [0, -76.546], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -76.546], [0, 0], [76.546, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -587.949], [-68.304, -726.645], [68.304, -726.645], [207, -587.949], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-73.126, 0], [0, 0], [0, -73.126], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -73.126], [0, 0], [73.126, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -575.61], [-74.502, -708.108], [74.502, -708.108], [207, -575.61], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.819, 0], [0, 0], [0, -69.819], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -69.819], [0, 0], [69.819, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -563.683], [-80.493, -690.19], [80.493, -690.19], [207, -563.683], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-66.677, 0], [0, 0], [0, -66.677], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -66.677], [0, 0], [66.677, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -552.347], [-86.186, -673.161], [86.186, -673.161], [207, -552.347], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-63.742, 0], [0, 0], [0, -63.742], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -63.742], [0, 0], [63.742, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -541.759], [-91.505, -657.254], [91.505, -657.254], [207, -541.759], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.048, 0], [0, 0], [0, -61.048], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -61.048], [0, 0], [61.048, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.04], [-96.386, -642.653], [96.386, -642.653], [207, -532.04], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.62, 0], [0, 0], [0, -58.62], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.62], [0, 0], [58.62, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.283], [-100.785, -629.498], [100.785, -629.498], [207, -523.283], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.477, 0], [0, 0], [0, -56.477], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -56.477], [0, 0], [56.477, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.552], [-104.668, -617.884], [104.668, -617.884], [207, -515.552], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.628, 0], [0, 0], [0, -54.629], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -54.629], [0, 0], [54.628, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -508.884], [-108.017, -607.866], [108.017, -607.866], [207, -508.884], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-53.079, 0], [0, 0], [0, -53.079], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -53.079], [0, 0], [53.079, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -503.293], [-110.826, -599.467], [110.826, -599.467], [207, -503.293], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.827, 0], [0, 0], [0, -51.827], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -51.827], [0, 0], [51.827, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.777], [-113.094, -592.683], [113.094, -592.683], [207, -498.777], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 106, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.868, 0], [0, 0], [0, -50.868], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.868], [0, 0], [50.868, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.319], [-114.831, -587.489], [114.831, -587.489], [207, -495.319], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.196, 0], [0, 0], [0, -50.196], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.196], [0, 0], [50.196, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.894], [-116.049, -583.845], [116.049, -583.845], [207, -492.894], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 108, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.8, 0], [0, 0], [0, -49.8], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.8], [0, 0], [49.8, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.467], [-116.766, -581.701], [116.766, -581.701], [207, -491.467], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 109, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 110, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607843137, 0.341176470588, 0.780392156863, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"k": [{"s": [0, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0], "t": 394, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 11986", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 7, "op": 82, "st": -110, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Pill to Arc Shadow", "tt": 1, "tp": 3, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -609, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -599, "s": [100]}, {"t": 0, "s": [15]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"k": [{"s": [869.65], "t": 54, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [868.552], "t": 55, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [866.62], "t": 56, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [863.754], "t": 57, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [859.83], "t": 58, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [854.697], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [848.161], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [839.974], "t": 61, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [829.807], "t": 62, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [817.208], "t": 63, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [801.528], "t": 64, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [781.789], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [756.397], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [722.519], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [674.682], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [606.651], "t": 69, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [553.048], "t": 70, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [541], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [484.717], "t": 72, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [453.506], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [434.136], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [420.535], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [410.249], "t": 76, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [402.1], "t": 77, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [395.436], "t": 78, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [389.862], "t": 79, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [385.123], "t": 80, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [381.042], "t": 81, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [377.493], "t": 82, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [374.382], "t": 83, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [371.638], "t": 84, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [369.207], "t": 85, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [367.043], "t": 86, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [365.113], "t": 87, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [363.386], "t": 88, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [361.84], "t": 89, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [360.453], "t": 90, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [359.209], "t": 91, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [358.094], "t": 92, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [357.096], "t": 93, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [356.202], "t": 94, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [355.404], "t": 95, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.694], "t": 96, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [354.065], "t": 97, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.51], "t": 98, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [353.022], "t": 99, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.599], "t": 100, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [352.233], "t": 101, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.923], "t": 102, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.663], "t": 103, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.45], "t": 104, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [351.155], "t": 106, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}}, "a": {"a": 0, "k": [0, -2, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Continue Y Position", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.7]}, "t": 71, "s": [0]}, {"t": 109, "s": [-190]}], "ix": 1}}]}, {"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 8, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.32, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.75, -21.641], [21.75, -21.75], [21.75, -21.75]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 24, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 23.5], [21.75, -21.75], [21.75, -66.5]], "c": false}]}, {"i": {"x": 0.65, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 13.5], [21.75, -21.75], [21.75, -56.5]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.35, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 13.5], [21.75, -21.75], [21.75, -56.5]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.35, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [0, 11.75], [0, 0]], "o": [[0, 0], [0, -11.583], [0, 0]], "v": [[21.75, 31.5], [21.75, -21.75], [21.75, -74.5]], "c": false}]}, {"t": 71, "s": [{"i": [[0, 0], [0, 23.75], [0, 0]], "o": [[0, 0], [0, -23.583], [0, 0]], "v": [[29.75, 31.5], [21.75, -21.75], [29.75, -74.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.44], "y": [0]}, "t": -59, "s": [54]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.418], "y": [0]}, "t": -4, "s": [58]}, {"i": {"x": [0.149], "y": [1]}, "o": {"x": [0.529], "y": [0]}, "t": 0, "s": [58]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 20, "s": [27]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 67, "s": [27]}, {"t": 86, "s": [58], "h": 1}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.28], "y": [0.77]}, "o": {"x": [0.87], "y": [0.12]}, "t": 69, "s": [0]}, {"t": 83, "s": [49.5]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.28], "y": [0.77]}, "o": {"x": [0.87], "y": [0.12]}, "t": 69, "s": [100]}, {"t": 83, "s": [50.5]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -465, "s": [0]}, {"t": -447, "s": [90]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Bend", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 82, "st": -610, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -466, "s": [0]}, {"t": -456, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 0, "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.18], "y": [1]}, "t": 82, "s": [162]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.18], "y": [0]}, "t": 120, "s": [190]}, {"i": {"x": [0.7], "y": [1.01]}, "o": {"x": [1], "y": [0]}, "t": 201, "s": [190]}, {"t": 221, "s": [200]}], "ix": 4}}, "a": {"a": 0, "k": [0, -21.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.22, "y": 1}, "o": {"x": 0.18, "y": 1}, "t": 82, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-162.25, -21], [-162.25, -21]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.48, "y": 0}, "t": 110, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 120, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.25]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 151, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.437]], "c": false}]}, {"i": {"x": 0.33, "y": 1}, "o": {"x": 1, "y": 0}, "t": 154, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.437]], "c": false}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.55, "y": 0}, "t": 171, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.25]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 1, "y": 0}, "t": 185, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-257.375, -41.562]], "c": false}]}, {"i": {"x": 0.47, "y": 1}, "o": {"x": 0.34, "y": 0}, "t": 201, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-272.375, -37.562]], "c": false}]}, {"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 241, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.25]], "c": false}]}, {"t": 291, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -67.25]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.18], "y": [0.9]}, "t": 82, "s": [54]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 106, "s": [64]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 136, "s": [52]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 221, "s": [52]}, {"i": {"x": [0.56], "y": [1]}, "o": {"x": [0.88], "y": [0]}, "t": 241, "s": [50]}, {"t": 291, "s": [52]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -322, "s": [0]}, {"t": -304, "s": [90]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Right", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-162.25, -21], [-162.25, -21]], "c": false}], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-171.218, -21.242], [-162.226, -24.963]], "c": false}], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-179.437, -21.465], [-162.204, -28.595]], "c": false}], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-186.429, -21.654], [-162.185, -31.685]], "c": false}], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-191.962, -21.803], [-162.17, -34.13]], "c": false}], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-196.133, -21.916], [-162.158, -35.972]], "c": false}], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-199.213, -21.999], [-162.15, -37.334]], "c": false}], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-201.485, -22.06], [-162.144, -38.338]], "c": false}], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-203.175, -22.106], [-162.139, -39.084]], "c": false}], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-204.445, -22.14], [-162.136, -39.645]], "c": false}], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-205.408, -22.166], [-162.133, -40.071]], "c": false}], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.145, -22.186], [-162.131, -40.397]], "c": false}], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.712, -22.202], [-162.13, -40.647]], "c": false}], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.15, -22.214], [-162.129, -40.841]], "c": false}], "t": 95, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.489, -22.223], [-162.128, -40.991]], "c": false}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.75, -22.23], [-162.127, -41.106]], "c": false}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.952, -22.235], [-162.127, -41.195]], "c": false}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.106, -22.239], [-162.126, -41.263]], "c": false}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.223, -22.243], [-162.126, -41.315]], "c": false}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.31, -22.245], [-162.126, -41.354]], "c": false}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.374, -22.247], [-162.125, -41.382]], "c": false}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.453, -22.249], [-162.125, -41.417]], "c": false}], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.418]], "c": false}], "t": 121, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.358]], "c": false}], "t": 122, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.252]], "c": false}], "t": 123, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.094]], "c": false}], "t": 124, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -40.877]], "c": false}], "t": 125, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -40.59]], "c": false}], "t": 126, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -40.224]], "c": false}], "t": 127, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -39.761]], "c": false}], "t": 128, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -39.181]], "c": false}], "t": 129, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -38.454]], "c": false}], "t": 130, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -37.534]], "c": false}], "t": 131, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -36.35]], "c": false}], "t": 132, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -34.776]], "c": false}], "t": 133, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -32.559]], "c": false}], "t": 134, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -29.136]], "c": false}], "t": 135, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -24.536]], "c": false}], "t": 136, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.25]], "c": false}], "t": 137, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.437]], "c": false}], "t": 151, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.414]], "c": false}], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.338]], "c": false}], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -41.199]], "c": false}], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -40.983]], "c": false}], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -40.668]], "c": false}], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -40.224]], "c": false}], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -39.598]], "c": false}], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -38.698]], "c": false}], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -37.322]], "c": false}], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -34.857]], "c": false}], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -28.947]], "c": false}], "t": 165, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -25.094]], "c": false}], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -23.637]], "c": false}], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.895]], "c": false}], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.497]], "c": false}], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.305]], "c": false}], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.125, -22.25]], "c": false}], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-162.753, -22.377]], "c": false}], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-165.208, -22.875]], "c": false}], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-171.361, -24.123]], "c": false}], "t": 174, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-188.488, -27.595]], "c": false}], "t": 175, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-219.099, -33.802]], "c": false}], "t": 176, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-234.244, -36.873]], "c": false}], "t": 177, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-242.564, -38.559]], "c": false}], "t": 178, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-247.877, -39.637]], "c": false}], "t": 179, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-251.472, -40.366]], "c": false}], "t": 180, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-253.937, -40.865]], "c": false}], "t": 181, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-255.594, -41.201]], "c": false}], "t": 182, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-256.64, -41.413]], "c": false}], "t": 183, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.5, -22.25], [-257.203, -41.528]], "c": false}], "t": 184, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.455, -22.25], [-257.459, -41.54]], "c": false}], "t": 187, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.395, -22.25], [-257.572, -41.51]], "c": false}], "t": 188, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.305, -22.25], [-257.74, -41.465]], "c": false}], "t": 189, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.181, -22.25], [-257.973, -41.403]], "c": false}], "t": 190, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-208.018, -22.25], [-258.279, -41.321]], "c": false}], "t": 191, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.808, -22.25], [-258.673, -41.216]], "c": false}], "t": 192, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.541, -22.25], [-259.174, -41.083]], "c": false}], "t": 193, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207.203, -22.25], [-259.806, -40.914]], "c": false}], "t": 194, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.776, -22.25], [-260.608, -40.7]], "c": false}], "t": 195, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-206.227, -22.25], [-261.637, -40.426]], "c": false}], "t": 196, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-205.502, -22.25], [-262.996, -40.064]], "c": false}], "t": 197, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-204.5, -22.25], [-264.875, -39.562]], "c": false}], "t": 198, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-203.002, -22.25], [-267.683, -38.814]], "c": false}], "t": 199, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-201.093, -22.25], [-271.264, -37.859]], "c": false}], "t": 200, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-272.375, -37.562]], "c": false}], "t": 201, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-272.244, -37.534]], "c": false}], "t": 202, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-271.843, -37.449]], "c": false}], "t": 203, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-271.165, -37.305]], "c": false}], "t": 204, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-270.202, -37.1]], "c": false}], "t": 205, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-268.952, -36.833]], "c": false}], "t": 206, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-267.415, -36.506]], "c": false}], "t": 207, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-265.596, -36.118]], "c": false}], "t": 208, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-263.505, -35.673]], "c": false}], "t": 209, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-261.16, -35.173]], "c": false}], "t": 210, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-258.583, -34.624]], "c": false}], "t": 211, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-255.802, -34.032]], "c": false}], "t": 212, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-252.851, -33.403]], "c": false}], "t": 213, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-249.768, -32.746]], "c": false}], "t": 214, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-246.593, -32.07]], "c": false}], "t": 215, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-243.367, -31.382]], "c": false}], "t": 216, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-240.129, -30.693]], "c": false}], "t": 217, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-236.915, -30.008]], "c": false}], "t": 218, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-233.76, -29.336]], "c": false}], "t": 219, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-230.692, -28.682]], "c": false}], "t": 220, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-227.734, -28.052]], "c": false}], "t": 221, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-224.906, -27.449]], "c": false}], "t": 222, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-222.222, -26.878]], "c": false}], "t": 223, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-219.692, -26.339]], "c": false}], "t": 224, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-217.325, -25.834]], "c": false}], "t": 225, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-215.123, -25.365]], "c": false}], "t": 226, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-213.089, -24.932]], "c": false}], "t": 227, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-211.222, -24.534]], "c": false}], "t": 228, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-209.521, -24.172]], "c": false}], "t": 229, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-207.982, -23.844]], "c": false}], "t": 230, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-206.602, -23.55]], "c": false}], "t": 231, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-205.377, -23.289]], "c": false}], "t": 232, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-204.302, -23.06]], "c": false}], "t": 233, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-203.371, -22.862]], "c": false}], "t": 234, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-202.581, -22.693]], "c": false}], "t": 235, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-201.926, -22.554]], "c": false}], "t": 236, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-201.4, -22.442]], "c": false}], "t": 237, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.999, -22.356]], "c": false}], "t": 238, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.719, -22.297]], "c": false}], "t": 239, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.554, -22.262]], "c": false}], "t": 240, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.258]], "c": false}], "t": 242, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.282]], "c": false}], "t": 243, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.323]], "c": false}], "t": 244, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.382]], "c": false}], "t": 245, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.46]], "c": false}], "t": 246, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.558]], "c": false}], "t": 247, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.677]], "c": false}], "t": 248, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.819]], "c": false}], "t": 249, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -22.984]], "c": false}], "t": 250, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -23.174]], "c": false}], "t": 251, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -23.391]], "c": false}], "t": 252, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -23.637]], "c": false}], "t": 253, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -23.913]], "c": false}], "t": 254, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -24.223]], "c": false}], "t": 255, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -24.568]], "c": false}], "t": 256, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -24.951]], "c": false}], "t": 257, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -25.376]], "c": false}], "t": 258, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -25.846]], "c": false}], "t": 259, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -26.366]], "c": false}], "t": 260, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -26.94]], "c": false}], "t": 261, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -27.574]], "c": false}], "t": 262, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -28.274]], "c": false}], "t": 263, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -29.049]], "c": false}], "t": 264, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -29.907]], "c": false}], "t": 265, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -30.859]], "c": false}], "t": 266, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -31.918]], "c": false}], "t": 267, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -33.101]], "c": false}], "t": 268, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -34.425]], "c": false}], "t": 269, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -35.914]], "c": false}], "t": 270, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -37.595]], "c": false}], "t": 271, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -39.5]], "c": false}], "t": 272, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -41.659]], "c": false}], "t": 273, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -44.097]], "c": false}], "t": 274, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -46.807]], "c": false}], "t": 275, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -49.72]], "c": false}], "t": 276, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -52.682]], "c": false}], "t": 277, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -55.485]], "c": false}], "t": 278, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -57.961]], "c": false}], "t": 279, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -60.044]], "c": false}], "t": 280, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -61.745]], "c": false}], "t": 281, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -63.113]], "c": false}], "t": 282, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -64.205]], "c": false}], "t": 283, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -65.068]], "c": false}], "t": 284, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -65.743]], "c": false}], "t": 285, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -66.262]], "c": false}], "t": 286, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -66.651]], "c": false}], "t": 287, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -66.93]], "c": false}], "t": 288, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -67.114]], "c": false}], "t": 289, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-200.5, -22.25], [-200.5, -67.218]], "c": false}], "t": 290, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"k": [{"s": [54], "t": 82, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [56.043], "t": 83, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [57.904], "t": 84, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [59.441], "t": 85, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [60.61], "t": 86, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [61.465], "t": 87, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.085], "t": 88, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.541], "t": 89, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.882], "t": 90, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.14], "t": 91, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.338], "t": 92, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.492], "t": 93, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.612], "t": 94, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.706], "t": 95, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.78], "t": 96, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.838], "t": 97, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.883], "t": 98, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.945], "t": 100, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.932], "t": 109, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.876], "t": 110, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.8], "t": 111, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.703], "t": 112, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.582], "t": 113, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.435], "t": 114, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.259], "t": 115, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.049], "t": 116, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.802], "t": 117, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.512], "t": 118, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.171], "t": 119, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [61.773], "t": 120, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [61.307], "t": 121, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [60.76], "t": 122, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [60.117], "t": 123, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [59.363], "t": 124, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [58.485], "t": 125, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [57.487], "t": 126, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [56.408], "t": 127, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [55.337], "t": 128, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.382], "t": 129, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [53.607], "t": 130, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [53.021], "t": 131, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52.599], "t": 132, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52.31], "t": 133, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52.128], "t": 134, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52.03], "t": 135, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52], "t": 221, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.963], "t": 222, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.888], "t": 223, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.796], "t": 224, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.694], "t": 225, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.586], "t": 226, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.473], "t": 227, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.357], "t": 228, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.239], "t": 229, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.12], "t": 230, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.88], "t": 232, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.761], "t": 233, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.643], "t": 234, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.527], "t": 235, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.414], "t": 236, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.306], "t": 237, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.204], "t": 238, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.112], "t": 239, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.037], "t": 240, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.074], "t": 254, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.12], "t": 257, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.16], "t": 259, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.208], "t": 261, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.237], "t": 262, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.268], "t": 263, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.302], "t": 264, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.34], "t": 265, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.383], "t": 266, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.43], "t": 267, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.482], "t": 268, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.541], "t": 269, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.607], "t": 270, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.682], "t": 271, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.767], "t": 272, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.863], "t": 273, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.971], "t": 274, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.091], "t": 275, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.221], "t": 276, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.353], "t": 277, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.477], "t": 278, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.587], "t": 279, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.68], "t": 280, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.755], "t": 281, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.816], "t": 282, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.865], "t": 283, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.903], "t": 284, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.933], "t": 285, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.973], "t": 287, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, -100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 1, "k": [{"t": -467, "s": [0], "h": 1}, {"t": -304, "s": [100], "h": 1}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Left", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.22, "y": 1}, "o": {"x": 0.18, "y": 1}, "t": 82, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.862, -261.879], [0.055, -282.955], [11.354, -261.879]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.18, "y": 0}, "t": 110, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.915, -246.504], [0.001, -267.58], [11.3, -246.504]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 120, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.915, -246.504], [0.001, -267.58], [11.3, -246.504]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [{"i": [[4.907, -3.179], [-12.215, 0], [3.984, 2.581]], "o": [[-4.447, 2.881], [11.035, 0], [-4.907, -3.179]], "v": [[-7.665, -246.504], [-0.083, -267.58], [7.766, -246.504]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.915, -246.504], [0.001, -267.58], [11.3, -246.504]], "c": true}]}, {"i": {"x": 0.33, "y": 1}, "o": {"x": 1, "y": 0}, "t": 154, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.915, -246.504], [0.001, -267.58], [11.3, -246.504]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.55, "y": 0}, "t": 171, "s": [{"i": [[4.907, -3.179], [-12.215, 0], [3.984, 2.581]], "o": [[-4.447, 2.881], [11.035, 0], [-4.907, -3.179]], "v": [[-7.665, -246.504], [-0.083, -267.58], [7.766, -246.504]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 1, "y": 0}, "t": 185, "s": [{"i": [[7.064, 3.179], [-17.586, 0], [5.736, -2.581]], "o": [[-6.403, -2.881], [15.887, 0], [-7.064, 3.179]], "v": [[-10.865, -389.406], [0.052, -368.33], [11.351, -389.406]], "c": true}]}, {"i": {"x": 0.47, "y": 1}, "o": {"x": 0.34, "y": 0}, "t": 201, "s": [{"i": [[5.881, 3.179], [-14.64, 0], [4.775, -2.581]], "o": [[-5.33, -2.881], [13.225, 0], [-5.881, 3.179]], "v": [[-9.107, -406.156], [-0.02, -385.08], [9.387, -406.156]], "c": true}]}, {"t": 241, "s": [{"i": [[4.598, 3.179], [-11.448, 0], [3.734, -2.581]], "o": [[-4.168, -2.881], [10.342, 0], [-4.598, 3.179]], "v": [[-7.195, -320.656], [-0.089, -299.58], [7.266, -320.656]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.66, "y": 1}, "o": {"x": 0.64, "y": 0}, "t": 120, "s": [0, 92], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.48, "y": 1}, "o": {"x": 0.55, "y": 0}, "t": 137, "s": [0, 82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.48, "y": 0.48}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [0, 87], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.66, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [0, 87], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.48, "y": 1}, "o": {"x": 0.55, "y": 0}, "t": 171, "s": [0, 85], "to": [0, 0], "ti": [0, 0]}, {"t": 185, "s": [0, 87]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Bottom Rounding", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 82, "op": 241, "st": -467, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 241, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 247, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 935, "s": [100]}, {"t": 941, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -21.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 241, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -21.75], [0, -21.75]], "c": false}]}, {"t": 291, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[45, -21.75], [-45, -21.75]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 52, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Right", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -21.75], [0, -21.75]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 64, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 0, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Left", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 241, "op": 254, "st": 241, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": ".onSurfaceHome", "cl": "onSurfaceHome", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 19, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 49, "s": [0]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 71, "s": [100]}, {"t": 80, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 1429, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rectangle Independent Corners", "np": 19, "mn": "Pseudo/0.16410552199068107", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Align", "mn": "Pseudo/0.16410552199068107-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}, {"ty": 6, "nm": "Size", "mn": "Pseudo/0.16410552199068107-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "w", "mn": "Pseudo/0.16410552199068107-0003", "ix": 3, "v": {"a": 0, "k": 414, "ix": 3}}, {"ty": 0, "nm": "h", "mn": "Pseudo/0.16410552199068107-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.8], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 53, "s": [581]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.312], "y": [4.051]}, "t": 71, "s": [910]}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 80, "s": [910]}, {"t": 110, "s": [581]}], "ix": 4}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Rounding", "mn": "Pseudo/0.16410552199068107-0006", "ix": 6, "v": 0}, {"ty": 0, "nm": "tl", "mn": "Pseudo/0.16410552199068107-0007", "ix": 7, "v": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 53, "s": [90]}, {"t": 71, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 80, "s": [200]}, {"t": 110, "s": [90]}], "ix": 7}}, {"ty": 0, "nm": "tr", "mn": "Pseudo/0.16410552199068107-0008", "ix": 8, "v": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 53, "s": [90]}, {"t": 71, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 80, "s": [200]}, {"t": 110, "s": [90]}], "ix": 8}}, {"ty": 0, "nm": "br", "mn": "Pseudo/0.16410552199068107-0009", "ix": 9, "v": {"a": 0, "k": 135, "ix": 9}}, {"ty": 0, "nm": "bl", "mn": "Pseudo/0.16410552199068107-0010", "ix": 10, "v": {"a": 0, "k": 135, "ix": 10}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Alignment", "mn": "Pseudo/0.16410552199068107-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "X Anchor %", "mn": "Pseudo/0.16410552199068107-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 0, "nm": "Y Anchor %", "mn": "Pseudo/0.16410552199068107-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "X Position", "mn": "Pseudo/0.16410552199068107-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15}}, {"ty": 0, "nm": "Y Position", "mn": "Pseudo/0.16410552199068107-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0017", "ix": 17, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.772, 0], [0, 0], [0, -49.772], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.772], [0, 0], [49.772, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.167], [-116.817, -581.35], [116.817, -581.35], [207, -491.167], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.09, 0], [0, 0], [0, -50.09], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.09], [0, 0], [50.09, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.689], [-116.241, -582.448], [116.241, -582.448], [207, -491.689], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.651, 0], [0, 0], [0, -50.651], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.651], [0, 0], [50.651, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.605], [-115.225, -584.38], [115.225, -584.38], [207, -492.605], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.485, 0], [0, 0], [0, -51.485], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -51.485], [0, 0], [51.485, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -493.959], [-113.713, -587.246], [113.713, -587.246], [207, -493.959], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-52.63, 0], [0, 0], [0, -52.63], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -52.63], [0, 0], [52.63, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.808], [-111.638, -591.17], [111.638, -591.17], [207, -495.808], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.132, 0], [0, 0], [0, -54.133], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -54.133], [0, 0], [54.133, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.219], [-108.916, -596.303], [108.916, -596.303], [207, -498.219], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.05, 0], [0, 0], [0, -56.05], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -56.05], [0, 0], [56.05, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -501.281], [-105.442, -602.839], [105.442, -602.839], [207, -501.281], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.455, 0], [0, 0], [0, -58.455], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.455], [0, 0], [58.455, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -505.109], [-101.083, -611.026], [101.083, -611.026], [207, -505.109], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.443, 0], [0, 0], [0, -61.443], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -61.443], [0, 0], [61.443, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -509.862], [-95.67, -621.193], [95.669, -621.193], [207, -509.862], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-65.136, 0], [0, 0], [0, -65.136], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -65.136], [0, 0], [65.136, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.772], [-88.979, -633.792], [88.979, -633.792], [207, -515.772], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.689, 0], [0, 0], [0, -69.689], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -69.689], [0, 0], [69.689, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.201], [-80.73, -649.472], [80.73, -649.472], [207, -523.201], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-75.292, 0], [0, 0], [0, -75.292], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -75.292], [0, 0], [75.292, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.789], [-70.578, -669.211], [70.578, -669.211], [207, -532.789], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-82.119, 0], [0, 0], [0, -82.119], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -82.119], [0, 0], [82.119, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -545.809], [-58.206, -694.603], [58.206, -694.603], [207, -545.809], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.124, 0], [0, 0], [0, -90.124], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -90.124], [0, 0], [90.124, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -565.184], [-43.703, -728.481], [43.703, -728.481], [207, -565.184], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-98.484, 0], [0, 0], [0, -98.484], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -98.484], [0, 0], [98.484, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -597.873], [-28.555, -776.318], [28.555, -776.318], [207, -597.873], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.33, 0], [0, 0], [0, -105.33], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.33], [0, 0], [105.33, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -653.5], [-16.151, -844.349], [16.151, -844.349], [207, -653.5], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.245, 0], [0, 0], [0, -109.245], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.245], [0, 0], [109.245, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -700.008], [-9.056, -897.952], [9.056, -897.952], [207, -700.008], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.387], [-7, -911.387], [7, -911.387], [207, -711.387], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.036], [-7, -911.036], [7, -911.036], [207, -711.036], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.362], [-7, -910.362], [7, -910.362], [207, -710.362], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 76, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 80, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.251, 0], [0, 0], [0, -110.251], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.251], [0, 0], [110.251, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -709.533], [-7.234, -909.299], [7.234, -909.299], [207, -709.533], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 81, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.855, 0], [0, 0], [0, -109.855], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.855], [0, 0], [109.855, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -708.106], [-7.951, -907.155], [7.951, -907.155], [207, -708.106], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.183, 0], [0, 0], [0, -109.183], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.183], [0, 0], [109.183, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -705.681], [-9.169, -903.511], [9.169, -903.511], [207, -705.681], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-108.224, 0], [0, 0], [0, -108.224], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -108.224], [0, 0], [108.224, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -702.223], [-10.906, -898.317], [10.906, -898.317], [207, -702.223], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-106.972, 0], [0, 0], [0, -106.972], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -106.972], [0, 0], [106.972, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -697.707], [-13.174, -891.533], [13.174, -891.533], [207, -697.707], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.423, 0], [0, 0], [0, -105.422], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.422], [0, 0], [105.423, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -692.117], [-15.983, -883.134], [15.983, -883.134], [207, -692.117], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-103.574, 0], [0, 0], [0, -103.574], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -103.574], [0, 0], [103.574, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -685.448], [-19.332, -873.116], [19.332, -873.116], [207, -685.448], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-101.431, 0], [0, 0], [0, -101.431], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -101.431], [0, 0], [101.431, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -677.717], [-23.215, -861.502], [23.215, -861.502], [207, -677.717], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-99.003, 0], [0, 0], [0, -99.003], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -99.003], [0, 0], [99.003, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -668.96], [-27.614, -848.347], [27.614, -848.347], [207, -668.96], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-96.309, 0], [0, 0], [0, -96.309], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -96.309], [0, 0], [96.309, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -659.241], [-32.495, -833.746], [32.495, -833.746], [207, -659.241], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-93.374, 0], [0, 0], [0, -93.374], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -93.374], [0, 0], [93.374, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -648.653], [-37.814, -817.839], [37.814, -817.839], [207, -648.653], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.232, 0], [0, 0], [0, -90.232], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -90.232], [0, 0], [90.232, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -637.317], [-43.507, -800.81], [43.507, -800.81], [207, -637.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-86.925, 0], [0, 0], [0, -86.925], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -86.925], [0, 0], [86.925, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -625.39], [-49.498, -782.892], [49.498, -782.892], [207, -625.39], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-83.505, 0], [0, 0], [0, -83.505], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -83.505], [0, 0], [83.505, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -613.051], [-55.696, -764.355], [55.696, -764.355], [207, -613.051], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-76.546, 0], [0, 0], [0, -76.546], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -76.546], [0, 0], [76.546, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -587.949], [-68.304, -726.645], [68.304, -726.645], [207, -587.949], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-73.126, 0], [0, 0], [0, -73.126], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -73.126], [0, 0], [73.126, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -575.61], [-74.502, -708.108], [74.502, -708.108], [207, -575.61], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.819, 0], [0, 0], [0, -69.819], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -69.819], [0, 0], [69.819, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -563.683], [-80.493, -690.19], [80.493, -690.19], [207, -563.683], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-66.677, 0], [0, 0], [0, -66.677], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -66.677], [0, 0], [66.677, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -552.347], [-86.186, -673.161], [86.186, -673.161], [207, -552.347], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-63.742, 0], [0, 0], [0, -63.742], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -63.742], [0, 0], [63.742, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -541.759], [-91.505, -657.254], [91.505, -657.254], [207, -541.759], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.048, 0], [0, 0], [0, -61.048], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -61.048], [0, 0], [61.048, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.04], [-96.386, -642.653], [96.386, -642.653], [207, -532.04], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.62, 0], [0, 0], [0, -58.62], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.62], [0, 0], [58.62, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.283], [-100.785, -629.498], [100.785, -629.498], [207, -523.283], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.477, 0], [0, 0], [0, -56.477], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -56.477], [0, 0], [56.477, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.552], [-104.668, -617.884], [104.668, -617.884], [207, -515.552], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.628, 0], [0, 0], [0, -54.629], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -54.629], [0, 0], [54.628, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -508.884], [-108.017, -607.866], [108.017, -607.866], [207, -508.884], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-53.079, 0], [0, 0], [0, -53.079], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -53.079], [0, 0], [53.079, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -503.293], [-110.826, -599.467], [110.826, -599.467], [207, -503.293], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.827, 0], [0, 0], [0, -51.827], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -51.827], [0, 0], [51.827, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.777], [-113.094, -592.683], [113.094, -592.683], [207, -498.777], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 106, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.868, 0], [0, 0], [0, -50.868], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.868], [0, 0], [50.868, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.319], [-114.831, -587.489], [114.831, -587.489], [207, -495.319], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.196, 0], [0, 0], [0, -50.196], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.196], [0, 0], [50.196, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.894], [-116.049, -583.845], [116.049, -583.845], [207, -492.894], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 108, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.8, 0], [0, 0], [0, -49.8], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.8], [0, 0], [49.8, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.467], [-116.766, -581.701], [116.766, -581.701], [207, -491.467], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 109, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 110, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.925, 0.753, 0.424, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.925, 0.753, 0.424, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"k": [{"s": [0, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0], "t": 394, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 11986", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 7, "op": 110, "st": -110, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": ".onSurfaceHome", "cl": "onSurfaceHome", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [95]}, {"t": 100, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 1376, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rectangle Independent Corners", "np": 19, "mn": "Pseudo/0.16410552199068107", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Align", "mn": "Pseudo/0.16410552199068107-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}, {"ty": 6, "nm": "Size", "mn": "Pseudo/0.16410552199068107-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "w", "mn": "Pseudo/0.16410552199068107-0003", "ix": 3, "v": {"a": 0, "k": 414, "ix": 3}}, {"ty": 0, "nm": "h", "mn": "Pseudo/0.16410552199068107-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.268]}, "t": 71, "s": [857]}, {"t": 109, "s": [1204]}], "ix": 4}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Rounding", "mn": "Pseudo/0.16410552199068107-0006", "ix": 6, "v": 0}, {"ty": 0, "nm": "tl", "mn": "Pseudo/0.16410552199068107-0007", "ix": 7, "v": {"a": 0, "k": 200, "ix": 7}}, {"ty": 0, "nm": "tr", "mn": "Pseudo/0.16410552199068107-0008", "ix": 8, "v": {"a": 0, "k": 200, "ix": 8}}, {"ty": 0, "nm": "br", "mn": "Pseudo/0.16410552199068107-0009", "ix": 9, "v": {"a": 0, "k": 135, "ix": 9}}, {"ty": 0, "nm": "bl", "mn": "Pseudo/0.16410552199068107-0010", "ix": 10, "v": {"a": 0, "k": 135, "ix": 10}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Alignment", "mn": "Pseudo/0.16410552199068107-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "X Anchor %", "mn": "Pseudo/0.16410552199068107-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 0, "nm": "Y Anchor %", "mn": "Pseudo/0.16410552199068107-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "X Position", "mn": "Pseudo/0.16410552199068107-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15}}, {"ty": 0, "nm": "Y Position", "mn": "Pseudo/0.16410552199068107-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0017", "ix": 17, "v": 0}]}, {"ty": 5, "nm": "Transform", "np": 14, "mn": "ADBE Geometry2", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Anchor Point", "mn": "ADBE Geometry2-0001", "ix": 1, "v": {"a": 0, "k": [206, 446], "ix": 1}}, {"ty": 3, "nm": "Position", "mn": "ADBE Geometry2-0002", "ix": 2, "v": {"a": 0, "k": [206, 446], "ix": 2}}, {"ty": 7, "nm": "Uniform Scale", "mn": "ADBE Geometry2-0011", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Scale Height", "mn": "ADBE Geometry2-0003", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 0, "nm": "Scale Width", "mn": "ADBE Geometry2-0004", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "Skew", "mn": "ADBE Geometry2-0005", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Skew Axis", "mn": "ADBE Geometry2-0006", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 0, "nm": "Rotation", "mn": "ADBE Geometry2-0007", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Geometry2-0008", "ix": 9, "v": {"a": 0, "k": 50, "ix": 9}}, {"ty": 7, "nm": "Use Composition’s Shutter Angle", "mn": "ADBE Geometry2-0009", "ix": 10, "v": {"a": 0, "k": 1, "ix": 10}}, {"ty": 0, "nm": "<PERSON>ter <PERSON>", "mn": "ADBE Geometry2-0010", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11}}, {"ty": 7, "nm": "Sampling", "mn": "ADBE Geometry2-0012", "ix": 12, "v": {"a": 0, "k": 1, "ix": 12}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -657], [-7, -857], [7, -857], [207, -657], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.063], [-7, -910.063], [7, -910.063], [207, -710.063], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -753.007], [-7, -953.007], [7, -953.007], [207, -753.007], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -785.49], [-7, -985.49], [7, -985.49], [207, -785.49], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -811.374], [-7, -1011.374], [7, -1011.374], [207, -811.374], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -832.839], [-7, -1032.839], [7, -1032.839], [207, -832.839], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 76, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -851.125], [-7, -1051.125], [7, -1051.125], [207, -851.125], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 77, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -866.998], [-7, -1066.998], [7, -1066.998], [207, -866.998], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 78, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -880.965], [-7, -1080.965], [7, -1080.965], [207, -880.965], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 79, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -893.379], [-7, -1093.379], [7, -1093.379], [207, -893.379], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 80, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -904.497], [-7, -1104.497], [7, -1104.497], [207, -904.497], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 81, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -914.513], [-7, -1114.513], [7, -1114.513], [207, -914.513], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -923.578], [-7, -1123.578], [7, -1123.578], [207, -923.578], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -931.81], [-7, -1131.81], [7, -1131.81], [207, -931.81], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -939.307], [-7, -1139.307], [7, -1139.307], [207, -939.307], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -946.146], [-7, -1146.146], [7, -1146.146], [207, -946.146], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -952.394], [-7, -1152.394], [7, -1152.394], [207, -952.394], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -958.107], [-7, -1158.106], [7, -1158.106], [207, -958.107], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -963.331], [-7, -1163.331], [7, -1163.331], [207, -963.331], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -968.107], [-7, -1168.107], [7, -1168.107], [207, -968.107], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -972.472], [-7, -1172.472], [7, -1172.472], [207, -972.472], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -976.455], [-7, -1176.455], [7, -1176.455], [207, -976.455], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -980.085], [-7, -1180.085], [7, -1180.085], [207, -980.085], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -983.384], [-7, -1183.384], [7, -1183.384], [207, -983.384], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -986.375], [-7, -1186.375], [7, -1186.375], [207, -986.375], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 95, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -989.077], [-7, -1189.078], [7, -1189.078], [207, -989.077], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -991.508], [-7, -1191.508], [7, -1191.508], [207, -991.508], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -993.682], [-7, -1193.682], [7, -1193.682], [207, -993.682], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -995.614], [-7, -1195.614], [7, -1195.614], [207, -995.614], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -997.317], [-7, -1197.317], [7, -1197.317], [207, -997.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -998.803], [-7, -1198.803], [7, -1198.803], [207, -998.803], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1000.082], [-7, -1200.082], [7, -1200.082], [207, -1000.082], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1001.164], [-7, -1201.165], [7, -1201.165], [207, -1001.164], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1002.06], [-7, -1202.06], [7, -1202.06], [207, -1002.06], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1002.776], [-7, -1202.776], [7, -1202.776], [207, -1002.776], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1003.702], [-7, -1203.702], [7, -1203.702], [207, -1003.702], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.925, 0.753, 0.424, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.925, 0.753, 0.424, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"k": [{"s": [0, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0], "t": 394, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 11986", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 70, "op": 109, "st": -110, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": ".surfaceHome", "cl": "surfaceHome", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"a": 0, "k": 446, "ix": 4}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [412, 892], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.365, 0.259, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.365, 0.259, 0, 1], "t": 394, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Volume", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1500, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_1", "nm": "Part02_Charade_Loop_V02", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Secondary Y Movement", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [807.709]}, {"i": {"x": [0.12], "y": [1]}, "o": {"x": [0.44], "y": [0]}, "t": 40.334, "s": [814.909]}, {"t": 52, "s": [807.709]}], "ix": 4}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Void", "np": 20, "mn": "Pseudo/289611", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/289611-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1}}, {"ty": 0, "nm": "Height", "mn": "Pseudo/289611-0002", "ix": 2, "v": {"a": 0, "k": 100, "ix": 2}}, {"ty": 0, "nm": "Offset X", "mn": "Pseudo/289611-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Offset Y", "mn": "Pseudo/289611-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "About", "mn": "Pseudo/289611-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Plague of null layers.", "mn": "Pseudo/289611-0006", "ix": 6, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Following projects", "mn": "Pseudo/289611-0008", "ix": 8, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0009", "ix": 9, "v": 0}, {"ty": 6, "nm": "through time.", "mn": "Pseudo/289611-0010", "ix": 10, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Be free of the past.", "mn": "Pseudo/289611-0012", "ix": 12, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0013", "ix": 13, "v": 0}, {"ty": 6, "nm": "Void - 1.0.0", "mn": "Pseudo/289611-0014", "ix": 14, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0015", "ix": 15, "v": 0}, {"ty": 6, "nm": "© 2022 Battle Axe Inc", "mn": "Pseudo/289611-0016", "ix": 16, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0017", "ix": 17, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0018", "ix": 18, "v": 0}]}], "ip": 0, "op": 467, "st": -145, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "MASTER Y POSITION", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 0, "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.426], "y": [0.38]}, "o": {"x": [0.48], "y": [0.051]}, "t": 0, "s": [-97.709]}, {"i": {"x": [0.633], "y": [1]}, "o": {"x": [0.654], "y": [-0.375]}, "t": 23, "s": [-103.709]}, {"i": {"x": [0.8], "y": [0.15]}, "o": {"x": [0.3], "y": [0]}, "t": 38, "s": [-92.709]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.7]}, "t": 44.666, "s": [-50.709]}, {"i": {"x": [0.15], "y": [1]}, "o": {"x": [0.52], "y": [0]}, "t": 78, "s": [12.291]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 109, "s": [-7.709]}, {"i": {"x": [0.8], "y": [0.764]}, "o": {"x": [0.3], "y": [0]}, "t": 118, "s": [-7.709]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.2], "y": [0]}, "t": 128, "s": [64.291]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.78], "y": [0]}, "t": 143, "s": [46.291]}, {"i": {"x": [0.8], "y": [0.528]}, "o": {"x": [0.3], "y": [0]}, "t": 145, "s": [46.291]}, {"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.18], "y": [1]}, "t": 155, "s": [64.291]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [-7.709]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.52], "y": [0]}, "t": 195, "s": [-7.709]}, {"i": {"x": [0.428], "y": [1]}, "o": {"x": [0.681], "y": [0]}, "t": 235, "s": [-261.709]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 290, "s": [-71.709]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.52], "y": [0]}, "t": 294, "s": [-71.709]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 334, "s": [-261.709]}, {"i": {"x": [0.473], "y": [1.533]}, "o": {"x": [0.63], "y": [0]}, "t": 343, "s": [-261.709]}, {"i": {"x": [0.105], "y": [1]}, "o": {"x": [0.497], "y": [-0.207]}, "t": 406, "s": [-157.709]}, {"t": 466, "s": [62.291]}], "ix": 4}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Void", "np": 20, "mn": "Pseudo/289611", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/289611-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1}}, {"ty": 0, "nm": "Height", "mn": "Pseudo/289611-0002", "ix": 2, "v": {"a": 0, "k": 100, "ix": 2}}, {"ty": 0, "nm": "Offset X", "mn": "Pseudo/289611-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Offset Y", "mn": "Pseudo/289611-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "About", "mn": "Pseudo/289611-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Plague of null layers.", "mn": "Pseudo/289611-0006", "ix": 6, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Following projects", "mn": "Pseudo/289611-0008", "ix": 8, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0009", "ix": 9, "v": 0}, {"ty": 6, "nm": "through time.", "mn": "Pseudo/289611-0010", "ix": 10, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Be free of the past.", "mn": "Pseudo/289611-0012", "ix": 12, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0013", "ix": 13, "v": 0}, {"ty": 6, "nm": "Void - 1.0.0", "mn": "Pseudo/289611-0014", "ix": 14, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0015", "ix": 15, "v": 0}, {"ty": 6, "nm": "© 2022 Battle Axe Inc", "mn": "Pseudo/289611-0016", "ix": 16, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0017", "ix": 17, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0018", "ix": 18, "v": 0}]}], "ip": 0, "op": 467, "st": -145, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": ".onSurfaceHome", "cl": "onSurfaceHome", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 72, "s": [100]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 84, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 96, "s": [100]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 162, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 174, "s": [100]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 186, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 198, "s": [100]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 210, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 264, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 276, "s": [100]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 288, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 300, "s": [100]}, {"t": 312, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 1429, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rectangle Independent Corners", "np": 19, "mn": "Pseudo/0.16410552199068107", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Align", "mn": "Pseudo/0.16410552199068107-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}, {"ty": 6, "nm": "Size", "mn": "Pseudo/0.16410552199068107-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "w", "mn": "Pseudo/0.16410552199068107-0003", "ix": 3, "v": {"a": 0, "k": 414, "ix": 3}}, {"ty": 0, "nm": "h", "mn": "Pseudo/0.16410552199068107-0004", "ix": 4, "v": {"a": 0, "k": 581, "ix": 4}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Rounding", "mn": "Pseudo/0.16410552199068107-0006", "ix": 6, "v": 0}, {"ty": 0, "nm": "tl", "mn": "Pseudo/0.16410552199068107-0007", "ix": 7, "v": {"a": 0, "k": 90, "ix": 7}}, {"ty": 0, "nm": "tr", "mn": "Pseudo/0.16410552199068107-0008", "ix": 8, "v": {"a": 0, "k": 90, "ix": 8}}, {"ty": 0, "nm": "br", "mn": "Pseudo/0.16410552199068107-0009", "ix": 9, "v": {"a": 0, "k": 135, "ix": 9}}, {"ty": 0, "nm": "bl", "mn": "Pseudo/0.16410552199068107-0010", "ix": 10, "v": {"a": 0, "k": 135, "ix": 10}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Alignment", "mn": "Pseudo/0.16410552199068107-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "X Anchor %", "mn": "Pseudo/0.16410552199068107-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 0, "nm": "Y Anchor %", "mn": "Pseudo/0.16410552199068107-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "X Position", "mn": "Pseudo/0.16410552199068107-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15}}, {"ty": 0, "nm": "Y Position", "mn": "Pseudo/0.16410552199068107-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0017", "ix": 17, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 466, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.925, 0.753, 0.424, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.925, 0.753, 0.424, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"k": [{"s": [0, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0], "t": 466, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 11986", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 312, "st": -205, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -144, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -134, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 549, "s": [100]}, {"t": 555, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.108}, "t": 426, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 465, "s": [0, -10.9, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -21.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.44, "y": 0}, "t": 406, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [55.625, -40.25]], "c": false}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 426, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -36.75]], "c": false}]}, {"t": 465, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.83], "y": [0.83]}, "o": {"x": [0.44], "y": [0]}, "t": 406, "s": [54]}, {"t": 426, "s": [58]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 18, "s": [90]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Right", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [55.625, -40.25]], "c": false}], "t": 406, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [55.461, -40.238]], "c": false}], "t": 407, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [54.979, -40.201]], "c": false}], "t": 408, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [54.191, -40.142]], "c": false}], "t": 409, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [53.112, -40.06]], "c": false}], "t": 410, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [51.754, -39.958]], "c": false}], "t": 411, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [50.13, -39.835]], "c": false}], "t": 412, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [48.251, -39.693]], "c": false}], "t": 413, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [46.13, -39.533]], "c": false}], "t": 414, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [43.779, -39.356]], "c": false}], "t": 415, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [41.212, -39.162]], "c": false}], "t": 416, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [38.443, -38.953]], "c": false}], "t": 417, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [35.488, -38.73]], "c": false}], "t": 418, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [32.365, -38.494]], "c": false}], "t": 419, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [29.095, -38.248]], "c": false}], "t": 420, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [25.705, -37.992]], "c": false}], "t": 421, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [22.232, -37.73]], "c": false}], "t": 422, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [18.728, -37.465]], "c": false}], "t": 423, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [15.275, -37.205]], "c": false}], "t": 424, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [12.014, -36.959]], "c": false}], "t": 425, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -36.75]], "c": false}], "t": 426, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -36.245]], "c": false}], "t": 427, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -35.487]], "c": false}], "t": 428, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -34.479]], "c": false}], "t": 429, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -33.307]], "c": false}], "t": 430, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -32.102]], "c": false}], "t": 431, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -30.966]], "c": false}], "t": 432, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -29.94]], "c": false}], "t": 433, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -29.029]], "c": false}], "t": 434, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -28.223]], "c": false}], "t": 435, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -27.508]], "c": false}], "t": 436, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -26.872]], "c": false}], "t": 437, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -26.304]], "c": false}], "t": 438, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -25.794]], "c": false}], "t": 439, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -25.335]], "c": false}], "t": 440, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -24.92]], "c": false}], "t": 441, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -24.545]], "c": false}], "t": 442, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -24.205]], "c": false}], "t": 443, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -23.896]], "c": false}], "t": 444, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -23.615]], "c": false}], "t": 445, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -23.36]], "c": false}], "t": 446, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -23.129]], "c": false}], "t": 447, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.918]], "c": false}], "t": 448, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.728]], "c": false}], "t": 449, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.555]], "c": false}], "t": 450, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.4]], "c": false}], "t": 451, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.26]], "c": false}], "t": 452, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.135]], "c": false}], "t": 453, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -22.023]], "c": false}], "t": 454, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.924]], "c": false}], "t": 455, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.837]], "c": false}], "t": 456, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.762]], "c": false}], "t": 457, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.697]], "c": false}], "t": 458, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.642]], "c": false}], "t": 459, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.597]], "c": false}], "t": 460, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.561]], "c": false}], "t": 461, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.534]], "c": false}], "t": 462, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[9.25, -21.5], [9.25, -21.515]], "c": false}], "t": 463, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"k": [{"s": [54.014], "t": 407, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.056], "t": 408, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.123], "t": 409, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.216], "t": 410, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.333], "t": 411, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.474], "t": 412, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.636], "t": 413, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.819], "t": 414, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [55.022], "t": 415, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [55.244], "t": 416, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [55.483], "t": 417, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [55.738], "t": 418, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [56.008], "t": 419, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [56.291], "t": 420, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [56.584], "t": 421, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [56.883], "t": 422, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [57.185], "t": 423, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [57.482], "t": 424, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [57.763], "t": 425, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [58], "t": 426, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, -100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 1, "k": [{"t": -145, "s": [0], "h": 1}, {"t": 18, "s": [100], "h": 1}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Left", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.44, "y": 0}, "t": 406, "s": [{"i": [[6.167, -3.179], [-15.352, 0], [5.008, 2.581]], "o": [[-5.59, 2.881], [13.869, 0], [-6.167, -3.179]], "v": [[-9.526, -79.571], [0.604, -100.669], [9.867, -79.571]], "c": true}]}, {"t": 426, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.895, -79.571], [0.709, -100.669], [11.321, -79.571]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.765, "y": 0.675}, "o": {"x": 0.399, "y": 0}, "t": 406, "s": [0, 139], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.24, "y": 1}, "o": {"x": 0.4, "y": 0.441}, "t": 424, "s": [0, 101], "to": [0, 0], "ti": [0, 0]}, {"t": 427, "s": [0, 93]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 406, "op": 466, "st": -145, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -6, "s": [100]}, {"t": 5, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -21.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -40.125]], "c": false}], "t": 195, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.033, -21.75], [-55.984, -40.016]], "c": false}], "t": 196, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-7.198, -21.75], [-50.169, -39.599]], "c": false}], "t": 197, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-5.356, -21.75], [-37.325, -38.678]], "c": false}], "t": 198, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-2.425, -21.75], [-16.892, -37.213]], "c": false}], "t": 199, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.467, -21.75], [3.272, -35.766]], "c": false}], "t": 200, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.584, -21.75], [18.031, -34.708]], "c": false}], "t": 201, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[4.048, -21.75], [28.237, -33.976]], "c": false}], "t": 202, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.064, -21.75], [35.32, -33.468]], "c": false}], "t": 203, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[5.767, -21.75], [40.22, -33.116]], "c": false}], "t": 204, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.241, -21.75], [43.523, -32.88]], "c": false}], "t": 205, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.54, -21.75], [45.607, -32.73]], "c": false}], "t": 206, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.701, -21.75], [46.73, -32.65]], "c": false}], "t": 207, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [47.072, -32.625]], "c": false}], "t": 208, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [49.587, -33.707]], "c": false}], "t": 209, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [54.144, -35.668]], "c": false}], "t": 210, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [57.197, -36.982]], "c": false}], "t": 211, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [59.162, -37.828]], "c": false}], "t": 212, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [60.549, -38.425]], "c": false}], "t": 213, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [61.581, -38.869]], "c": false}], "t": 214, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [62.369, -39.208]], "c": false}], "t": 215, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [62.978, -39.47]], "c": false}], "t": 216, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [63.45, -39.673]], "c": false}], "t": 217, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [63.811, -39.829]], "c": false}], "t": 218, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [64.082, -39.945]], "c": false}], "t": 219, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [64.276, -40.028]], "c": false}], "t": 220, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [64.405, -40.084]], "c": false}], "t": 221, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [64.477, -40.115]], "c": false}], "t": 222, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 54, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, -100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Left", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 195, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -40.125]], "c": false}]}, {"i": {"x": 0.01, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [47.072, -32.625]], "c": false}]}, {"t": 223, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.75, -21.75], [64.5, -40.125]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 54, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Right", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 200, "op": 221, "st": -6, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -145, "s": [100]}, {"t": -134, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -21.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-55.625, -45.75]], "c": false}], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-55.725, -44.786]], "c": false}], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-55.906, -43.051]], "c": false}], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.119, -41.012]], "c": false}], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.349, -38.801]], "c": false}], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.591, -36.476]], "c": false}], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.841, -34.073]], "c": false}], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.097, -31.615]], "c": false}], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.357, -29.121]], "c": false}], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.618, -26.612]], "c": false}], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.878, -24.121]], "c": false}], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-58.125, -21.75]], "c": false}], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-58.021, -24.795]], "c": false}], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.846, -29.943]], "c": false}], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.74, -33.077]], "c": false}], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.672, -35.065]], "c": false}], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.625, -36.457]], "c": false}], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.59, -37.483]], "c": false}], "t": 76, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.564, -38.258]], "c": false}], "t": 77, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.543, -38.847]], "c": false}], "t": 78, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.528, -39.292]], "c": false}], "t": 79, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.517, -39.621]], "c": false}], "t": 80, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.509, -39.856]], "c": false}], "t": 81, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.504, -40.011]], "c": false}], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.501, -40.098]], "c": false}], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -40.125]], "c": false}], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -40.048]], "c": false}], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -39.825]], "c": false}], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -39.469]], "c": false}], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -38.991]], "c": false}], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -38.401]], "c": false}], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -37.708]], "c": false}], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -36.919]], "c": false}], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -36.042]], "c": false}], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -35.084]], "c": false}], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -34.051]], "c": false}], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -32.951]], "c": false}], "t": 95, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -31.79]], "c": false}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -30.576]], "c": false}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -29.316]], "c": false}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -28.021]], "c": false}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -26.701]], "c": false}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -25.375]], "c": false}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -24.067]], "c": false}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -22.825]], "c": false}], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -21.75]], "c": false}], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.38, -24.323]], "c": false}], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.199, -28.188]], "c": false}], "t": 106, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.031, -31.777]], "c": false}], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.898, -34.618]], "c": false}], "t": 108, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.795, -36.811]], "c": false}], "t": 109, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.716, -38.509]], "c": false}], "t": 110, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.654, -39.829]], "c": false}], "t": 111, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.606, -40.851]], "c": false}], "t": 112, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.57, -41.634]], "c": false}], "t": 113, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.542, -42.22]], "c": false}], "t": 114, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.523, -42.64]], "c": false}], "t": 115, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.51, -42.919]], "c": false}], "t": 116, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.502, -43.076]], "c": false}], "t": 117, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.5, -43.125]], "c": false}], "t": 118, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 54, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, -100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Left", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.17, "y": 0}, "t": 59, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-55.625, -45.75]], "c": false}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-58.125, -21.75]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.4, "y": 0}, "t": 84, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -40.125]], "c": false}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-57.5, -21.75]], "c": false}]}, {"t": 118, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-8.25, -21.75], [-56.5, -43.125]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 54, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Right", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.17, "y": 0}, "t": 59, "s": [{"i": [[7.064, -3.179], [-17.586, 0], [5.736, 2.581]], "o": [[-6.403, 2.881], [15.887, 0], [-7.064, -3.179]], "v": [[-10.895, 10.429], [0.709, -10.669], [11.321, 10.429]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[4.884, -3.179], [-12.16, 0], [3.966, 2.581]], "o": [[-4.427, 2.881], [10.985, 0], [-4.884, -3.179]], "v": [[-7.577, 14.554], [0.447, -6.544], [7.784, 14.554]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.4, "y": 0}, "t": 84, "s": [{"i": [[5.367, -3.179], [-13.36, 0], [4.358, 2.581]], "o": [[-4.864, 2.881], [12.069, 0], [-5.367, -3.179]], "v": [[-8.36, 13.429], [0.455, -7.669], [8.517, 13.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [{"i": [[6.067, -3.179], [-15.103, 0], [4.926, 2.581]], "o": [[-5.499, 2.881], [13.644, 0], [-6.067, -3.179]], "v": [[-9.38, 13.554], [0.585, -7.544], [9.699, 13.554]], "c": true}]}, {"t": 112, "s": [{"i": [[6.067, -3.179], [-15.103, 0], [4.926, 2.581]], "o": [[-5.499, 2.881], [13.644, 0], [-6.067, -3.179]], "v": [[-9.38, 13.554], [0.585, -7.544], [9.699, 13.554]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -90], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, -100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Round Bottom", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 59, "op": 112, "st": -145, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 549, "s": [100]}, {"t": 555, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -21.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -21.75], [0, -21.75]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 12, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -21.75], [0, -21.75]], "c": false}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -21.75], [0, -21.75]], "c": false}]}, {"i": {"x": 0.22, "y": 1}, "o": {"x": 0.18, "y": 1}, "t": 23, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-11.643, -21.893], [-35.357, -21.893]], "c": false}]}, {"t": 38, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-12.75, -21.75], [-47.75, -37]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.52], "y": [0.96]}, "o": {"x": [0.48], "y": [0.04]}, "t": 0, "s": [48]}, {"t": 18, "s": [64]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Right", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -21.75], [0, -21.75]], "c": false}]}, {"i": {"x": 0.22, "y": 1}, "o": {"x": 0.18, "y": 1}, "t": 23, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-11.643, -21.893], [-35.357, -21.893]], "c": false}]}, {"t": 38, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-12.75, -21.75], [-47.75, -7.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"k": [{"s": [48], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [48.143], "t": 1, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [48.435], "t": 2, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [48.9], "t": 3, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [49.559], "t": 4, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [50.434], "t": 5, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [51.537], "t": 6, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52.865], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [54.378], "t": 8, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [57.622], "t": 10, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [59.135], "t": 11, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [60.463], "t": 12, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [61.566], "t": 13, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [62.441], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.1], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.565], "t": 16, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.857], "t": 17, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [64], "t": 18, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -21.75], "ix": 2}, "a": {"a": 0, "k": [0, -21.75], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 90, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Left", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": -145, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": ".secondaryHome", "cl": "secondaryHome", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 52, "s": [100], "h": 1}, {"t": 62, "s": [0], "h": 1}, {"t": 112, "s": [100], "h": 1}, {"t": 200, "s": [0], "h": 1}, {"t": 221, "s": [100], "h": 1}, {"t": 408, "s": [0], "h": 1}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -259.147, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.001, "y": 0}, "t": 27, "s": [{"i": [[4.22, -5.198], [6.889, -2.445], [6.141, 2.195], [4.414, 5.745], [2.09, 6.003], [1.842, 5.673], [0, 0], [-4.629, 7.032], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-2.537, 1.423], [0, 0], [-5.764, -2.348], [-3.35, -5.761], [3.559, -9.527], [0, 0], [2.211, -5.388]], "o": [[-4.607, 5.675], [-6.146, 2.181], [-6.823, -2.438], [-3.873, -5.041], [-1.961, -5.633], [-0.673, -2.074], [-3.418, -8.054], [2.391, -3.632], [7.091, -4.519], [0, 0], [2.053, 0.611], [0, 0], [6.766, -3.045], [6.042, 2.461], [4.226, 7.269], [0, 0], [0, 0], [-2.542, 6.194]], "v": [[26.625, 263.759], [9.088, 276.927], [-10.477, 276.927], [-27.766, 263.755], [-34.35, 245.857], [-39.92, 228.853], [-41.895, 222.617], [-38.621, 196.593], [-29.625, 187.329], [-2.764, 182.988], [-0.678, 184.077], [3.537, 183.327], [5.123, 182.613], [23.746, 183.97], [38.891, 196.367], [42.816, 222.402], [40.88, 229.024], [34.705, 245.369]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 40, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 49, "s": [{"i": [[0, 0], [5.924, -2.615], [6.267, 2.766], [3.263, 7.561], [2.981, 6.909], [0, 0], [0, 0], [-4.825, 8.784], [-3.668, 2.709], [-9.176, -4.785], [0, 0], [-7.064, 3.683], [0, 0], [-5.942, -2.295], [-3.35, -6.676], [4.5, -10.429], [0, 0], [2.725, -6.315]], "o": [[-3.262, 7.56], [-6.266, 2.766], [-5.925, -2.615], [0, 0], [-2.981, -6.909], [0, 0], [-3.988, -9.246], [2.335, -4.251], [7.091, -5.237], [0, 0], [7.064, 3.683], [0, 0], [6.766, -3.528], [6.387, 2.466], [4.226, 8.423], [0, 0], [0, 0], [-3.063, 7.1]], "v": [[23.515, 261.285], [9.088, 276.548], [-10.477, 276.549], [-24.906, 261.285], [-33.85, 240.557], [-42.795, 219.829], [-48.707, 206.127], [-47.871, 176.22], [-38.875, 165.486], [-13.389, 163.498], [-11.803, 164.325], [10.412, 164.325], [11.998, 163.498], [31.496, 162.173], [46.641, 176.538], [47.316, 206.126], [40.88, 221.041], [32.705, 239.986]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.61, "y": 0}, "t": 57, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0, 0], [5.829, -2.275], [6.166, 2.405], [3.21, 6.575], [2.933, 6.009], [0, 0], [0, 0], [-4.747, 7.639], [-3.609, 2.356], [-9.028, -5.782], [0, 0], [-6.95, 4.451], [0, 0], [-5.847, -1.996], [-3.296, -5.806], [4.428, -9.07], [0, 0], [2.681, -5.492]], "o": [[-3.21, 6.575], [-6.165, 2.406], [-5.83, -2.274], [0, 0], [-2.933, -6.009], [0, 0], [-3.924, -8.041], [2.297, -3.697], [6.977, -4.554], [0, 0], [6.95, 4.451], [0, 0], [6.657, -4.263], [6.284, 2.145], [4.158, 7.326], [0, 0], [0, 0], [-3.014, 6.174]], "v": [[23.147, 263.139], [8.953, 276.413], [-10.297, 276.414], [-24.493, 263.14], [-31.981, 245.113], [-39.468, 227.086], [-43.871, 215.169], [-44.463, 189.161], [-35.612, 179.825], [-10.537, 178.375], [-8.977, 179.374], [7.631, 179.374], [9.191, 178.375], [28.374, 176.944], [43.275, 189.437], [42.525, 215.169], [37.607, 228.14], [30.877, 244.617]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [{"i": [[0, 0], [5.782, -2.283], [6.116, 2.414], [3.184, 6.6], [2.91, 6.031], [0, 0], [0, 0], [-4.709, 7.668], [-3.58, 2.365], [-8.956, -2.881], [0, 0], [-6.894, 2.218], [0, 0], [-5.8, -2.003], [-3.269, -5.828], [4.392, -9.104], [0, 0], [2.66, -5.513]], "o": [[-3.184, 6.599], [-6.116, 2.415], [-5.783, -2.283], [0, 0], [-2.91, -6.031], [0, 0], [-3.893, -8.071], [2.279, -3.711], [6.921, -4.571], [0, 0], [6.894, 2.218], [0, 0], [6.604, -2.124], [6.234, 2.153], [4.125, 7.353], [0, 0], [0, 0], [-2.99, 6.198]], "v": [[22.966, 262.838], [8.886, 276.162], [-10.209, 276.162], [-24.29, 262.838], [-31.064, 244.743], [-37.837, 226.649], [-41.5, 214.688], [-42.792, 188.581], [-34.012, 179.21], [-9.139, 174.179], [-7.591, 174.677], [6.267, 174.677], [7.814, 174.179], [26.844, 176.318], [41.625, 188.859], [40.176, 214.688], [36.003, 227.707], [29.98, 244.245]], "c": true}]}, {"i": {"x": 0.48, "y": 1}, "o": {"x": 0.26, "y": 1}, "t": 111, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 1.445], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 1.445], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[22.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-30.85, 245.866], [-37.795, 227.978], [-42.707, 216.152], [-41.871, 190.343], [-32.875, 181.079], [-7.389, 179.363], [-5.803, 180.077], [5.412, 180.077], [6.998, 179.363], [26.496, 178.22], [41.641, 190.617], [42.316, 216.152], [38.88, 229.024], [31.705, 245.374]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"i": {"x": 0.902, "y": 0}, "o": {"x": 0.3, "y": 0}, "t": 119, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.2, "y": 0}, "t": 129, "s": [{"i": [[0, 0], [6.002, -2.085], [6.349, 2.205], [3.305, 6.027], [3.02, 5.508], [0, 0], [0, 0], [-4.888, 7.002], [-3.716, 2.159], [-9.296, -3.814], [0, 0], [-7.157, 2.936], [0, 0], [-6.02, -1.829], [-3.393, -5.322], [4.559, -8.314], [0, 0], [2.761, -5.034]], "o": [[-3.305, 6.027], [-6.349, 2.205], [-6.003, -2.085], [0, 0], [-3.02, -5.508], [0, 0], [-4.041, -7.37], [2.366, -3.389], [7.184, -4.175], [0, 0], [7.157, 2.936], [0, 0], [6.855, -2.813], [6.471, 1.966], [4.282, 6.715], [0, 0], [0, 0], [-3.104, 5.66]], "v": [[23.828, 264.941], [9.213, 277.109], [-10.609, 277.11], [-25.226, 264.942], [-34.288, 248.418], [-43.349, 231.894], [-49.339, 220.97], [-48.492, 197.13], [-39.379, 188.572], [-13.559, 186.988], [-11.952, 187.647], [10.554, 187.647], [12.161, 186.988], [31.914, 185.931], [47.257, 197.383], [47.941, 220.97], [41.421, 232.86], [33.139, 247.963]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 144, "s": [{"i": [[0, 0], [5.924, -2.439], [6.267, 2.579], [3.263, 7.051], [2.981, 6.443], [0, 0], [0, 0], [-4.825, 8.191], [-3.668, 2.526], [-9.176, -4.462], [0, 0], [-7.064, 3.435], [0, 0], [-5.942, -2.14], [-3.35, -6.225], [4.5, -9.725], [0, 0], [2.725, -5.889]], "o": [[-3.262, 7.05], [-6.266, 2.58], [-5.925, -2.439], [0, 0], [-2.981, -6.443], [0, 0], [-3.988, -8.622], [2.335, -3.964], [7.091, -4.884], [0, 0], [7.064, 3.435], [0, 0], [6.766, -3.29], [6.387, 2.3], [4.226, 7.855], [0, 0], [0, 0], [-3.063, 6.621]], "v": [[23.515, 262.501], [9.088, 276.734], [-10.477, 276.735], [-24.906, 262.501], [-33.85, 243.171], [-42.795, 223.841], [-48.707, 211.063], [-47.871, 183.174], [-38.875, 173.164], [-13.389, 171.31], [-11.803, 172.081], [10.412, 172.081], [11.998, 171.31], [31.496, 170.074], [46.641, 183.471], [47.316, 211.063], [40.88, 224.972], [32.705, 242.639]], "c": true}]}, {"i": {"x": 0.999, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 146, "s": [{"i": [[0, 0], [5.924, -2.439], [6.267, 2.579], [3.263, 7.051], [2.981, 6.443], [0, 0], [0, 0], [-4.825, 8.191], [-3.668, 2.526], [-9.176, -4.462], [0, 0], [-7.064, 3.435], [0, 0], [-5.942, -2.14], [-3.35, -6.225], [4.5, -9.725], [0, 0], [2.725, -5.889]], "o": [[-3.262, 7.05], [-6.266, 2.58], [-5.925, -2.439], [0, 0], [-2.981, -6.443], [0, 0], [-3.988, -8.622], [2.335, -3.964], [7.091, -4.884], [0, 0], [7.064, 3.435], [0, 0], [6.766, -3.29], [6.387, 2.3], [4.226, 7.855], [0, 0], [0, 0], [-3.063, 6.621]], "v": [[23.515, 262.501], [9.088, 276.734], [-10.477, 276.735], [-24.906, 262.501], [-33.85, 243.171], [-42.795, 223.841], [-48.707, 211.063], [-47.871, 183.174], [-38.875, 173.164], [-13.389, 171.31], [-11.803, 172.081], [10.412, 172.081], [11.998, 171.31], [31.496, 170.074], [46.641, 183.471], [47.316, 211.063], [40.88, 224.972], [32.705, 242.639]], "c": true}]}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.05, "y": 0.884}, "t": 156, "s": [{"i": [[0, 0], [6.002, -2.085], [6.349, 2.205], [3.305, 6.027], [3.02, 5.508], [0, 0], [0, 0], [-4.888, 7.002], [-3.716, 2.159], [-9.296, -3.814], [0, 0], [-7.157, 2.936], [0, 0], [-6.02, -1.829], [-3.393, -5.322], [4.559, -8.314], [0, 0], [2.761, -5.034]], "o": [[-3.305, 6.027], [-6.349, 2.205], [-6.003, -2.085], [0, 0], [-3.02, -5.508], [0, 0], [-4.041, -7.37], [2.366, -3.389], [7.184, -4.175], [0, 0], [7.157, 2.936], [0, 0], [6.855, -2.813], [6.471, 1.966], [4.282, 6.715], [0, 0], [0, 0], [-3.104, 5.66]], "v": [[23.828, 264.941], [9.213, 277.109], [-10.609, 277.11], [-25.226, 264.942], [-34.288, 248.418], [-43.349, 231.894], [-49.339, 220.97], [-48.492, 197.13], [-39.379, 188.572], [-13.559, 186.988], [-11.952, 187.647], [10.554, 187.647], [12.161, 186.988], [31.914, 185.931], [47.257, 197.383], [47.941, 220.97], [41.421, 232.86], [33.139, 247.963]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 175, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 195, "s": [{"i": [[0, 0], [5.924, -2.257], [6.267, 2.387], [3.263, 6.525], [2.981, 5.963], [0, 0], [0, 0], [-4.825, 7.58], [-3.668, 2.338], [-9.176, -4.129], [0, 0], [-7.064, 3.179], [0, 0], [-5.942, -1.98], [-3.35, -5.761], [4.5, -9], [0, 0], [2.725, -5.45]], "o": [[-3.262, 6.524], [-6.266, 2.387], [-5.925, -2.257], [0, 0], [-2.981, -5.963], [0, 0], [-3.988, -7.979], [2.335, -3.669], [7.091, -4.519], [0, 0], [7.064, 3.179], [0, 0], [6.766, -3.045], [6.387, 2.129], [4.226, 7.269], [0, 0], [0, 0], [-3.063, 6.127]], "v": [[23.515, 263.755], [9.088, 276.927], [-10.477, 276.927], [-24.906, 263.755], [-33.85, 245.866], [-42.795, 227.978], [-48.707, 216.152], [-47.871, 190.343], [-38.875, 181.079], [-13.389, 179.363], [-11.803, 180.077], [10.412, 180.077], [11.998, 179.363], [31.496, 178.22], [46.641, 190.617], [47.316, 216.152], [40.88, 229.024], [32.705, 245.374]], "c": true}]}, {"t": 200, "s": [{"i": [[0, 0], [4.982, -1.843], [5.269, 1.949], [2.743, 5.327], [2.507, 4.868], [0, 0], [0, 0], [-4.057, 6.188], [-3.084, 1.909], [-7.716, -3.371], [0, 0], [-5.94, 2.595], [0, 0], [-4.997, -1.617], [-2.817, -4.703], [3.784, -7.348], [0, 0], [2.291, -4.449]], "o": [[-2.743, 5.326], [-5.269, 1.949], [-4.982, -1.842], [0, 0], [-2.507, -4.868], [0, 0], [-3.354, -6.514], [1.963, -2.995], [5.963, -3.69], [0, 0], [5.94, 2.595], [0, 0], [5.69, -2.486], [5.371, 1.738], [3.554, 5.935], [0, 0], [0, 0], [-2.576, 5.002]], "v": [[21.328, 266.623], [7.572, 277.365], [-8.88, 277.366], [-21.013, 266.612], [-30.158, 252.046], [-38.929, 237.463], [-42.276, 232.155], [-38.901, 208.3], [-31.337, 200.737], [-7.657, 199.329], [-6.324, 199.912], [6.078, 199.877], [7.412, 199.294], [26.437, 198.157], [39.172, 208.278], [42.466, 230.78], [38.179, 240.068], [31.18, 251.666]], "c": true}], "h": 1}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [{"i": [[0, 0], [-6.469, 2.54], [-6.843, -2.686], [-3.562, -7.344], [-2.238, -6.985], [0, 0], [0, 0], [6.26, -8], [4.298, -2.248], [9.409, 5.544], [0, 0], [2.81, -1.732], [0, 0], [6.751, 1.517], [4.533, 6.044], [-3.466, 10.58], [0, 0], [-1.94, 6.445]], "o": [[3.562, -7.343], [6.843, -2.687], [6.47, 2.54], [0, 0], [2.25, 7.021], [0, 0], [3.257, 9.331], [-3.029, 3.872], [-8.309, 4.346], [0, 0], [-2.38, -1.461], [0, 0], [-6.856, 4.193], [-7.256, -1.631], [-5.72, -7.626], [0, 0], [0, 0], [2.154, -7.155]], "v": [[-25.972, 255.773], [-10.844, 240.983], [10.52, 240.983], [26.276, 255.808], [31.363, 274.998], [37.793, 295.9], [41.622, 309.746], [42.615, 338.568], [31.6, 348.034], [3.818, 347.339], [2.192, 346.381], [-2.497, 346.652], [-4.104, 347.635], [-23.931, 350.481], [-42.288, 338.404], [-42.272, 310.024], [-38.575, 296.317], [-32.279, 277.471]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 225, "s": [{"i": [[0, 0], [-6.469, 2.54], [-6.843, -2.686], [-3.562, -7.344], [-2.238, -6.985], [0, 0], [0, 0], [6.26, -8], [4.298, -2.248], [9.409, 5.544], [0, 0], [2.81, -1.732], [0, 0], [6.751, 1.517], [4.533, 6.044], [-3.466, 10.58], [0, 0], [-1.94, 6.445]], "o": [[3.562, -7.343], [6.843, -2.687], [6.47, 2.54], [0, 0], [2.25, 7.021], [0, 0], [3.257, 9.331], [-3.029, 3.872], [-8.309, 4.346], [0, 0], [-2.38, -1.461], [0, 0], [-6.856, 4.193], [-7.256, -1.631], [-5.72, -7.626], [0, 0], [0, 0], [2.154, -7.155]], "v": [[-25.972, 255.773], [-10.844, 240.983], [10.52, 240.983], [26.276, 255.808], [31.363, 274.998], [37.793, 295.9], [41.622, 309.746], [42.615, 338.568], [31.6, 348.034], [3.818, 347.339], [2.192, 346.381], [-2.497, 346.652], [-4.104, 347.635], [-23.931, 350.481], [-42.288, 338.404], [-42.272, 310.024], [-38.575, 296.317], [-32.279, 277.471]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 230, "s": [{"i": [[0, 0], [-6.469, 2.537], [-6.843, -2.683], [-3.562, -7.334], [-3.256, -6.702], [0, 0], [0, 0], [5.268, -8.52], [4.005, -2.628], [10.02, 4.641], [0, 0], [7.713, -3.573], [0, 0], [6.489, 2.226], [3.658, 6.475], [-4.914, 10.116], [0, 0], [-2.976, 6.126]], "o": [[3.562, -7.333], [6.843, -2.683], [6.47, 2.537], [0, 0], [3.256, 6.702], [0, 0], [4.355, 8.968], [-2.55, 4.123], [-7.743, 5.08], [0, 0], [-7.714, -3.573], [0, 0], [-7.388, 3.422], [-6.974, -2.392], [-4.615, -8.17], [0, 0], [0, 0], [3.345, -6.886]], "v": [[-26.597, 256.303], [-10.844, 241.499], [10.52, 241.498], [26.276, 256.303], [36.042, 276.409], [45.809, 296.515], [52.265, 309.806], [51.352, 338.814], [41.529, 349.227], [13.7, 351.155], [11.968, 350.353], [-12.29, 350.353], [-14.021, 351.155], [-35.312, 352.44], [-51.849, 338.506], [-52.586, 309.806], [-45.559, 295.339], [-36.632, 276.963]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 235, "s": [{"i": [[0, 0], [-6.469, 2.527], [-6.843, -2.672], [-3.562, -7.305], [-2.238, -6.948], [0, 0], [0, 0], [6.26, -7.958], [4.298, -2.236], [9.409, 5.515], [0, 0], [2.81, -1.723], [0, 0], [6.751, 1.509], [4.533, 6.012], [-3.466, 10.525], [0, 0], [-1.94, 6.411]], "o": [[3.562, -7.305], [6.843, -2.673], [6.47, 2.527], [0, 0], [2.25, 6.984], [0, 0], [3.257, 9.283], [-3.029, 3.851], [-8.309, 4.323], [0, 0], [-2.38, -1.454], [0, 0], [-6.856, 4.171], [-7.256, -1.622], [-5.72, -7.586], [0, 0], [0, 0], [2.154, -7.118]], "v": [[-25.972, 256.282], [-10.844, 241.57], [10.52, 241.569], [26.276, 256.317], [31.363, 275.406], [37.793, 296.199], [41.622, 309.972], [42.615, 338.644], [31.6, 348.06], [3.818, 347.368], [2.192, 346.415], [-2.497, 346.685], [-4.104, 347.662], [-23.931, 350.493], [-42.288, 338.48], [-42.272, 310.249], [-38.575, 296.613], [-32.279, 277.866]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.4, "y": 0}, "t": 240, "s": [{"i": [[0, 0], [-6.469, 2.537], [-6.843, -2.683], [-3.562, -7.334], [-3.256, -6.702], [0, 0], [0, 0], [5.268, -8.52], [4.005, -2.628], [10.02, 4.641], [0, 0], [7.713, -3.573], [0, 0], [6.489, 2.226], [3.658, 6.475], [-4.914, 10.116], [0, 0], [-2.976, 6.126]], "o": [[3.562, -7.333], [6.843, -2.683], [6.47, 2.537], [0, 0], [3.256, 6.702], [0, 0], [4.355, 8.968], [-2.55, 4.123], [-7.743, 5.08], [0, 0], [-7.714, -3.573], [0, 0], [-7.388, 3.422], [-6.974, -2.392], [-4.615, -8.17], [0, 0], [0, 0], [3.345, -6.886]], "v": [[-26.597, 256.303], [-10.844, 241.499], [10.52, 241.498], [26.276, 256.303], [36.042, 276.409], [45.809, 296.515], [52.265, 309.806], [51.352, 338.814], [41.529, 349.227], [13.7, 351.155], [11.968, 350.353], [-12.29, 350.353], [-14.021, 351.155], [-35.312, 352.44], [-51.849, 338.506], [-52.586, 309.806], [-45.559, 295.339], [-36.632, 276.963]], "c": true}]}, {"i": {"x": 0.83, "y": 1}, "o": {"x": 0.17, "y": 0}, "t": 250, "s": [{"i": [[0, 0], [-6.469, 2.528], [-6.843, -2.674], [-3.562, -7.309], [-2.238, -6.951], [0, 0], [0, 0], [6.26, -7.962], [4.298, -2.237], [9.409, 5.517], [0, 0], [2.81, -1.724], [0, 0], [6.751, 1.51], [4.533, 6.015], [-3.466, 10.53], [0, 0], [-1.94, 6.414]], "o": [[3.562, -7.308], [6.843, -2.674], [6.47, 2.528], [0, 0], [2.25, 6.988], [0, 0], [3.257, 9.287], [-3.029, 3.853], [-8.309, 4.325], [0, 0], [-2.38, -1.454], [0, 0], [-6.856, 4.173], [-7.256, -1.623], [-5.72, -7.59], [0, 0], [0, 0], [2.154, -7.121]], "v": [[-25.972, 256.238], [-10.844, 241.519], [10.52, 241.518], [26.276, 256.273], [31.363, 275.37], [37.793, 296.172], [41.622, 309.952], [42.615, 338.637], [31.6, 348.057], [3.818, 347.366], [2.192, 346.412], [-2.497, 346.682], [-4.104, 347.66], [-23.931, 350.492], [-42.288, 338.473], [-42.272, 310.229], [-38.575, 296.587], [-32.279, 277.831]], "c": true}]}, {"i": {"x": 0.31, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 296, "s": [{"i": [[0, 0], [-5.924, 2.257], [-6.267, -2.387], [-3.263, -6.525], [-2.981, -5.963], [0, 0], [0, 0], [4.825, -7.58], [3.668, -2.338], [9.176, 4.129], [0, 0], [7.064, -3.179], [0, 0], [5.942, 1.98], [3.35, 5.761], [-4.5, 9], [0, 0], [-2.725, 5.45]], "o": [[3.262, -6.524], [6.266, -2.387], [5.925, 2.257], [0, 0], [2.981, 5.963], [0, 0], [3.988, 7.979], [-2.335, 3.669], [-7.091, 4.519], [0, 0], [-7.064, -3.179], [0, 0], [-6.766, 3.045], [-6.387, -2.129], [-4.226, -7.269], [0, 0], [0, 0], [3.063, -6.127]], "v": [[-24.394, 255.294], [-9.968, 242.121], [9.597, 242.121], [24.026, 255.294], [32.971, 273.182], [41.915, 291.071], [47.827, 302.896], [46.991, 328.705], [37.996, 337.969], [12.509, 339.685], [10.924, 338.971], [-11.292, 338.971], [-12.878, 339.685], [-32.376, 340.828], [-47.521, 328.431], [-48.196, 302.896], [-41.76, 290.025], [-33.585, 273.675]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 313, "s": [{"i": [[0, 0], [-5.924, 2.568], [-6.267, -2.716], [-3.263, -7.424], [-2.981, -6.785], [0, 0], [0, 0], [4.825, -8.625], [3.668, -2.66], [9.176, 4.699], [0, 0], [7.064, -3.617], [0, 0], [5.942, 2.253], [3.35, 6.555], [-4.5, 10.241], [0, 0], [-2.725, 6.201]], "o": [[3.262, -7.424], [6.266, -2.716], [5.925, 2.568], [0, 0], [2.981, 6.785], [0, 0], [3.988, 9.079], [-2.335, 4.174], [-7.091, 5.142], [0, 0], [-7.064, -3.617], [0, 0], [-6.766, 3.465], [-6.387, -2.422], [-4.226, -8.271], [0, 0], [0, 0], [3.063, -6.972]], "v": [[-24.394, 257.438], [-9.968, 242.451], [9.597, 242.45], [24.026, 257.438], [32.971, 277.793], [41.915, 298.147], [47.827, 311.602], [46.991, 340.969], [37.996, 351.51], [12.509, 353.462], [10.924, 352.65], [-11.292, 352.65], [-12.878, 353.462], [-32.376, 354.763], [-47.521, 340.657], [-48.196, 311.602], [-41.76, 296.957], [-33.585, 278.353]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 318, "s": [{"i": [[0, 0], [-6.469, 2.54], [-6.843, -2.686], [-3.562, -7.344], [-2.238, -6.985], [0, 0], [0, 0], [6.26, -8], [4.298, -2.248], [9.409, 5.544], [0, 0], [2.81, -1.732], [0, 0], [6.751, 1.517], [4.533, 6.044], [-3.466, 10.58], [0, 0], [-1.94, 6.445]], "o": [[3.562, -7.343], [6.843, -2.687], [6.47, 2.54], [0, 0], [2.25, 7.021], [0, 0], [3.257, 9.331], [-3.029, 3.872], [-8.309, 4.346], [0, 0], [-2.38, -1.461], [0, 0], [-6.856, 4.193], [-7.256, -1.631], [-5.72, -7.626], [0, 0], [0, 0], [2.154, -7.155]], "v": [[-25.972, 255.773], [-10.844, 240.983], [10.52, 240.983], [26.276, 255.808], [31.363, 274.998], [37.793, 295.9], [41.622, 309.746], [42.615, 338.568], [31.6, 348.034], [3.818, 347.339], [2.192, 346.381], [-2.497, 346.652], [-4.104, 347.635], [-23.931, 350.481], [-42.288, 338.404], [-42.272, 310.024], [-38.575, 296.317], [-32.279, 277.471]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 323, "s": [{"i": [[0, 0], [-6.469, 2.434], [-6.843, -2.574], [-3.562, -7.037], [-3.256, -6.431], [0, 0], [0, 0], [5.268, -8.175], [4.005, -2.521], [10.02, 4.453], [0, 0], [7.713, -3.428], [0, 0], [6.489, 2.136], [3.658, 6.213], [-4.914, 9.707], [0, 0], [-2.976, 5.878]], "o": [[3.562, -7.036], [6.843, -2.575], [6.47, 2.434], [0, 0], [3.256, 6.431], [0, 0], [4.355, 8.605], [-2.55, 3.956], [-7.743, 4.874], [0, 0], [-7.714, -3.428], [0, 0], [-7.388, 3.284], [-6.974, -2.296], [-4.615, -7.84], [0, 0], [0, 0], [3.345, -6.608]], "v": [[-26.597, 255.596], [-10.844, 241.39], [10.52, 241.389], [26.276, 255.596], [36.042, 274.888], [45.809, 294.181], [52.265, 306.934], [51.352, 334.769], [41.529, 344.76], [13.7, 346.61], [11.968, 345.84], [-12.29, 345.84], [-14.021, 346.61], [-35.312, 347.843], [-51.849, 334.473], [-52.586, 306.934], [-45.559, 293.052], [-36.632, 275.419]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 328, "s": [{"i": [[0, 0], [-6.469, 2.527], [-6.843, -2.672], [-3.562, -7.305], [-2.238, -6.948], [0, 0], [0, 0], [6.26, -7.958], [4.298, -2.236], [9.409, 5.515], [0, 0], [2.81, -1.723], [0, 0], [6.751, 1.509], [4.533, 6.012], [-3.466, 10.525], [0, 0], [-1.94, 6.411]], "o": [[3.562, -7.305], [6.843, -2.673], [6.47, 2.527], [0, 0], [2.25, 6.984], [0, 0], [3.257, 9.283], [-3.029, 3.851], [-8.309, 4.323], [0, 0], [-2.38, -1.454], [0, 0], [-6.856, 4.171], [-7.256, -1.622], [-5.72, -7.586], [0, 0], [0, 0], [2.154, -7.118]], "v": [[-25.972, 256.282], [-10.844, 241.57], [10.52, 241.569], [26.276, 256.317], [31.363, 275.406], [37.793, 296.199], [41.622, 309.972], [42.615, 338.644], [31.6, 348.06], [3.818, 347.368], [2.192, 346.415], [-2.497, 346.685], [-4.104, 347.662], [-23.931, 350.493], [-42.288, 338.48], [-42.272, 310.249], [-38.575, 296.613], [-32.279, 277.866]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.4, "y": 0}, "t": 333, "s": [{"i": [[0, 0], [-6.469, 2.537], [-6.843, -2.683], [-3.562, -7.334], [-3.256, -6.702], [0, 0], [0, 0], [5.268, -8.52], [4.005, -2.628], [10.02, 4.641], [0, 0], [7.713, -3.573], [0, 0], [6.489, 2.226], [3.658, 6.475], [-4.914, 10.116], [0, 0], [-2.976, 6.126]], "o": [[3.562, -7.333], [6.843, -2.683], [6.47, 2.537], [0, 0], [3.256, 6.702], [0, 0], [4.355, 8.968], [-2.55, 4.123], [-7.743, 5.08], [0, 0], [-7.714, -3.573], [0, 0], [-7.388, 3.422], [-6.974, -2.392], [-4.615, -8.17], [0, 0], [0, 0], [3.345, -6.886]], "v": [[-26.597, 256.303], [-10.844, 241.499], [10.52, 241.498], [26.276, 256.303], [36.042, 276.409], [45.809, 296.515], [52.265, 309.806], [51.352, 338.814], [41.529, 349.227], [13.7, 351.155], [11.968, 350.353], [-12.29, 350.353], [-14.021, 351.155], [-35.312, 352.44], [-51.849, 338.506], [-52.586, 309.806], [-45.559, 295.339], [-36.632, 276.963]], "c": true}]}, {"i": {"x": 0.83, "y": 1}, "o": {"x": 0.17, "y": 0}, "t": 343, "s": [{"i": [[0, 0], [-6.469, 2.528], [-6.843, -2.674], [-3.562, -7.309], [-2.238, -6.951], [0, 0], [0, 0], [6.26, -7.962], [4.298, -2.237], [9.409, 5.517], [0, 0], [2.81, -1.724], [0, 0], [6.751, 1.51], [4.533, 6.015], [-3.466, 10.53], [0, 0], [-1.94, 6.414]], "o": [[3.562, -7.308], [6.843, -2.674], [6.47, 2.528], [0, 0], [2.25, 6.988], [0, 0], [3.257, 9.287], [-3.029, 3.853], [-8.309, 4.325], [0, 0], [-2.38, -1.454], [0, 0], [-6.856, 4.173], [-7.256, -1.623], [-5.72, -7.59], [0, 0], [0, 0], [2.154, -7.121]], "v": [[-25.972, 256.238], [-10.844, 241.519], [10.52, 241.518], [26.276, 256.273], [31.363, 275.37], [37.793, 296.172], [41.622, 309.952], [42.615, 338.637], [31.6, 348.057], [3.818, 347.366], [2.192, 346.412], [-2.497, 346.682], [-4.104, 347.66], [-23.931, 350.492], [-42.288, 338.473], [-42.272, 310.229], [-38.575, 296.587], [-32.279, 277.831]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 389, "s": [{"i": [[0, 0], [-5.924, 2.257], [-6.267, -2.387], [-3.263, -6.525], [-2.981, -5.963], [0, 0], [0, 0], [4.825, -7.58], [3.668, -2.338], [9.176, 4.129], [0, 0], [7.064, -3.179], [0, 0], [5.942, 1.98], [3.35, 5.761], [-4.5, 9], [0, 0], [-2.725, 5.45]], "o": [[3.262, -6.524], [6.266, -2.387], [5.925, 2.257], [0, 0], [2.981, 5.963], [0, 0], [3.988, 7.979], [-2.335, 3.669], [-7.091, 4.519], [0, 0], [-7.064, -3.179], [0, 0], [-6.766, 3.045], [-6.387, -2.129], [-4.226, -7.269], [0, 0], [0, 0], [3.063, -6.127]], "v": [[-24.394, 255.294], [-9.968, 242.121], [9.597, 242.121], [24.026, 255.294], [32.971, 273.182], [41.915, 291.071], [47.827, 302.896], [46.991, 328.705], [37.996, 337.969], [12.509, 339.685], [10.924, 338.971], [-11.292, 338.971], [-12.878, 339.685], [-32.376, 340.828], [-47.521, 328.431], [-48.196, 302.896], [-41.76, 290.025], [-33.585, 273.675]], "c": true}]}, {"t": 406, "s": [{"i": [[0, 0], [-5.924, 2.257], [-6.267, -2.387], [-3.263, -6.525], [-2.981, -5.963], [0, 0], [0, 0], [4.825, -7.58], [3.668, -2.338], [9.176, 4.129], [0, 0], [7.064, -3.179], [0, 0], [5.942, 1.98], [3.35, 5.761], [-4.5, 9], [0, 0], [-2.725, 5.45]], "o": [[3.262, -6.524], [6.266, -2.387], [5.925, 2.257], [0, 0], [2.981, 5.963], [0, 0], [3.988, 7.979], [-2.335, 3.669], [-7.091, 4.519], [0, 0], [-7.064, -3.179], [0, 0], [-6.766, 3.045], [-6.387, -2.129], [-4.226, -7.269], [0, 0], [0, 0], [3.063, -6.127]], "v": [[-24.394, 255.294], [-9.968, 242.121], [9.597, 242.121], [24.026, 255.294], [30.971, 273.182], [38.915, 291.071], [43.202, 302.895], [42.366, 328.704], [33.371, 337.968], [7.884, 339.684], [6.299, 338.97], [-6.167, 338.97], [-7.753, 339.684], [-27.251, 340.827], [-42.396, 328.43], [-43.071, 302.895], [-38.76, 290.025], [-31.585, 273.675]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.149, 0.098, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.149, 0.098, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "animated arrow", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 27, "op": 408, "st": -145, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": ".surfaceHome", "cl": "surfaceHome", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"a": 0, "k": 446, "ix": 4}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [412, 892], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.365, 0.259, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.365, 0.259, 0, 1], "t": 466, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Volume", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 467, "st": -145, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "Part01_ThumbDemo_V02", "fr": 60, "pfr": 1, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "HAND NULL", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.39], "y": [1]}, "o": {"x": [0.59], "y": [0]}, "t": 3, "s": [10]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.15], "y": [0]}, "t": 59, "s": [0]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 95, "s": [-3]}, {"i": {"x": [0.44], "y": [1]}, "o": {"x": [0.84], "y": [0]}, "t": 122, "s": [-4]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.56], "y": [0]}, "t": 150, "s": [15]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 175, "s": [-4]}, {"i": {"x": [0.44], "y": [1]}, "o": {"x": [0.84], "y": [0]}, "t": 234, "s": [-4]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 262, "s": [15]}, {"t": 323, "s": [-5]}], "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.06], "y": [0.36]}, "t": 0, "s": [549]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [412]}, {"i": {"x": [0.56], "y": [1]}, "o": {"x": [0.88], "y": [0]}, "t": 262, "s": [412]}, {"t": 343, "s": [549]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.194], "y": [0]}, "t": 0, "s": [1031.366]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 63, "s": [920]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 122, "s": [920]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.259], "y": [0]}, "t": 145, "s": [727]}, {"i": {"x": [0.45], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 147, "s": [727]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 208, "s": [920]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 234, "s": [920]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.259], "y": [0]}, "t": 257, "s": [727]}, {"i": {"x": [0.47], "y": [1]}, "o": {"x": [0.47], "y": [0]}, "t": 259, "s": [727]}, {"t": 336, "s": [1014.366]}], "ix": 4}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Void", "np": 20, "mn": "Pseudo/289611", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/289611-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1}}, {"ty": 0, "nm": "Height", "mn": "Pseudo/289611-0002", "ix": 2, "v": {"a": 0, "k": 100, "ix": 2}}, {"ty": 0, "nm": "Offset X", "mn": "Pseudo/289611-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Offset Y", "mn": "Pseudo/289611-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "About", "mn": "Pseudo/289611-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Plague of null layers.", "mn": "Pseudo/289611-0006", "ix": 6, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Following projects", "mn": "Pseudo/289611-0008", "ix": 8, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0009", "ix": 9, "v": 0}, {"ty": 6, "nm": "through time.", "mn": "Pseudo/289611-0010", "ix": 10, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Be free of the past.", "mn": "Pseudo/289611-0012", "ix": 12, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0013", "ix": 13, "v": 0}, {"ty": 6, "nm": "Void - 1.0.0", "mn": "Pseudo/289611-0014", "ix": 14, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0015", "ix": 15, "v": 0}, {"ty": 6, "nm": "© 2022 Battle Axe Inc", "mn": "Pseudo/289611-0016", "ix": 16, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0017", "ix": 17, "v": 0}, {"ty": 6, "nm": "Void", "mn": "Pseudo/289611-0018", "ix": 18, "v": 0}]}], "ip": 0, "op": 321, "st": -40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "⨳ Thumb KO", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"k": [{"s": [17.7], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.084], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.752], "t": 61, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.548], "t": 62, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.405], "t": 63, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.298], "t": 64, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.213], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.144], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.086], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.037], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.995], "t": 69, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.958], "t": 70, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.926], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.898], "t": 72, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.874], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.852], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.832], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.815], "t": 76, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.8], "t": 77, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.786], "t": 78, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.773], "t": 79, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.763], "t": 80, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.753], "t": 81, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.744], "t": 82, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.73], "t": 84, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.715], "t": 87, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.706], "t": 148, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.723], "t": 149, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.753], "t": 150, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.794], "t": 151, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.845], "t": 152, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.906], "t": 153, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.974], "t": 154, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.048], "t": 155, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.126], "t": 156, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.206], "t": 157, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.286], "t": 158, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.366], "t": 159, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.445], "t": 160, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.522], "t": 161, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.596], "t": 162, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.667], "t": 163, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.736], "t": 164, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.801], "t": 165, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.864], "t": 166, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.923], "t": 167, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.979], "t": 168, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.033], "t": 169, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.083], "t": 170, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.131], "t": 171, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.176], "t": 172, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.219], "t": 173, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.259], "t": 174, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.297], "t": 175, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.333], "t": 176, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.366], "t": 177, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.397], "t": 178, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.427], "t": 179, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.454], "t": 180, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.48], "t": 181, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.504], "t": 182, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.526], "t": 183, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.547], "t": 184, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.566], "t": 185, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.584], "t": 186, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.6], "t": 187, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.615], "t": 188, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.628], "t": 189, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.64], "t": 190, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.651], "t": 191, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.661], "t": 192, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.677], "t": 194, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.693], "t": 197, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.682], "t": 203, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.664], "t": 204, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.642], "t": 205, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.617], "t": 206, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.589], "t": 207, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.559], "t": 208, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.528], "t": 209, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.496], "t": 210, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.462], "t": 211, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.427], "t": 212, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.391], "t": 213, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.355], "t": 214, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.318], "t": 215, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.28], "t": 216, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.241], "t": 217, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.202], "t": 218, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.163], "t": 219, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.123], "t": 220, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.083], "t": 221, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.043], "t": 222, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.002], "t": 223, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.961], "t": 224, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.92], "t": 225, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.878], "t": 226, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.837], "t": 227, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.795], "t": 228, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.754], "t": 229, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.587], "t": 233, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.545], "t": 234, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.504], "t": 235, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.462], "t": 236, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.421], "t": 237, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.38], "t": 238, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.339], "t": 239, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.298], "t": 240, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.258], "t": 241, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.218], "t": 242, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.178], "t": 243, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.139], "t": 244, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.1], "t": 245, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.062], "t": 246, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.025], "t": 247, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.988], "t": 248, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.952], "t": 249, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.917], "t": 250, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.883], "t": 251, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.85], "t": 252, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.819], "t": 253, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.79], "t": 254, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.764], "t": 255, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.74], "t": 256, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.72], "t": 257, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.706], "t": 258, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.706], "t": 260, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.723], "t": 261, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.753], "t": 262, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.794], "t": 263, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.845], "t": 264, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.906], "t": 265, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.974], "t": 266, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.048], "t": 267, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.126], "t": 268, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.206], "t": 269, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.286], "t": 270, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.366], "t": 271, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.445], "t": 272, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.522], "t": 273, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.596], "t": 274, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.667], "t": 275, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.736], "t": 276, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.801], "t": 277, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.864], "t": 278, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.923], "t": 279, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.979], "t": 280, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.033], "t": 281, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.083], "t": 282, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.131], "t": 283, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.176], "t": 284, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.219], "t": 285, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.259], "t": 286, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.297], "t": 287, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.333], "t": 288, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.366], "t": 289, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.397], "t": 290, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.427], "t": 291, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.454], "t": 292, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.48], "t": 293, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.504], "t": 294, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.526], "t": 295, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.547], "t": 296, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.566], "t": 297, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.584], "t": 298, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.6], "t": 299, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.615], "t": 300, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.628], "t": 301, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.64], "t": 302, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.651], "t": 303, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.661], "t": 304, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.677], "t": 306, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [17.693], "t": 309, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "p": {"k": [{"s": [0, 0, 0], "t": 122, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.015, 0, 0], "t": 123, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.077, 0, 0], "t": 124, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.192, 0, 0], "t": 125, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.364, 0, 0], "t": 126, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.598, 0, 0], "t": 127, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.902, 0, 0], "t": 128, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.285, 0, 0], "t": 129, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.76, 0, 0], "t": 130, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.347, 0, 0], "t": 131, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.074, 0, 0], "t": 132, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.968, 0, 0], "t": 133, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-5.068, 0, 0], "t": 134, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-6.423, 0, 0], "t": 135, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-8.089, 0, 0], "t": 136, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-10.109, 0, 0], "t": 137, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-12.416, 0, 0], "t": 138, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-14.732, 0, 0], "t": 139, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-16.688, 0, 0], "t": 140, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-18.11, 0, 0], "t": 141, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.047, 0, 0], "t": 142, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.624, 0, 0], "t": 143, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.922, 0, 0], "t": 144, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-20, 0, 0], "t": 145, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-20, 0, 0], "t": 147, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.949, 0, 0], "t": 148, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.77, 0, 0], "t": 149, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.468, 0, 0], "t": 150, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.054, 0, 0], "t": 151, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-18.54, 0, 0], "t": 152, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-17.938, 0, 0], "t": 153, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-17.258, 0, 0], "t": 154, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-16.519, 0, 0], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-15.742, 0, 0], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-14.942, 0, 0], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-14.136, 0, 0], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-13.336, 0, 0], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-12.55, 0, 0], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-11.783, 0, 0], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-11.04, 0, 0], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-10.326, 0, 0], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-9.641, 0, 0], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-8.987, 0, 0], "t": 165, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-8.363, 0, 0], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-7.77, 0, 0], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-7.207, 0, 0], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-6.673, 0, 0], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-6.169, 0, 0], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-5.691, 0, 0], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-5.24, 0, 0], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-4.813, 0, 0], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-4.411, 0, 0], "t": 174, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-4.031, 0, 0], "t": 175, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.675, 0, 0], "t": 176, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.34, 0, 0], "t": 177, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.026, 0, 0], "t": 178, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.732, 0, 0], "t": 179, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.457, 0, 0], "t": 180, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.2, 0, 0], "t": 181, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.961, 0, 0], "t": 182, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.739, 0, 0], "t": 183, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.533, 0, 0], "t": 184, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.343, 0, 0], "t": 185, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.168, 0, 0], "t": 186, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.006, 0, 0], "t": 187, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.857, 0, 0], "t": 188, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.722, 0, 0], "t": 189, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.598, 0, 0], "t": 190, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.488, 0, 0], "t": 191, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.389, 0, 0], "t": 192, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.302, 0, 0], "t": 193, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.226, 0, 0], "t": 194, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.161, 0, 0], "t": 195, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.108, 0, 0], "t": 196, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.066, 0, 0], "t": 197, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.034, 0, 0], "t": 198, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.013, 0, 0], "t": 199, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.002, 0, 0], "t": 200, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0, 0], "t": 201, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0, 0], "t": 234, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.015, 0, 0], "t": 235, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.077, 0, 0], "t": 236, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.192, 0, 0], "t": 237, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.364, 0, 0], "t": 238, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.598, 0, 0], "t": 239, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.902, 0, 0], "t": 240, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.285, 0, 0], "t": 241, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.76, 0, 0], "t": 242, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.347, 0, 0], "t": 243, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.074, 0, 0], "t": 244, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.968, 0, 0], "t": 245, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-5.068, 0, 0], "t": 246, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-6.423, 0, 0], "t": 247, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-8.089, 0, 0], "t": 248, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-10.109, 0, 0], "t": 249, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-12.416, 0, 0], "t": 250, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-14.732, 0, 0], "t": 251, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-16.688, 0, 0], "t": 252, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-18.11, 0, 0], "t": 253, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.047, 0, 0], "t": 254, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.624, 0, 0], "t": 255, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.922, 0, 0], "t": 256, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-20, 0, 0], "t": 257, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-20, 0, 0], "t": 259, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.949, 0, 0], "t": 260, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.77, 0, 0], "t": 261, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.468, 0, 0], "t": 262, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-19.054, 0, 0], "t": 263, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-18.54, 0, 0], "t": 264, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-17.938, 0, 0], "t": 265, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-17.258, 0, 0], "t": 266, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-16.519, 0, 0], "t": 267, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-15.742, 0, 0], "t": 268, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-14.942, 0, 0], "t": 269, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-14.136, 0, 0], "t": 270, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-13.336, 0, 0], "t": 271, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-12.55, 0, 0], "t": 272, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-11.783, 0, 0], "t": 273, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-11.04, 0, 0], "t": 274, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-10.326, 0, 0], "t": 275, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-9.641, 0, 0], "t": 276, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-8.987, 0, 0], "t": 277, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-8.363, 0, 0], "t": 278, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-7.77, 0, 0], "t": 279, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-7.207, 0, 0], "t": 280, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-6.673, 0, 0], "t": 281, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-6.169, 0, 0], "t": 282, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-5.691, 0, 0], "t": 283, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-5.24, 0, 0], "t": 284, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-4.813, 0, 0], "t": 285, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-4.411, 0, 0], "t": 286, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-4.031, 0, 0], "t": 287, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.675, 0, 0], "t": 288, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.34, 0, 0], "t": 289, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-3.026, 0, 0], "t": 290, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.732, 0, 0], "t": 291, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.457, 0, 0], "t": 292, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-2.2, 0, 0], "t": 293, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.961, 0, 0], "t": 294, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.739, 0, 0], "t": 295, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.533, 0, 0], "t": 296, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.343, 0, 0], "t": 297, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.168, 0, 0], "t": 298, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-1.006, 0, 0], "t": 299, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.857, 0, 0], "t": 300, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.722, 0, 0], "t": 301, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.598, 0, 0], "t": 302, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.488, 0, 0], "t": 303, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.389, 0, 0], "t": 304, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.302, 0, 0], "t": 305, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.226, 0, 0], "t": 306, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.161, 0, 0], "t": 307, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.108, 0, 0], "t": 308, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.066, 0, 0], "t": 309, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.034, 0, 0], "t": 310, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.013, 0, 0], "t": 311, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [-0.002, 0, 0], "t": 312, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0, 0], "t": 313, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 49, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.058], [20.871, -4.348], [30.147, 15.653], [-19.422, 9.856], [-43.806, 10.633], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.871, 4.348], [-30.147, -15.364], [31.033, -15.738], [13.48, -3.272], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.238, 116.511], [-118.128, 117.96], [-234.659, 118.83], [-235.238, 50.129], [-138.858, 16.664], [-91.169, 5.488], [-12.612, -36.255]], "c": true}], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.095, 4.103], [20.866, -4.391], [30.055, 14.909], [-19.458, 9.569], [-43.79, 10.76], [-18.62, 4.309], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.284, -4.689], [-20.866, 4.391], [-30.056, -14.63], [31.09, -15.283], [13.475, -3.311], [35.363, -7.848], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.667, 116.665], [-117.97, 119.232], [-229.615, 116.95], [-229.867, 50.888], [-139.016, 15.389], [-88.152, 4.787], [-12.612, -36.255]], "c": true}], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.098, 4.127], [20.863, -4.414], [30.005, 14.508], [-19.477, 9.414], [-43.782, 10.829], [-18.619, 4.316], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.287, -4.716], [-20.863, 4.414], [-30.007, -14.235], [31.121, -15.037], [13.472, -3.332], [35.361, -7.859], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.82, 116.749], [-117.884, 119.917], [-226.898, 115.937], [-226.972, 51.297], [-139.102, 14.702], [-86.526, 4.409], [-12.612, -36.255]], "c": true}], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.099, 4.142], [20.861, -4.429], [29.974, 14.261], [-19.489, 9.319], [-43.776, 10.871], [-18.619, 4.32], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.289, -4.733], [-20.861, 4.429], [-29.977, -13.992], [31.139, -14.886], [13.471, -3.345], [35.36, -7.866], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.3, 116.8], [-117.832, 120.338], [-225.228, 115.315], [-225.194, 51.548], [-139.154, 14.28], [-85.528, 4.177], [-12.612, -36.255]], "c": true}], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.101, 4.152], [20.86, -4.438], [29.953, 14.089], [-19.498, 9.253], [-43.773, 10.9], [-18.618, 4.323], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.291, -4.744], [-20.86, 4.438], [-29.956, -13.823], [31.153, -14.781], [13.469, -3.354], [35.36, -7.871], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.936, 116.835], [-117.796, 120.632], [-224.061, 114.88], [-223.952, 51.724], [-139.191, 13.985], [-84.829, 4.015], [-12.612, -36.255]], "c": true}], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.102, 4.16], [20.859, -4.446], [29.937, 13.959], [-19.504, 9.203], [-43.77, 10.923], [-18.618, 4.325], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.292, -4.753], [-20.859, 4.446], [-29.94, -13.695], [31.162, -14.701], [13.469, -3.361], [35.359, -7.874], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.662, 116.862], [-117.768, 120.854], [-223.181, 114.552], [-223.014, 51.856], [-139.219, 13.762], [-84.303, 3.893], [-12.612, -36.255]], "c": true}], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.102, 4.166], [20.858, -4.452], [29.924, 13.857], [-19.509, 9.163], [-43.768, 10.94], [-18.618, 4.326], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.293, -4.76], [-20.858, 4.452], [-29.927, -13.594], [31.17, -14.638], [13.468, -3.366], [35.359, -7.877], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.446, 116.884], [-117.746, 121.029], [-222.486, 114.293], [-222.274, 51.961], [-139.241, 13.586], [-83.887, 3.796], [-12.612, -36.255]], "c": true}], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.103, 4.171], [20.858, -4.457], [29.914, 13.773], [-19.513, 9.131], [-43.766, 10.954], [-18.618, 4.328], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.294, -4.766], [-20.858, 4.457], [-29.917, -13.511], [31.177, -14.587], [13.467, -3.371], [35.359, -7.88], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.269, 116.901], [-117.728, 121.172], [-221.919, 114.082], [-221.67, 52.046], [-139.258, 13.443], [-83.548, 3.717], [-12.612, -36.255]], "c": true}], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.104, 4.175], [20.857, -4.461], [29.905, 13.703], [-19.516, 9.104], [-43.764, 10.966], [-18.617, 4.329], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.294, -4.771], [-20.857, 4.461], [-29.908, -13.443], [31.182, -14.544], [13.467, -3.375], [35.358, -7.882], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.121, 116.915], [-117.714, 121.291], [-221.446, 113.906], [-221.166, 52.118], [-139.273, 13.323], [-83.265, 3.652], [-12.612, -36.255]], "c": true}], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.104, 4.179], [20.857, -4.464], [29.897, 13.644], [-19.519, 9.081], [-43.763, 10.977], [-18.617, 4.33], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.295, -4.775], [-20.857, 4.464], [-29.901, -13.384], [31.186, -14.508], [13.467, -3.378], [35.358, -7.883], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.997, 116.928], [-117.701, 121.392], [-221.044, 113.756], [-220.739, 52.178], [-139.286, 13.222], [-83.025, 3.596], [-12.612, -36.255]], "c": true}], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.104, 4.182], [20.856, -4.467], [29.891, 13.593], [-19.522, 9.062], [-43.762, 10.985], [-18.617, 4.331], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.295, -4.778], [-20.856, 4.467], [-29.895, -13.334], [31.19, -14.477], [13.466, -3.38], [35.358, -7.885], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.889, 116.938], [-117.69, 121.479], [-220.7, 113.628], [-220.372, 52.23], [-139.297, 13.135], [-82.819, 3.548], [-12.612, -36.255]], "c": true}], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.184], [20.856, -4.47], [29.886, 13.549], [-19.524, 9.045], [-43.761, 10.993], [-18.617, 4.331], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.295, -4.781], [-20.856, 4.47], [-29.89, -13.291], [31.194, -14.45], [13.466, -3.383], [35.358, -7.886], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.796, 116.947], [-117.681, 121.554], [-220.401, 113.516], [-220.054, 52.275], [-139.306, 13.059], [-82.64, 3.507], [-12.612, -36.255]], "c": true}], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.187], [20.856, -4.472], [29.881, 13.51], [-19.526, 9.03], [-43.76, 10.999], [-18.617, 4.332], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.296, -4.784], [-20.856, 4.472], [-29.885, -13.253], [31.197, -14.426], [13.466, -3.385], [35.358, -7.887], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.715, 116.955], [-117.673, 121.62], [-220.14, 113.419], [-219.776, 52.314], [-139.314, 12.993], [-82.484, 3.47], [-12.612, -36.255]], "c": true}], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.189], [20.856, -4.474], [29.877, 13.477], [-19.527, 9.017], [-43.76, 11.005], [-18.617, 4.333], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.296, -4.786], [-20.856, 4.474], [-29.881, -13.219], [31.199, -14.406], [13.465, -3.386], [35.357, -7.888], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.643, 116.962], [-117.665, 121.678], [-219.911, 113.334], [-219.531, 52.349], [-139.322, 12.935], [-82.347, 3.438], [-12.612, -36.255]], "c": true}], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.191], [20.855, -4.476], [29.873, 13.447], [-19.529, 9.005], [-43.759, 11.01], [-18.617, 4.333], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.296, -4.788], [-20.855, 4.476], [-29.877, -13.19], [31.201, -14.387], [13.465, -3.388], [35.357, -7.889], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.58, 116.969], [-117.659, 121.729], [-219.708, 113.258], [-219.316, 52.379], [-139.328, 12.884], [-82.226, 3.41], [-12.612, -36.255]], "c": true}], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.192], [20.855, -4.477], [29.87, 13.42], [-19.53, 8.995], [-43.758, 11.015], [-18.617, 4.334], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.79], [-20.855, 4.477], [-29.874, -13.164], [31.203, -14.371], [13.465, -3.389], [35.357, -7.89], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.524, 116.974], [-117.654, 121.774], [-219.529, 113.191], [-219.124, 52.406], [-139.334, 12.839], [-82.118, 3.385], [-12.612, -36.255]], "c": true}], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.194], [20.855, -4.478], [29.867, 13.397], [-19.531, 8.986], [-43.758, 11.019], [-18.617, 4.334], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.791], [-20.855, 4.478], [-29.871, -13.141], [31.205, -14.357], [13.465, -3.391], [35.357, -7.89], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.475, 116.979], [-117.649, 121.815], [-219.369, 113.132], [-218.955, 52.43], [-139.339, 12.798], [-82.023, 3.363], [-12.612, -36.255]], "c": true}], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.195], [20.855, -4.48], [29.864, 13.376], [-19.532, 8.978], [-43.757, 11.022], [-18.617, 4.334], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.793], [-20.855, 4.48], [-29.868, -13.12], [31.207, -14.344], [13.465, -3.392], [35.357, -7.891], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.43, 116.983], [-117.644, 121.85], [-219.227, 113.079], [-218.803, 52.451], [-139.343, 12.762], [-81.938, 3.343], [-12.612, -36.255]], "c": true}], "t": 76, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.196], [20.855, -4.481], [29.862, 13.357], [-19.533, 8.971], [-43.757, 11.026], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.794], [-20.855, 4.481], [-29.866, -13.102], [31.208, -14.332], [13.465, -3.393], [35.357, -7.891], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.391, 116.987], [-117.64, 121.882], [-219.101, 113.032], [-218.669, 52.47], [-139.347, 12.73], [-81.862, 3.326], [-12.612, -36.255]], "c": true}], "t": 77, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.197], [20.855, -4.482], [29.86, 13.34], [-19.534, 8.965], [-43.757, 11.028], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.795], [-20.855, 4.482], [-29.864, -13.085], [31.21, -14.322], [13.465, -3.394], [35.357, -7.892], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.356, 116.991], [-117.637, 121.911], [-218.988, 112.99], [-218.549, 52.487], [-139.351, 12.702], [-81.795, 3.31], [-12.612, -36.255]], "c": true}], "t": 78, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.198], [20.854, -4.483], [29.858, 13.326], [-19.535, 8.959], [-43.756, 11.031], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.796], [-20.854, 4.483], [-29.862, -13.071], [31.211, -14.313], [13.464, -3.394], [35.357, -7.892], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.324, 116.994], [-117.633, 121.936], [-218.888, 112.952], [-218.441, 52.503], [-139.354, 12.676], [-81.735, 3.296], [-12.612, -36.255]], "c": true}], "t": 79, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.199], [20.854, -4.483], [29.856, 13.312], [-19.535, 8.954], [-43.756, 11.033], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.797], [-20.854, 4.483], [-29.861, -13.058], [31.212, -14.305], [13.464, -3.395], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.297, 116.997], [-117.631, 121.959], [-218.798, 112.919], [-218.346, 52.516], [-139.357, 12.654], [-81.681, 3.284], [-12.612, -36.255]], "c": true}], "t": 80, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.199], [20.854, -4.484], [29.855, 13.301], [-19.536, 8.949], [-43.756, 11.035], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.798], [-20.854, 4.484], [-29.859, -13.046], [31.213, -14.298], [13.464, -3.396], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.272, 116.999], [-117.628, 121.979], [-218.719, 112.889], [-218.261, 52.528], [-139.359, 12.634], [-81.633, 3.273], [-12.612, -36.255]], "c": true}], "t": 81, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.2], [20.854, -4.485], [29.854, 13.29], [-19.537, 8.945], [-43.756, 11.037], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.799], [-20.854, 4.485], [-29.858, -13.036], [31.213, -14.291], [13.464, -3.396], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.25, 117.001], [-117.626, 121.996], [-218.648, 112.863], [-218.186, 52.539], [-139.361, 12.616], [-81.591, 3.263], [-12.612, -36.255]], "c": true}], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.201], [20.854, -4.485], [29.852, 13.281], [-19.537, 8.942], [-43.755, 11.039], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.799], [-20.854, 4.485], [-29.857, -13.027], [31.214, -14.286], [13.464, -3.397], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.23, 117.003], [-117.624, 122.012], [-218.586, 112.84], [-218.12, 52.548], [-139.363, 12.6], [-81.554, 3.254], [-12.612, -36.255]], "c": true}], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.201], [20.854, -4.486], [29.851, 13.273], [-19.537, 8.939], [-43.755, 11.04], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.8], [-20.854, 4.486], [-29.856, -13.019], [31.215, -14.281], [13.464, -3.397], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.213, 117.005], [-117.622, 122.026], [-218.531, 112.819], [-218.062, 52.556], [-139.365, 12.586], [-81.521, 3.247], [-12.612, -36.255]], "c": true}], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.201], [20.854, -4.486], [29.85, 13.266], [-19.538, 8.936], [-43.755, 11.041], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.8], [-20.854, 4.486], [-29.855, -13.012], [31.215, -14.277], [13.464, -3.397], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.199, 117.006], [-117.621, 122.038], [-218.483, 112.801], [-218.011, 52.563], [-139.366, 12.574], [-81.493, 3.24], [-12.612, -36.255]], "c": true}], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.202], [20.854, -4.486], [29.85, 13.26], [-19.538, 8.933], [-43.755, 11.042], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.801], [-20.854, 4.486], [-29.854, -13.006], [31.216, -14.273], [13.464, -3.398], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.186, 117.007], [-117.619, 122.048], [-218.442, 112.786], [-217.967, 52.57], [-139.368, 12.564], [-81.468, 3.234], [-12.612, -36.255]], "c": true}], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.202], [20.854, -4.487], [29.849, 13.255], [-19.538, 8.931], [-43.755, 11.043], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.801], [-20.854, 4.487], [-29.853, -13.001], [31.216, -14.27], [13.464, -3.398], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.174, 117.009], [-117.618, 122.057], [-218.406, 112.773], [-217.929, 52.575], [-139.369, 12.555], [-81.446, 3.229], [-12.612, -36.255]], "c": true}], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.202], [20.854, -4.487], [29.849, 13.25], [-19.538, 8.93], [-43.755, 11.044], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.801], [-20.854, 4.487], [-29.853, -12.996], [31.216, -14.267], [13.464, -3.398], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.165, 117.009], [-117.617, 122.065], [-218.376, 112.761], [-217.896, 52.58], [-139.37, 12.547], [-81.428, 3.225], [-12.612, -36.255]], "c": true}], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.487], [29.848, 13.246], [-19.539, 8.928], [-43.755, 11.045], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.487], [-29.852, -12.992], [31.217, -14.265], [13.464, -3.399], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.157, 117.01], [-117.617, 122.071], [-218.35, 112.752], [-217.869, 52.583], [-139.371, 12.541], [-81.413, 3.221], [-12.612, -36.255]], "c": true}], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.487], [29.848, 13.243], [-19.539, 8.927], [-43.755, 11.045], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.487], [-29.852, -12.99], [31.217, -14.263], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.151, 117.011], [-117.616, 122.077], [-218.33, 112.744], [-217.847, 52.586], [-139.371, 12.535], [-81.401, 3.219], [-12.612, -36.255]], "c": true}], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.487], [29.847, 13.241], [-19.539, 8.926], [-43.755, 11.046], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.487], [-29.852, -12.987], [31.217, -14.261], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.146, 117.011], [-117.615, 122.081], [-218.313, 112.738], [-217.83, 52.589], [-139.372, 12.531], [-81.391, 3.216], [-12.612, -36.255]], "c": true}], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.853, -4.49], [29.848, 13.234], [-19.538, 8.926], [-43.753, 11.05], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.853, 4.49], [-29.852, -12.98], [31.216, -14.262], [13.464, -3.4], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.609, 122.089], [-218.282, 112.739], [-217.802, 52.604], [-139.378, 12.528], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.852, -4.496], [29.852, 13.224], [-19.535, 8.933], [-43.749, 11.064], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.852, 4.496], [-29.856, -12.971], [31.211, -14.271], [13.462, -3.405], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.592, 122.092], [-218.266, 112.774], [-217.805, 52.64], [-139.395, 12.539], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.849, -4.509], [29.859, 13.206], [-19.529, 8.944], [-43.742, 11.09], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.849, 4.509], [-29.863, -12.952], [31.202, -14.29], [13.46, -3.413], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.558, 122.098], [-218.236, 112.841], [-217.812, 52.707], [-139.427, 12.561], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.843, -4.529], [29.871, 13.176], [-19.52, 8.963], [-43.729, 11.133], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.843, 4.529], [-29.875, -12.923], [31.187, -14.32], [13.456, -3.426], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.504, 122.108], [-218.187, 112.95], [-217.822, 52.818], [-139.479, 12.596], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.835, -4.56], [29.89, 13.13], [-19.505, 8.992], [-43.71, 11.199], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.835, 4.56], [-29.893, -12.877], [31.163, -14.367], [13.45, -3.446], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.42, 122.123], [-218.112, 113.119], [-217.838, 52.99], [-139.56, 12.65], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.823, -4.609], [29.918, 13.059], [-19.483, 9.037], [-43.681, 11.3], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.823, 4.609], [-29.921, -12.806], [31.128, -14.438], [13.441, -3.477], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.29, 122.147], [-217.996, 113.379], [-217.863, 53.253], [-139.685, 12.734], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.804, -4.686], [29.964, 12.946], [-19.447, 9.109], [-43.634, 11.462], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.804, 4.686], [-29.966, -12.693], [31.07, -14.553], [13.427, -3.527], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.083, 122.185], [-217.811, 113.794], [-217.902, 53.674], [-139.884, 12.867], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.77, -4.822], [30.044, 12.747], [-19.384, 9.235], [-43.551, 11.747], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.77, 4.822], [-30.044, -12.494], [30.969, -14.755], [13.401, -3.615], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-116.718, 122.253], [-217.485, 114.525], [-217.971, 54.416], [-140.234, 13.103], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.705, -5.078], [30.195, 12.372], [-19.265, 9.473], [-43.396, 12.284], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.705, 5.078], [-30.192, -12.118], [30.779, -15.136], [13.353, -3.78], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-116.032, 122.379], [-216.87, 115.902], [-218.102, 55.813], [-140.895, 13.546], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.64, -5.337], [30.347, 11.993], [-19.145, 9.713], [-43.239, 12.825], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.64, 5.337], [-30.341, -11.74], [30.588, -15.519], [13.305, -3.947], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.339, 122.506], [-216.251, 117.29], [-218.233, 57.222], [-141.561, 13.993], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.601, -5.492], [30.439, 11.766], [-19.073, 9.857], [-43.144, 13.15], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.601, 5.492], [-30.431, -11.513], [30.473, -15.749], [13.276, -4.047], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.924, 122.583], [-215.879, 118.123], [-218.312, 58.067], [-141.96, 14.261], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 106, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.575, -5.593], [30.498, 11.617], [-19.026, 9.952], [-43.083, 13.363], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.575, 5.593], [-30.489, -11.364], [30.397, -15.9], [13.257, -4.112], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.651, 122.633], [-215.635, 118.67], [-218.364, 58.621], [-142.223, 14.437], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.557, -5.667], [30.542, 11.51], [-18.992, 10.02], [-43.038, 13.517], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.557, 5.667], [-30.532, -11.257], [30.343, -16.009], [13.243, -4.159], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.455, 122.669], [-215.459, 119.064], [-218.401, 59.021], [-142.412, 14.564], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 108, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.543, -5.723], [30.575, 11.428], [-18.966, 10.072], [-43.004, 13.634], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.543, 5.723], [-30.564, -11.175], [30.301, -16.092], [13.233, -4.195], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.305, 122.697], [-215.325, 119.365], [-218.43, 59.326], [-142.556, 14.661], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 109, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.531, -5.767], [30.601, 11.363], [-18.945, 10.113], [-42.977, 13.727], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.531, 5.767], [-30.589, -11.11], [30.269, -16.158], [13.225, -4.224], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.186, 122.719], [-215.219, 119.602], [-218.452, 59.567], [-142.67, 14.737], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 110, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.523, -5.802], [30.622, 11.312], [-18.929, 10.146], [-42.956, 13.801], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.523, 5.802], [-30.61, -11.058], [30.242, -16.21], [13.218, -4.247], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.092, 122.736], [-215.134, 119.792], [-218.47, 59.76], [-142.761, 14.798], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 111, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.515, -5.831], [30.638, 11.269], [-18.916, 10.173], [-42.938, 13.861], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.515, 5.831], [-30.626, -11.016], [30.221, -16.253], [13.213, -4.265], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.014, 122.75], [-215.066, 119.947], [-218.485, 59.916], [-142.835, 14.848], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 112, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.509, -5.855], [30.652, 11.235], [-18.905, 10.194], [-42.924, 13.91], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.509, 5.855], [-30.64, -10.982], [30.204, -16.288], [13.208, -4.28], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.952, 122.762], [-215.009, 120.073], [-218.497, 60.044], [-142.896, 14.888], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 113, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.505, -5.874], [30.664, 11.207], [-18.896, 10.212], [-42.912, 13.951], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.505, 5.874], [-30.651, -10.954], [30.19, -16.316], [13.205, -4.293], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.9, 122.771], [-214.963, 120.176], [-218.507, 60.149], [-142.945, 14.922], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 114, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.501, -5.889], [30.673, 11.184], [-18.889, 10.227], [-42.903, 13.983], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.501, 5.889], [-30.66, -10.931], [30.178, -16.339], [13.202, -4.303], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.858, 122.779], [-214.926, 120.259], [-218.514, 60.234], [-142.985, 14.948], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 115, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.497, -5.902], [30.68, 11.166], [-18.883, 10.238], [-42.895, 14.009], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.497, 5.902], [-30.667, -10.913], [30.169, -16.358], [13.2, -4.311], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.825, 122.785], [-214.896, 120.327], [-218.521, 60.302], [-143.017, 14.97], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 116, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.495, -5.912], [30.686, 11.151], [-18.878, 10.248], [-42.889, 14.03], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.495, 5.912], [-30.673, -10.898], [30.161, -16.372], [13.198, -4.317], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.799, 122.79], [-214.872, 120.38], [-218.526, 60.356], [-143.043, 14.987], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 117, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.493, -5.919], [30.69, 11.14], [-18.875, 10.255], [-42.885, 14.046], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.493, 5.919], [-30.677, -10.887], [30.156, -16.384], [13.196, -4.322], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.778, 122.794], [-214.854, 120.42], [-218.53, 60.397], [-143.062, 15], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 118, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.492, -5.925], [30.694, 11.132], [-18.872, 10.26], [-42.881, 14.058], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.492, 5.925], [-30.68, -10.879], [30.152, -16.392], [13.195, -4.326], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.763, 122.797], [-214.841, 120.45], [-218.533, 60.427], [-143.077, 15.01], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 119, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.491, -5.929], [30.696, 11.127], [-18.87, 10.263], [-42.879, 14.065], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.491, 5.929], [-30.683, -10.873], [30.149, -16.397], [13.195, -4.328], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.753, 122.798], [-214.832, 120.47], [-218.534, 60.448], [-143.086, 15.016], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 120, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.491, -5.926], [30.694, 11.13], [-18.871, 10.261], [-42.88, 14.06], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.491, 5.926], [-30.681, -10.877], [30.15, -16.393], [13.195, -4.327], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.76, 122.801], [-214.837, 120.461], [-218.532, 60.439], [-143.08, 15.017], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 123, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.494, -5.909], [30.683, 11.154], [-18.878, 10.245], [-42.888, 14.024], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.494, 5.909], [-30.67, -10.901], [30.161, -16.368], [13.197, -4.315], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.807, 122.805], [-214.874, 120.382], [-218.52, 60.362], [-143.036, 15.005], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 124, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.5, -5.878], [30.662, 11.198], [-18.891, 10.215], [-42.903, 13.959], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.5, 5.878], [-30.649, -10.945], [30.181, -16.321], [13.202, -4.295], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.891, 122.811], [-214.94, 120.24], [-218.497, 60.223], [-142.957, 14.984], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 125, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.509, -5.831], [30.631, 11.264], [-18.91, 10.171], [-42.926, 13.86], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.509, 5.831], [-30.619, -11.011], [30.212, -16.25], [13.209, -4.265], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.018, 122.822], [-215.041, 120.025], [-218.463, 60.011], [-142.837, 14.951], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 126, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.522, -5.764], [30.587, 11.358], [-18.937, 10.109], [-42.957, 13.721], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.522, 5.764], [-30.575, -11.105], [30.255, -16.15], [13.219, -4.222], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.198, 122.836], [-215.184, 119.721], [-218.416, 59.713], [-142.668, 14.904], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 127, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.539, -5.674], [30.527, 11.485], [-18.973, 10.024], [-43.001, 13.532], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.539, 5.674], [-30.517, -11.232], [30.313, -16.015], [13.232, -4.164], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.441, 122.855], [-215.377, 119.309], [-218.351, 59.31], [-142.439, 14.841], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 128, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.562, -5.555], [30.448, 11.653], [-19.021, 9.911], [-43.058, 13.281], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.562, 5.555], [-30.439, -11.4], [30.39, -15.835], [13.249, -4.087], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.764, 122.881], [-215.634, 118.763], [-218.265, 58.774], [-142.135, 14.757], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 129, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.592, -5.398], [30.343, 11.875], [-19.085, 9.763], [-43.133, 12.95], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.592, 5.398], [-30.336, -11.622], [30.492, -15.598], [13.273, -3.985], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.19, 122.915], [-215.972, 118.042], [-218.152, 58.067], [-141.734, 14.646], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 130, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.632, -5.189], [30.204, 12.169], [-19.169, 9.566], [-43.233, 12.512], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.632, 5.189], [-30.2, -11.916], [30.627, -15.284], [13.303, -3.85], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.755, 122.96], [-216.421, 117.086], [-218.001, 57.13], [-141.203, 14.5], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 131, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.686, -4.908], [30.018, 12.566], [-19.283, 9.302], [-43.368, 11.922], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.686, 4.908], [-30.017, -12.313], [30.808, -14.861], [13.345, -3.669], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-116.515, 123.021], [-217.025, 115.8], [-217.799, 55.869], [-140.488, 14.302], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 132, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.76, -4.519], [29.759, 13.113], [-19.44, 8.935], [-43.554, 11.106], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.76, 4.519], [-29.763, -12.861], [31.059, -14.276], [13.402, -3.417], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.567, 123.104], [-217.86, 114.021], [-217.519, 54.124], [-139.498, 14.029], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 133, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.865, -3.972], [29.396, 13.885], [-19.662, 8.42], [-43.816, 9.956], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.865, 3.972], [-29.406, -13.633], [31.413, -13.452], [13.483, -3.064], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-119.049, 123.222], [-219.037, 111.514], [-217.125, 51.666], [-138.104, 13.644], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 134, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.997, -3.279], [28.935, 14.862], [-19.942, 7.767], [-44.148, 8.501], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.997, 3.279], [-28.954, -14.61], [31.861, -12.409], [13.585, -2.616], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-120.924, 123.372], [-220.526, 108.342], [-216.626, 48.556], [-136.34, 13.158], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 135, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.11, -2.69], [28.544, 15.692], [-20.18, 7.212], [-44.431, 7.265], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.11, 2.69], [-28.57, -15.44], [32.241, -11.522], [13.672, -2.235], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-122.517, 123.499], [-221.791, 105.647], [-216.202, 45.913], [-134.841, 12.744], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 136, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.177, -2.342], [28.313, 16.183], [-20.321, 6.884], [-44.597, 6.533], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.177, 2.342], [-28.343, -15.931], [32.466, -10.998], [13.723, -2.01], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-123.459, 123.574], [-222.539, 104.053], [-215.952, 44.35], [-133.955, 12.499], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 137, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.212, -2.159], [28.191, 16.441], [-20.395, 6.711], [-44.685, 6.149], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.212, 2.159], [-28.224, -16.189], [32.585, -10.723], [13.75, -1.892], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-123.954, 123.613], [-222.933, 103.215], [-215.82, 43.528], [-133.488, 12.371], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 138, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.228, -2.074], [28.135, 16.561], [-20.429, 6.631], [-44.726, 5.97], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.228, 2.074], [-28.168, -16.31], [32.64, -10.595], [13.763, -1.837], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-124.185, 123.632], [-223.116, 102.825], [-215.759, 43.146], [-133.272, 12.311], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 139, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.232, -2.05], [28.119, 16.594], [-20.439, 6.609], [-44.737, 5.921], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.232, 2.05], [-28.153, -16.343], [32.655, -10.559], [13.766, -1.822], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-124.248, 123.637], [-223.166, 102.718], [-215.742, 43.041], [-133.212, 12.294], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 140, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.232, -2.054], [28.123, 16.592], [-20.437, 6.615], [-44.735, 5.929], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.232, 2.054], [-28.156, -16.341], [32.652, -10.569], [13.766, -1.825], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.146, 117.011], [-124.237, 123.627], [-223.187, 102.747], [-215.777, 43.054], [-133.222, 12.302], [-81.393, 3.217], [-12.612, -36.255]], "c": true}], "t": 141, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.202], [21.23, -2.068], [28.135, 16.587], [-20.431, 6.634], [-44.73, 5.957], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.801], [-21.23, 2.068], [-28.168, -16.335], [32.642, -10.599], [13.764, -1.833], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.176, 117.008], [-124.201, 123.593], [-223.254, 102.841], [-215.891, 43.095], [-133.255, 12.328], [-81.45, 3.23], [-12.612, -36.255]], "c": true}], "t": 142, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.201], [21.226, -2.092], [28.156, 16.577], [-20.42, 6.669], [-44.72, 6.008], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.799], [-21.226, 2.092], [-28.189, -16.325], [32.625, -10.655], [13.761, -1.849], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.231, 117.003], [-124.136, 123.533], [-223.377, 103.014], [-216.1, 43.171], [-133.316, 12.375], [-81.555, 3.254], [-12.612, -36.255]], "c": true}], "t": 143, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.198], [21.22, -2.131], [28.19, 16.561], [-20.403, 6.723], [-44.705, 6.086], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.797], [-21.22, 2.131], [-28.223, -16.308], [32.598, -10.741], [13.756, -1.873], [35.357, -7.892], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.316, 116.995], [-124.034, 123.438], [-223.568, 103.281], [-216.424, 43.289], [-133.409, 12.447], [-81.717, 3.292], [-12.612, -36.255]], "c": true}], "t": 144, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.195], [21.211, -2.185], [28.238, 16.539], [-20.379, 6.8], [-44.682, 6.198], [-18.617, 4.334], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.793], [-21.211, 2.185], [-28.27, -16.285], [32.56, -10.864], [13.749, -1.907], [35.357, -7.891], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.437, 116.983], [-123.889, 123.303], [-223.841, 103.664], [-216.887, 43.457], [-133.544, 12.551], [-81.95, 3.346], [-12.612, -36.255]], "c": true}], "t": 145, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.19], [21.2, -2.26], [28.304, 16.508], [-20.346, 6.905], [-44.652, 6.351], [-18.617, 4.333], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.296, -4.787], [-21.2, 2.26], [-28.335, -16.253], [32.507, -11.032], [13.74, -1.954], [35.357, -7.889], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.602, 116.966], [-123.69, 123.119], [-224.214, 104.187], [-217.52, 43.687], [-133.727, 12.693], [-82.268, 3.42], [-12.612, -36.255]], "c": true}], "t": 146, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.184], [21.184, -2.358], [28.391, 16.468], [-20.303, 7.044], [-44.612, 6.552], [-18.617, 4.331], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.295, -4.78], [-21.184, 2.358], [-28.42, -16.211], [32.438, -11.253], [13.728, -2.016], [35.358, -7.886], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.82, 116.945], [-123.428, 122.877], [-224.705, 104.876], [-218.353, 43.99], [-133.968, 12.88], [-82.687, 3.517], [-12.612, -36.255]], "c": true}], "t": 147, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.104, 4.176], [21.164, -2.483], [28.501, 16.417], [-20.247, 7.22], [-44.562, 6.808], [-18.617, 4.329], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.294, -4.771], [-21.164, 2.483], [-28.528, -16.158], [32.35, -11.534], [13.712, -2.095], [35.358, -7.882], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.097, 116.918], [-123.096, 122.568], [-225.33, 105.751], [-219.412, 44.375], [-134.275, 13.117], [-83.219, 3.641], [-12.612, -36.255]], "c": true}], "t": 148, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.102, 4.166], [21.141, -2.634], [28.634, 16.355], [-20.18, 7.434], [-44.501, 7.118], [-18.618, 4.327], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.293, -4.761], [-21.141, 2.634], [-28.659, -16.094], [32.243, -11.875], [13.693, -2.19], [35.359, -7.877], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.433, 116.885], [-122.694, 122.195], [-226.085, 106.81], [-220.694, 44.841], [-134.646, 13.404], [-83.863, 3.79], [-12.612, -36.255]], "c": true}], "t": 149, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.101, 4.156], [21.114, -2.805], [28.785, 16.285], [-20.105, 7.675], [-44.431, 7.468], [-18.618, 4.324], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.291, -4.748], [-21.114, 2.805], [-28.808, -16.021], [32.122, -12.26], [13.672, -2.298], [35.359, -7.872], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.812, 116.848], [-122.238, 121.773], [-226.94, 108.009], [-222.145, 45.369], [-135.066, 13.729], [-84.591, 3.96], [-12.612, -36.255]], "c": true}], "t": 150, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.1, 4.144], [21.086, -2.984], [28.943, 16.212], [-20.026, 7.928], [-44.359, 7.835], [-18.619, 4.321], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.29, -4.736], [-21.086, 2.984], [-28.963, -15.945], [31.996, -12.664], [13.65, -2.411], [35.36, -7.867], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.21, 116.809], [-121.761, 121.331], [-227.835, 109.264], [-223.663, 45.921], [-135.506, 14.07], [-85.354, 4.137], [-12.612, -36.255]], "c": true}], "t": 151, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.098, 4.133], [21.058, -3.158], [29.097, 16.141], [-19.949, 8.174], [-44.288, 8.192], [-18.619, 4.318], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.288, -4.723], [-21.058, 3.158], [-29.114, -15.871], [31.873, -13.055], [13.628, -2.521], [35.361, -7.862], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.595, 116.771], [-121.298, 120.901], [-228.705, 110.483], [-225.138, 46.457], [-135.933, 14.4], [-86.095, 4.309], [-12.612, -36.255]], "c": true}], "t": 152, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.097, 4.123], [21.033, -3.318], [29.238, 16.075], [-19.878, 8.4], [-44.224, 8.52], [-18.619, 4.315], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.287, -4.712], [-21.033, 3.318], [-29.253, -15.803], [31.761, -13.416], [13.608, -2.622], [35.361, -7.857], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.951, 116.736], [-120.872, 120.506], [-229.505, 111.605], [-226.495, 46.95], [-136.326, 14.705], [-86.777, 4.468], [-12.612, -36.255]], "c": true}], "t": 153, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.096, 4.114], [21.011, -3.46], [29.364, 16.017], [-19.815, 8.601], [-44.166, 8.812], [-18.62, 4.312], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.286, -4.701], [-21.011, 3.46], [-29.377, -15.742], [31.66, -13.737], [13.59, -2.712], [35.362, -7.853], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.267, 116.705], [-120.493, 120.154], [-230.218, 112.604], [-227.705, 47.39], [-136.676, 14.976], [-87.385, 4.609], [-12.612, -36.255]], "c": true}], "t": 154, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.095, 4.106], [20.991, -3.585], [29.474, 15.966], [-19.759, 8.778], [-44.115, 9.069], [-18.62, 4.31], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.284, -4.693], [-20.991, 3.585], [-29.485, -15.689], [31.572, -14.019], [13.575, -2.791], [35.362, -7.849], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.545, 116.677], [-120.159, 119.845], [-230.844, 113.481], [-228.766, 47.776], [-136.984, 15.214], [-87.918, 4.733], [-12.612, -36.255]], "c": true}], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.094, 4.1], [20.974, -3.694], [29.57, 15.921], [-19.711, 8.932], [-44.071, 9.293], [-18.62, 4.308], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.283, -4.685], [-20.974, 3.694], [-29.58, -15.642], [31.495, -14.265], [13.561, -2.86], [35.363, -7.846], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.787, 116.654], [-119.869, 119.575], [-231.389, 114.247], [-229.692, 48.113], [-137.252, 15.421], [-88.383, 4.841], [-12.612, -36.255]], "c": true}], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.093, 4.094], [20.959, -3.79], [29.654, 15.882], [-19.669, 9.067], [-44.032, 9.488], [-18.62, 4.307], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.283, -4.678], [-20.959, 3.79], [-29.662, -15.602], [31.428, -14.479], [13.549, -2.92], [35.363, -7.843], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.998, 116.633], [-119.615, 119.34], [-231.865, 114.913], [-230.499, 48.406], [-137.485, 15.602], [-88.788, 4.935], [-12.612, -36.255]], "c": true}], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.093, 4.088], [20.946, -3.872], [29.727, 15.848], [-19.632, 9.184], [-43.999, 9.658], [-18.62, 4.305], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.282, -4.672], [-20.946, 3.872], [-29.734, -15.566], [31.369, -14.666], [13.539, -2.972], [35.363, -7.841], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.182, 116.615], [-119.395, 119.135], [-232.279, 115.494], [-231.202, 48.662], [-137.689, 15.76], [-89.141, 5.017], [-12.612, -36.255]], "c": true}], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.092, 4.084], [20.935, -3.945], [29.791, 15.819], [-19.6, 9.286], [-43.969, 9.806], [-18.621, 4.304], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.281, -4.667], [-20.935, 3.945], [-29.797, -15.535], [31.318, -14.829], [13.53, -3.017], [35.364, -7.839], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.343, 116.599], [-119.202, 118.957], [-232.641, 116.002], [-231.816, 48.885], [-137.867, 15.897], [-89.45, 5.088], [-12.612, -36.255]], "c": true}], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.092, 4.08], [20.925, -4.008], [29.847, 15.793], [-19.572, 9.375], [-43.944, 9.935], [-18.621, 4.303], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.281, -4.662], [-20.925, 4.008], [-29.852, -15.509], [31.274, -14.972], [13.522, -3.057], [35.364, -7.837], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.483, 116.585], [-119.034, 118.801], [-232.957, 116.445], [-232.352, 49.08], [-138.022, 16.017], [-89.719, 5.151], [-12.612, -36.255]], "c": true}], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.076], [20.916, -4.063], [29.896, 15.77], [-19.548, 9.453], [-43.921, 10.049], [-18.621, 4.302], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.28, -4.658], [-20.916, 4.063], [-29.9, -15.485], [31.235, -15.096], [13.515, -3.092], [35.364, -7.835], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.605, 116.573], [-118.887, 118.664], [-233.233, 116.832], [-232.82, 49.25], [-138.158, 16.122], [-89.954, 5.206], [-12.612, -36.255]], "c": true}], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.073], [20.908, -4.111], [29.938, 15.75], [-19.527, 9.521], [-43.902, 10.147], [-18.621, 4.301], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.28, -4.655], [-20.908, 4.111], [-29.942, -15.464], [31.201, -15.205], [13.509, -3.123], [35.364, -7.834], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.712, 116.563], [-118.758, 118.545], [-233.474, 117.169], [-233.229, 49.399], [-138.276, 16.214], [-90.16, 5.253], [-12.612, -36.255]], "c": true}], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.071], [20.902, -4.153], [29.975, 15.733], [-19.508, 9.581], [-43.885, 10.234], [-18.621, 4.301], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.652], [-20.902, 4.153], [-29.978, -15.447], [31.171, -15.299], [13.504, -3.149], [35.364, -7.832], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.806, 116.553], [-118.647, 118.441], [-233.684, 117.464], [-233.586, 49.528], [-138.379, 16.294], [-90.339, 5.295], [-12.612, -36.255]], "c": true}], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.068], [20.896, -4.19], [30.008, 15.718], [-19.492, 9.632], [-43.87, 10.308], [-18.621, 4.3], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.649], [-20.896, 4.19], [-30.01, -15.431], [31.145, -15.382], [13.499, -3.172], [35.365, -7.831], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.887, 116.545], [-118.549, 118.351], [-233.867, 117.72], [-233.895, 49.641], [-138.469, 16.363], [-90.494, 5.331], [-12.612, -36.255]], "c": true}], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.066], [20.891, -4.221], [30.035, 15.705], [-19.478, 9.677], [-43.857, 10.373], [-18.621, 4.299], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.647], [-20.891, 4.221], [-30.037, -15.418], [31.123, -15.453], [13.495, -3.192], [35.365, -7.83], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.957, 116.539], [-118.465, 118.273], [-234.025, 117.942], [-234.163, 49.738], [-138.547, 16.423], [-90.629, 5.362], [-12.612, -36.255]], "c": true}], "t": 165, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.065], [20.887, -4.249], [30.059, 15.694], [-19.466, 9.715], [-43.846, 10.429], [-18.621, 4.299], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.645], [-20.887, 4.249], [-30.061, -15.406], [31.104, -15.514], [13.492, -3.209], [35.365, -7.83], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.017, 116.533], [-118.393, 118.206], [-234.161, 118.132], [-234.394, 49.822], [-138.613, 16.475], [-90.745, 5.389], [-12.612, -36.255]], "c": true}], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.063], [20.883, -4.272], [30.08, 15.685], [-19.456, 9.748], [-43.837, 10.476], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.643], [-20.883, 4.272], [-30.081, -15.396], [31.087, -15.566], [13.489, -3.224], [35.365, -7.829], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.069, 116.528], [-118.331, 118.149], [-234.277, 118.295], [-234.59, 49.893], [-138.67, 16.519], [-90.844, 5.412], [-12.612, -36.255]], "c": true}], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.062], [20.88, -4.291], [30.097, 15.677], [-19.447, 9.776], [-43.829, 10.516], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.642], [-20.88, 4.291], [-30.098, -15.388], [31.074, -15.61], [13.487, -3.236], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.112, 116.523], [-118.279, 118.101], [-234.374, 118.431], [-234.756, 49.954], [-138.718, 16.556], [-90.927, 5.432], [-12.612, -36.255]], "c": true}], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.061], [20.878, -4.307], [30.111, 15.67], [-19.44, 9.798], [-43.822, 10.55], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.641], [-20.878, 4.307], [-30.112, -15.381], [31.062, -15.647], [13.485, -3.246], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.148, 116.52], [-118.236, 118.061], [-234.455, 118.545], [-234.894, 50.004], [-138.758, 16.587], [-90.996, 5.448], [-12.612, -36.255]], "c": true}], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.06], [20.876, -4.321], [30.123, 15.665], [-19.434, 9.817], [-43.817, 10.577], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.64], [-20.875, 4.321], [-30.123, -15.375], [31.053, -15.676], [13.483, -3.255], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.177, 116.517], [-118.201, 118.028], [-234.521, 118.637], [-235.005, 50.044], [-138.79, 16.612], [-91.052, 5.461], [-12.612, -36.255]], "c": true}], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.874, -4.331], [30.132, 15.66], [-19.429, 9.832], [-43.813, 10.598], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.639], [-20.874, 4.331], [-30.132, -15.371], [31.046, -15.7], [13.482, -3.261], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.2, 116.515], [-118.173, 118.003], [-234.573, 118.71], [-235.093, 50.076], [-138.816, 16.632], [-91.096, 5.471], [-12.612, -36.255]], "c": true}], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.873, -4.339], [30.139, 15.657], [-19.426, 9.843], [-43.81, 10.614], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.639], [-20.873, 4.339], [-30.139, -15.368], [31.04, -15.717], [13.481, -3.266], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.217, 116.513], [-118.153, 117.984], [-234.611, 118.764], [-235.158, 50.1], [-138.835, 16.646], [-91.129, 5.479], [-12.612, -36.255]], "c": true}], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.872, -4.344], [30.144, 15.655], [-19.424, 9.85], [-43.808, 10.625], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.872, 4.344], [-30.144, -15.365], [31.036, -15.729], [13.48, -3.269], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.229, 116.512], [-118.139, 117.97], [-234.638, 118.801], [-235.204, 50.116], [-138.848, 16.656], [-91.152, 5.484], [-12.612, -36.255]], "c": true}], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.871, -4.348], [30.147, 15.648], [-19.422, 9.854], [-43.806, 10.634], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.871, 4.348], [-30.147, -15.358], [31.034, -15.735], [13.48, -3.272], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.226, 116.512], [-118.126, 117.97], [-234.619, 118.815], [-235.196, 50.135], [-138.859, 16.654], [-91.145, 5.482], [-12.612, -36.255]], "c": true}], "t": 190, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.871, -4.349], [30.146, 15.64], [-19.422, 9.851], [-43.806, 10.635], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.639], [-20.871, 4.349], [-30.146, -15.35], [31.035, -15.73], [13.48, -3.273], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.209, 116.514], [-118.125, 117.984], [-234.566, 118.796], [-235.14, 50.143], [-138.861, 16.641], [-91.114, 5.475], [-12.612, -36.255]], "c": true}], "t": 191, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.06], [20.871, -4.35], [30.144, 15.628], [-19.423, 9.846], [-43.805, 10.637], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.64], [-20.871, 4.35], [-30.144, -15.339], [31.035, -15.723], [13.48, -3.273], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.185, 116.516], [-118.122, 118.003], [-234.489, 118.767], [-235.058, 50.155], [-138.863, 16.621], [-91.068, 5.464], [-12.612, -36.255]], "c": true}], "t": 192, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.061], [20.871, -4.35], [30.142, 15.613], [-19.424, 9.84], [-43.805, 10.64], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.641], [-20.871, 4.35], [-30.142, -15.324], [31.037, -15.714], [13.479, -3.274], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.153, 116.519], [-118.119, 118.029], [-234.386, 118.728], [-234.948, 50.17], [-138.866, 16.595], [-91.006, 5.45], [-12.612, -36.255]], "c": true}], "t": 193, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.062], [20.871, -4.352], [30.14, 15.594], [-19.425, 9.833], [-43.805, 10.643], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.642], [-20.871, 4.352], [-30.14, -15.305], [31.038, -15.702], [13.479, -3.275], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.112, 116.523], [-118.115, 118.062], [-234.254, 118.679], [-234.807, 50.19], [-138.871, 16.562], [-90.927, 5.432], [-12.612, -36.255]], "c": true}], "t": 194, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.063], [20.871, -4.353], [30.137, 15.569], [-19.426, 9.824], [-43.804, 10.647], [-18.621, 4.299], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.644], [-20.871, 4.353], [-30.137, -15.281], [31.04, -15.687], [13.479, -3.276], [35.365, -7.829], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.061, 116.528], [-118.11, 118.104], [-234.089, 118.618], [-234.632, 50.215], [-138.876, 16.52], [-90.828, 5.409], [-12.612, -36.255]], "c": true}], "t": 195, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.065], [20.87, -4.355], [30.133, 15.54], [-19.427, 9.812], [-43.803, 10.652], [-18.621, 4.299], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.646], [-20.87, 4.355], [-30.133, -15.252], [31.042, -15.669], [13.479, -3.278], [35.365, -7.83], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.998, 116.534], [-118.104, 118.154], [-233.888, 118.543], [-234.418, 50.245], [-138.882, 16.469], [-90.708, 5.381], [-12.612, -36.255]], "c": true}], "t": 196, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.067], [20.87, -4.357], [30.129, 15.504], [-19.429, 9.798], [-43.803, 10.659], [-18.621, 4.3], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.648], [-20.87, 4.357], [-30.129, -15.217], [31.045, -15.647], [13.479, -3.28], [35.365, -7.831], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.923, 116.542], [-118.096, 118.215], [-233.647, 118.453], [-234.162, 50.281], [-138.89, 16.409], [-90.564, 5.347], [-12.612, -36.255]], "c": true}], "t": 197, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.07], [20.87, -4.359], [30.123, 15.462], [-19.431, 9.782], [-43.802, 10.666], [-18.621, 4.3], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.651], [-20.87, 4.359], [-30.124, -15.175], [31.048, -15.621], [13.478, -3.282], [35.364, -7.832], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.834, 116.551], [-118.087, 118.287], [-233.361, 118.347], [-233.857, 50.324], [-138.899, 16.336], [-90.393, 5.308], [-12.612, -36.255]], "c": true}], "t": 198, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.073], [20.869, -4.362], [30.117, 15.412], [-19.434, 9.763], [-43.801, 10.674], [-18.621, 4.301], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.28, -4.654], [-20.869, 4.362], [-30.118, -15.126], [31.052, -15.591], [13.478, -3.285], [35.364, -7.833], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.729, 116.561], [-118.076, 118.372], [-233.024, 118.221], [-233.498, 50.375], [-138.909, 16.251], [-90.191, 5.261], [-12.612, -36.255]], "c": true}], "t": 199, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.076], [20.869, -4.365], [30.11, 15.354], [-19.436, 9.74], [-43.799, 10.684], [-18.621, 4.302], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.28, -4.658], [-20.869, 4.365], [-30.111, -15.068], [31.056, -15.555], [13.478, -3.288], [35.364, -7.835], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.605, 116.573], [-118.064, 118.472], [-232.628, 118.073], [-233.076, 50.435], [-138.922, 16.151], [-89.954, 5.206], [-12.612, -36.255]], "c": true}], "t": 200, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.092, 4.08], [20.869, -4.369], [30.102, 15.285], [-19.44, 9.714], [-43.798, 10.696], [-18.621, 4.303], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.281, -4.663], [-20.869, 4.369], [-30.102, -15.001], [31.062, -15.513], [13.477, -3.291], [35.364, -7.837], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.461, 116.587], [-118.049, 118.589], [-232.164, 117.9], [-232.581, 50.504], [-138.936, 16.033], [-89.677, 5.141], [-12.612, -36.255]], "c": true}], "t": 201, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.092, 4.085], [20.868, -4.374], [30.092, 15.205], [-19.444, 9.683], [-43.796, 10.71], [-18.62, 4.305], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.281, -4.668], [-20.868, 4.374], [-30.092, -14.922], [31.068, -15.464], [13.477, -3.296], [35.364, -7.839], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.291, 116.604], [-118.032, 118.726], [-231.62, 117.698], [-232.003, 50.586], [-138.953, 15.896], [-89.352, 5.066], [-12.612, -36.255]], "c": true}], "t": 202, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.093, 4.091], [20.867, -4.379], [30.08, 15.111], [-19.448, 9.647], [-43.794, 10.726], [-18.62, 4.306], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.282, -4.675], [-20.867, 4.379], [-30.081, -14.829], [31.075, -15.406], [13.476, -3.301], [35.363, -7.842], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.093, 116.623], [-118.013, 118.887], [-230.984, 117.46], [-231.325, 50.682], [-138.973, 15.735], [-88.971, 4.977], [-12.612, -36.255]], "c": true}], "t": 203, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.094, 4.097], [20.867, -4.386], [30.066, 15.001], [-19.453, 9.604], [-43.792, 10.745], [-18.62, 4.308], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.283, -4.682], [-20.866, 4.386], [-30.067, -14.721], [31.083, -15.339], [13.475, -3.306], [35.363, -7.845], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.861, 116.646], [-117.989, 119.075], [-230.237, 117.182], [-230.53, 50.794], [-138.997, 15.546], [-88.524, 4.873], [-12.612, -36.255]], "c": true}], "t": 204, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.095, 4.105], [20.866, -4.393], [30.05, 14.871], [-19.46, 9.555], [-43.789, 10.767], [-18.62, 4.31], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.284, -4.691], [-20.866, 4.393], [-30.052, -14.593], [31.093, -15.26], [13.475, -3.313], [35.362, -7.849], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.587, 116.673], [-117.962, 119.296], [-229.361, 116.856], [-229.596, 50.926], [-139.024, 15.325], [-88, 4.752], [-12.612, -36.255]], "c": true}], "t": 205, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.096, 4.114], [20.864, -4.402], [30.031, 14.72], [-19.467, 9.496], [-43.786, 10.793], [-18.619, 4.312], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.286, -4.701], [-20.864, 4.402], [-30.033, -14.444], [31.105, -15.167], [13.474, -3.321], [35.362, -7.853], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.267, 116.705], [-117.929, 119.555], [-228.332, 116.472], [-228.5, 51.081], [-139.057, 15.065], [-87.385, 4.609], [-12.612, -36.255]], "c": true}], "t": 206, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.097, 4.125], [20.863, -4.412], [30.009, 14.543], [-19.476, 9.428], [-43.782, 10.823], [-18.619, 4.315], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.287, -4.714], [-20.863, 4.412], [-30.011, -14.27], [31.118, -15.058], [13.472, -3.33], [35.361, -7.858], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.894, 116.741], [-117.892, 119.857], [-227.135, 116.026], [-227.225, 51.261], [-139.094, 14.762], [-86.668, 4.442], [-12.612, -36.255]], "c": true}], "t": 207, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.099, 4.137], [20.862, -4.424], [29.984, 14.342], [-19.485, 9.35], [-43.778, 10.857], [-18.619, 4.319], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.289, -4.727], [-20.862, 4.424], [-29.987, -14.072], [31.133, -14.935], [13.471, -3.341], [35.36, -7.864], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.47, 116.783], [-117.849, 120.2], [-225.773, 115.518], [-225.774, 51.466], [-139.137, 14.417], [-85.853, 4.253], [-12.612, -36.255]], "c": true}], "t": 208, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.1, 4.15], [20.86, -4.436], [29.957, 14.124], [-19.496, 9.267], [-43.773, 10.894], [-18.618, 4.322], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.291, -4.742], [-20.86, 4.436], [-29.96, -13.857], [31.15, -14.802], [13.47, -3.352], [35.36, -7.87], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.011, 116.828], [-117.803, 120.572], [-224.3, 114.969], [-224.206, 51.688], [-139.184, 14.045], [-84.972, 4.048], [-12.612, -36.255]], "c": true}], "t": 209, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.102, 4.163], [20.859, -4.449], [29.93, 13.909], [-19.507, 9.184], [-43.769, 10.931], [-18.618, 4.326], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.292, -4.757], [-20.859, 4.449], [-29.934, -13.645], [31.166, -14.67], [13.468, -3.364], [35.359, -7.876], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.555, 116.873], [-117.757, 120.94], [-222.839, 114.425], [-222.65, 51.908], [-139.23, 13.675], [-84.098, 3.845], [-12.612, -36.255]], "c": true}], "t": 210, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.103, 4.174], [20.857, -4.46], [29.906, 13.716], [-19.516, 9.109], [-43.765, 10.964], [-18.618, 4.329], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.294, -4.77], [-20.857, 4.46], [-29.91, -13.456], [31.181, -14.552], [13.467, -3.374], [35.358, -7.881], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.149, 116.913], [-117.716, 121.268], [-221.536, 113.939], [-221.262, 52.104], [-139.27, 13.346], [-83.319, 3.664], [-12.612, -36.255]], "c": true}], "t": 211, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.184], [20.856, -4.469], [29.887, 13.561], [-19.523, 9.049], [-43.761, 10.991], [-18.617, 4.331], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.295, -4.78], [-20.856, 4.469], [-29.891, -13.302], [31.193, -14.457], [13.466, -3.382], [35.358, -7.886], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.82, 116.945], [-117.683, 121.535], [-220.479, 113.545], [-220.137, 52.263], [-139.304, 13.079], [-82.687, 3.517], [-12.612, -36.255]], "c": true}], "t": 212, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.191], [20.855, -4.476], [29.872, 13.443], [-19.529, 9.004], [-43.759, 11.011], [-18.617, 4.333], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.296, -4.788], [-20.855, 4.476], [-29.876, -13.186], [31.202, -14.385], [13.465, -3.388], [35.357, -7.889], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.571, 116.97], [-117.658, 121.736], [-219.68, 113.247], [-219.285, 52.383], [-139.329, 12.877], [-82.208, 3.406], [-12.612, -36.255]], "c": true}], "t": 213, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.196], [20.855, -4.481], [29.862, 13.358], [-19.533, 8.971], [-43.757, 11.026], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.794], [-20.855, 4.481], [-29.866, -13.102], [31.208, -14.333], [13.465, -3.393], [35.357, -7.891], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.392, 116.987], [-117.64, 121.881], [-219.104, 113.033], [-218.672, 52.47], [-139.347, 12.731], [-81.864, 3.326], [-12.612, -36.255]], "c": true}], "t": 214, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.199], [20.854, -4.484], [29.855, 13.299], [-19.536, 8.949], [-43.756, 11.035], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.798], [-20.854, 4.484], [-29.859, -13.045], [31.213, -14.297], [13.464, -3.396], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.269, 116.999], [-117.628, 121.981], [-218.71, 112.886], [-218.252, 52.529], [-139.359, 12.631], [-81.628, 3.271], [-12.612, -36.255]], "c": true}], "t": 215, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.202], [20.854, -4.486], [29.85, 13.263], [-19.538, 8.935], [-43.755, 11.042], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.801], [-20.854, 4.486], [-29.854, -13.008], [31.215, -14.274], [13.464, -3.398], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.191, 117.007], [-117.62, 122.044], [-218.46, 112.793], [-217.987, 52.567], [-139.367, 12.568], [-81.479, 3.237], [-12.612, -36.255]], "c": true}], "t": 216, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.487], [29.848, 13.243], [-19.539, 8.927], [-43.755, 11.045], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.487], [-29.852, -12.989], [31.217, -14.262], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.15, 117.011], [-117.616, 122.077], [-218.327, 112.743], [-217.844, 52.587], [-139.371, 12.535], [-81.399, 3.218], [-12.612, -36.255]], "c": true}], "t": 217, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.488], [29.847, 13.237], [-19.539, 8.925], [-43.754, 11.046], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.488], [-29.851, -12.983], [31.217, -14.259], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.615, 122.088], [-218.286, 112.728], [-217.801, 52.593], [-139.373, 12.524], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 218, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.488], [29.847, 13.237], [-19.539, 8.925], [-43.754, 11.046], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.488], [-29.851, -12.983], [31.217, -14.259], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.615, 122.088], [-218.286, 112.728], [-217.801, 52.593], [-139.373, 12.524], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 222, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.848, -4.513], [29.862, 13.2], [-19.528, 8.948], [-43.739, 11.098], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.848, 4.513], [-29.866, -12.947], [31.199, -14.296], [13.459, -3.415], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.548, 122.1], [-218.226, 112.862], [-217.814, 52.729], [-139.437, 12.567], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 223, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.823, -4.611], [29.919, 13.057], [-19.482, 9.039], [-43.68, 11.304], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.823, 4.611], [-29.922, -12.803], [31.126, -14.441], [13.441, -3.478], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.285, 122.148], [-217.992, 113.388], [-217.864, 53.263], [-139.689, 12.737], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 224, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.768, -4.829], [30.048, 12.737], [-19.381, 9.242], [-43.547, 11.762], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.768, 4.829], [-30.048, -12.483], [30.964, -14.766], [13.4, -3.619], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-116.699, 122.256], [-217.467, 114.563], [-217.975, 54.455], [-140.253, 13.115], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 225, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.687, -5.148], [30.236, 12.27], [-19.233, 9.538], [-43.353, 12.429], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.687, 5.148], [-30.232, -12.017], [30.728, -15.239], [13.34, -3.825], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.846, 122.413], [-216.704, 116.275], [-218.137, 56.192], [-141.074, 13.666], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 226, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.619, -5.421], [30.397, 11.871], [-19.106, 9.791], [-43.188, 13.001], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.619, 5.421], [-30.39, -11.617], [30.526, -15.644], [13.289, -4.001], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.114, 122.548], [-216.049, 117.741], [-218.276, 57.679], [-141.777, 14.138], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 227, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.572, -5.607], [30.507, 11.597], [-19.019, 9.965], [-43.074, 13.392], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.572, 5.607], [-30.497, -11.344], [30.387, -15.921], [13.255, -4.121], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.614, 122.64], [-215.602, 118.745], [-218.371, 58.697], [-142.258, 14.461], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 228, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.54, -5.733], [30.58, 11.414], [-18.961, 10.081], [-42.998, 13.655], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.54, 5.733], [-30.57, -11.16], [30.294, -16.107], [13.231, -4.202], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.278, 122.702], [-215.301, 119.418], [-218.435, 59.38], [-142.581, 14.678], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 229, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.519, -5.817], [30.63, 11.29], [-18.922, 10.16], [-42.947, 13.832], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.519, 5.817], [-30.618, -11.037], [30.232, -16.232], [13.215, -4.256], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.052, 122.743], [-215.099, 119.871], [-218.478, 59.84], [-142.799, 14.823], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 230, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.505, -5.873], [30.663, 11.208], [-18.896, 10.211], [-42.913, 13.948], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.505, 5.873], [-30.65, -10.955], [30.19, -16.315], [13.205, -4.292], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.166, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.903, 122.771], [-214.966, 120.17], [-218.506, 60.143], [-142.942, 14.92], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 231, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.496, -5.907], [30.683, 11.158], [-18.88, 10.244], [-42.892, 14.021], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.496, 5.907], [-30.67, -10.904], [30.165, -16.366], [13.198, -4.314], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.81, 122.788], [-214.883, 120.357], [-218.524, 60.332], [-143.032, 14.98], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 232, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.491, -5.926], [30.694, 11.131], [-18.872, 10.261], [-42.881, 14.06], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.491, 5.926], [-30.681, -10.877], [30.151, -16.393], [13.195, -4.326], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.761, 122.797], [-214.838, 120.456], [-218.533, 60.433], [-143.079, 15.012], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 233, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.491, -5.926], [30.694, 11.13], [-18.871, 10.261], [-42.88, 14.06], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.491, 5.926], [-30.681, -10.877], [30.15, -16.393], [13.195, -4.327], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.76, 122.801], [-214.837, 120.461], [-218.532, 60.439], [-143.08, 15.017], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 235, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.494, -5.909], [30.683, 11.154], [-18.878, 10.245], [-42.888, 14.024], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.494, 5.909], [-30.67, -10.901], [30.161, -16.368], [13.197, -4.315], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.807, 122.805], [-214.874, 120.382], [-218.52, 60.362], [-143.036, 15.005], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 236, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.5, -5.878], [30.662, 11.198], [-18.891, 10.215], [-42.903, 13.959], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.5, 5.878], [-30.649, -10.945], [30.181, -16.321], [13.202, -4.295], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.891, 122.811], [-214.94, 120.24], [-218.497, 60.223], [-142.957, 14.984], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 237, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.509, -5.831], [30.631, 11.264], [-18.91, 10.171], [-42.926, 13.86], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.509, 5.831], [-30.619, -11.011], [30.212, -16.25], [13.209, -4.265], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.018, 122.822], [-215.041, 120.025], [-218.463, 60.011], [-142.837, 14.951], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 238, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.522, -5.764], [30.587, 11.358], [-18.937, 10.109], [-42.957, 13.721], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.522, 5.764], [-30.575, -11.105], [30.255, -16.15], [13.219, -4.222], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.198, 122.836], [-215.184, 119.721], [-218.416, 59.713], [-142.668, 14.904], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 239, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.539, -5.674], [30.527, 11.485], [-18.973, 10.024], [-43.001, 13.532], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.539, 5.674], [-30.517, -11.232], [30.313, -16.015], [13.232, -4.164], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.441, 122.855], [-215.377, 119.309], [-218.351, 59.31], [-142.439, 14.841], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 240, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.562, -5.555], [30.448, 11.653], [-19.021, 9.911], [-43.058, 13.281], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.562, 5.555], [-30.439, -11.4], [30.39, -15.835], [13.249, -4.087], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-114.764, 122.881], [-215.634, 118.763], [-218.265, 58.774], [-142.135, 14.757], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 241, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.592, -5.398], [30.343, 11.875], [-19.085, 9.763], [-43.133, 12.95], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.592, 5.398], [-30.336, -11.622], [30.492, -15.598], [13.273, -3.985], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.19, 122.915], [-215.972, 118.042], [-218.152, 58.067], [-141.734, 14.646], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 242, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.632, -5.189], [30.204, 12.169], [-19.169, 9.566], [-43.233, 12.512], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.632, 5.189], [-30.2, -11.916], [30.627, -15.284], [13.303, -3.85], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-115.755, 122.96], [-216.421, 117.086], [-218.001, 57.13], [-141.203, 14.5], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 243, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.686, -4.908], [30.018, 12.566], [-19.283, 9.302], [-43.368, 11.922], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.686, 4.908], [-30.017, -12.313], [30.808, -14.861], [13.345, -3.669], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-116.515, 123.021], [-217.025, 115.8], [-217.799, 55.869], [-140.488, 14.302], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 244, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.76, -4.519], [29.759, 13.113], [-19.44, 8.935], [-43.554, 11.106], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.76, 4.519], [-29.763, -12.861], [31.059, -14.276], [13.402, -3.417], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.567, 123.104], [-217.86, 114.021], [-217.519, 54.124], [-139.498, 14.029], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 245, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.865, -3.972], [29.396, 13.885], [-19.662, 8.42], [-43.816, 9.956], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.865, 3.972], [-29.406, -13.633], [31.413, -13.452], [13.483, -3.064], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-119.049, 123.222], [-219.037, 111.514], [-217.125, 51.666], [-138.104, 13.644], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 246, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.997, -3.279], [28.935, 14.862], [-19.942, 7.767], [-44.148, 8.501], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.997, 3.279], [-28.954, -14.61], [31.861, -12.409], [13.585, -2.616], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-120.924, 123.372], [-220.526, 108.342], [-216.626, 48.556], [-136.34, 13.158], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 247, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.11, -2.69], [28.544, 15.692], [-20.18, 7.212], [-44.431, 7.265], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.11, 2.69], [-28.57, -15.44], [32.241, -11.522], [13.672, -2.235], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-122.517, 123.499], [-221.791, 105.647], [-216.202, 45.913], [-134.841, 12.744], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 248, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.177, -2.342], [28.313, 16.183], [-20.321, 6.884], [-44.597, 6.533], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.177, 2.342], [-28.343, -15.931], [32.466, -10.998], [13.723, -2.01], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-123.459, 123.574], [-222.539, 104.053], [-215.952, 44.35], [-133.955, 12.499], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 249, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.212, -2.159], [28.191, 16.441], [-20.395, 6.711], [-44.685, 6.149], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.212, 2.159], [-28.224, -16.189], [32.585, -10.723], [13.75, -1.892], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-123.954, 123.613], [-222.933, 103.215], [-215.82, 43.528], [-133.488, 12.371], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 250, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.228, -2.074], [28.135, 16.561], [-20.429, 6.631], [-44.726, 5.97], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.228, 2.074], [-28.168, -16.31], [32.64, -10.595], [13.763, -1.837], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-124.185, 123.632], [-223.116, 102.825], [-215.759, 43.146], [-133.272, 12.311], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 251, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.232, -2.05], [28.119, 16.594], [-20.439, 6.609], [-44.737, 5.921], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.232, 2.05], [-28.153, -16.343], [32.655, -10.559], [13.766, -1.822], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-124.248, 123.637], [-223.166, 102.718], [-215.742, 43.041], [-133.212, 12.294], [-81.375, 3.213], [-12.612, -36.255]], "c": true}], "t": 252, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.232, -2.054], [28.123, 16.592], [-20.437, 6.615], [-44.735, 5.929], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.232, 2.054], [-28.156, -16.341], [32.652, -10.569], [13.766, -1.825], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.146, 117.011], [-124.237, 123.627], [-223.187, 102.747], [-215.777, 43.054], [-133.222, 12.302], [-81.393, 3.217], [-12.612, -36.255]], "c": true}], "t": 253, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.202], [21.23, -2.068], [28.135, 16.587], [-20.431, 6.634], [-44.73, 5.957], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.801], [-21.23, 2.068], [-28.168, -16.335], [32.642, -10.599], [13.764, -1.833], [35.357, -7.894], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.176, 117.008], [-124.201, 123.593], [-223.254, 102.841], [-215.891, 43.095], [-133.255, 12.328], [-81.45, 3.23], [-12.612, -36.255]], "c": true}], "t": 254, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.201], [21.226, -2.092], [28.156, 16.577], [-20.42, 6.669], [-44.72, 6.008], [-18.617, 4.336], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.799], [-21.226, 2.092], [-28.189, -16.325], [32.625, -10.655], [13.761, -1.849], [35.357, -7.893], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.231, 117.003], [-124.136, 123.533], [-223.377, 103.014], [-216.1, 43.171], [-133.316, 12.375], [-81.555, 3.254], [-12.612, -36.255]], "c": true}], "t": 255, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.198], [21.22, -2.131], [28.19, 16.561], [-20.403, 6.723], [-44.705, 6.086], [-18.617, 4.335], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.797], [-21.22, 2.131], [-28.223, -16.308], [32.598, -10.741], [13.756, -1.873], [35.357, -7.892], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.316, 116.995], [-124.034, 123.438], [-223.568, 103.281], [-216.424, 43.289], [-133.409, 12.447], [-81.717, 3.292], [-12.612, -36.255]], "c": true}], "t": 256, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.106, 4.195], [21.211, -2.185], [28.238, 16.539], [-20.379, 6.8], [-44.682, 6.198], [-18.617, 4.334], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.297, -4.793], [-21.211, 2.185], [-28.27, -16.285], [32.56, -10.864], [13.749, -1.907], [35.357, -7.891], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.437, 116.983], [-123.889, 123.303], [-223.841, 103.664], [-216.887, 43.457], [-133.544, 12.551], [-81.95, 3.346], [-12.612, -36.255]], "c": true}], "t": 257, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.19], [21.2, -2.26], [28.304, 16.508], [-20.346, 6.905], [-44.652, 6.351], [-18.617, 4.333], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.296, -4.787], [-21.2, 2.26], [-28.335, -16.253], [32.507, -11.032], [13.74, -1.954], [35.357, -7.889], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.602, 116.966], [-123.69, 123.119], [-224.214, 104.187], [-217.52, 43.687], [-133.727, 12.693], [-82.268, 3.42], [-12.612, -36.255]], "c": true}], "t": 258, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.105, 4.184], [21.184, -2.358], [28.391, 16.468], [-20.303, 7.044], [-44.612, 6.552], [-18.617, 4.331], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.295, -4.78], [-21.184, 2.358], [-28.42, -16.211], [32.438, -11.253], [13.728, -2.016], [35.358, -7.886], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.82, 116.945], [-123.428, 122.877], [-224.705, 104.876], [-218.353, 43.99], [-133.968, 12.88], [-82.687, 3.517], [-12.612, -36.255]], "c": true}], "t": 259, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.104, 4.176], [21.164, -2.483], [28.501, 16.417], [-20.247, 7.22], [-44.562, 6.808], [-18.617, 4.329], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.294, -4.771], [-21.164, 2.483], [-28.528, -16.158], [32.35, -11.534], [13.712, -2.095], [35.358, -7.882], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.097, 116.918], [-123.096, 122.568], [-225.33, 105.751], [-219.412, 44.375], [-134.275, 13.117], [-83.219, 3.641], [-12.612, -36.255]], "c": true}], "t": 260, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.102, 4.166], [21.141, -2.634], [28.634, 16.355], [-20.18, 7.434], [-44.501, 7.118], [-18.618, 4.327], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.293, -4.761], [-21.141, 2.634], [-28.659, -16.094], [32.243, -11.875], [13.693, -2.19], [35.359, -7.877], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.433, 116.885], [-122.694, 122.195], [-226.085, 106.81], [-220.694, 44.841], [-134.646, 13.404], [-83.863, 3.79], [-12.612, -36.255]], "c": true}], "t": 261, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.101, 4.156], [21.114, -2.805], [28.785, 16.285], [-20.105, 7.675], [-44.431, 7.468], [-18.618, 4.324], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.291, -4.748], [-21.114, 2.805], [-28.808, -16.021], [32.122, -12.26], [13.672, -2.298], [35.359, -7.872], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-42.812, 116.848], [-122.238, 121.773], [-226.94, 108.009], [-222.145, 45.369], [-135.066, 13.729], [-84.591, 3.96], [-12.612, -36.255]], "c": true}], "t": 262, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.1, 4.144], [21.086, -2.984], [28.943, 16.212], [-20.026, 7.928], [-44.359, 7.835], [-18.619, 4.321], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.29, -4.736], [-21.086, 2.984], [-28.963, -15.945], [31.996, -12.664], [13.65, -2.411], [35.36, -7.867], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.21, 116.809], [-121.761, 121.331], [-227.835, 109.264], [-223.663, 45.921], [-135.506, 14.07], [-85.354, 4.137], [-12.612, -36.255]], "c": true}], "t": 263, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.098, 4.133], [21.058, -3.158], [29.097, 16.141], [-19.949, 8.174], [-44.288, 8.192], [-18.619, 4.318], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.288, -4.723], [-21.058, 3.158], [-29.114, -15.871], [31.873, -13.055], [13.628, -2.521], [35.361, -7.862], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.595, 116.771], [-121.298, 120.901], [-228.705, 110.483], [-225.138, 46.457], [-135.933, 14.4], [-86.095, 4.309], [-12.612, -36.255]], "c": true}], "t": 264, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.097, 4.123], [21.033, -3.318], [29.238, 16.075], [-19.878, 8.4], [-44.224, 8.52], [-18.619, 4.315], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.287, -4.712], [-21.033, 3.318], [-29.253, -15.803], [31.761, -13.416], [13.608, -2.622], [35.361, -7.857], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-43.951, 116.736], [-120.872, 120.506], [-229.505, 111.605], [-226.495, 46.95], [-136.326, 14.705], [-86.777, 4.468], [-12.612, -36.255]], "c": true}], "t": 265, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.096, 4.114], [21.011, -3.46], [29.364, 16.017], [-19.815, 8.601], [-44.166, 8.812], [-18.62, 4.312], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.286, -4.701], [-21.011, 3.46], [-29.377, -15.742], [31.66, -13.737], [13.59, -2.712], [35.362, -7.853], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.267, 116.705], [-120.493, 120.154], [-230.218, 112.604], [-227.705, 47.39], [-136.676, 14.976], [-87.385, 4.609], [-12.612, -36.255]], "c": true}], "t": 266, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.095, 4.106], [20.991, -3.585], [29.474, 15.966], [-19.759, 8.778], [-44.115, 9.069], [-18.62, 4.31], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.284, -4.693], [-20.991, 3.585], [-29.485, -15.689], [31.572, -14.019], [13.575, -2.791], [35.362, -7.849], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.545, 116.677], [-120.159, 119.845], [-230.844, 113.481], [-228.766, 47.776], [-136.984, 15.214], [-87.918, 4.733], [-12.612, -36.255]], "c": true}], "t": 267, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.094, 4.1], [20.974, -3.694], [29.57, 15.921], [-19.711, 8.932], [-44.071, 9.293], [-18.62, 4.308], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.283, -4.685], [-20.974, 3.694], [-29.58, -15.642], [31.495, -14.265], [13.561, -2.86], [35.363, -7.846], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.787, 116.654], [-119.869, 119.575], [-231.389, 114.247], [-229.692, 48.113], [-137.252, 15.421], [-88.383, 4.841], [-12.612, -36.255]], "c": true}], "t": 268, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.093, 4.094], [20.959, -3.79], [29.654, 15.882], [-19.669, 9.067], [-44.032, 9.488], [-18.62, 4.307], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.283, -4.678], [-20.959, 3.79], [-29.662, -15.602], [31.428, -14.479], [13.549, -2.92], [35.363, -7.843], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-44.998, 116.633], [-119.615, 119.34], [-231.865, 114.913], [-230.499, 48.406], [-137.485, 15.602], [-88.788, 4.935], [-12.612, -36.255]], "c": true}], "t": 269, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.093, 4.088], [20.946, -3.872], [29.727, 15.848], [-19.632, 9.184], [-43.999, 9.658], [-18.62, 4.305], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.282, -4.672], [-20.946, 3.872], [-29.734, -15.566], [31.369, -14.666], [13.539, -2.972], [35.363, -7.841], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.182, 116.615], [-119.395, 119.135], [-232.279, 115.494], [-231.202, 48.662], [-137.689, 15.76], [-89.141, 5.017], [-12.612, -36.255]], "c": true}], "t": 270, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.092, 4.084], [20.935, -3.945], [29.791, 15.819], [-19.6, 9.286], [-43.969, 9.806], [-18.621, 4.304], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.281, -4.667], [-20.935, 3.945], [-29.797, -15.535], [31.318, -14.829], [13.53, -3.017], [35.364, -7.839], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.343, 116.599], [-119.202, 118.957], [-232.641, 116.002], [-231.816, 48.885], [-137.867, 15.897], [-89.45, 5.088], [-12.612, -36.255]], "c": true}], "t": 271, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.092, 4.08], [20.925, -4.008], [29.847, 15.793], [-19.572, 9.375], [-43.944, 9.935], [-18.621, 4.303], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.281, -4.662], [-20.925, 4.008], [-29.852, -15.509], [31.274, -14.972], [13.522, -3.057], [35.364, -7.837], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.483, 116.585], [-119.034, 118.801], [-232.957, 116.445], [-232.352, 49.08], [-138.022, 16.017], [-89.719, 5.151], [-12.612, -36.255]], "c": true}], "t": 272, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.076], [20.916, -4.063], [29.896, 15.77], [-19.548, 9.453], [-43.921, 10.049], [-18.621, 4.302], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.28, -4.658], [-20.916, 4.063], [-29.9, -15.485], [31.235, -15.096], [13.515, -3.092], [35.364, -7.835], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.605, 116.573], [-118.887, 118.664], [-233.233, 116.832], [-232.82, 49.25], [-138.158, 16.122], [-89.954, 5.206], [-12.612, -36.255]], "c": true}], "t": 273, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.073], [20.908, -4.111], [29.938, 15.75], [-19.527, 9.521], [-43.902, 10.147], [-18.621, 4.301], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.28, -4.655], [-20.908, 4.111], [-29.942, -15.464], [31.201, -15.205], [13.509, -3.123], [35.364, -7.834], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.712, 116.563], [-118.758, 118.545], [-233.474, 117.169], [-233.229, 49.399], [-138.276, 16.214], [-90.16, 5.253], [-12.612, -36.255]], "c": true}], "t": 274, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.091, 4.071], [20.902, -4.153], [29.975, 15.733], [-19.508, 9.581], [-43.885, 10.234], [-18.621, 4.301], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.652], [-20.902, 4.153], [-29.978, -15.447], [31.171, -15.299], [13.504, -3.149], [35.364, -7.832], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.806, 116.553], [-118.647, 118.441], [-233.684, 117.464], [-233.586, 49.528], [-138.379, 16.294], [-90.339, 5.295], [-12.612, -36.255]], "c": true}], "t": 275, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.068], [20.896, -4.19], [30.008, 15.718], [-19.492, 9.632], [-43.87, 10.308], [-18.621, 4.3], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.649], [-20.896, 4.19], [-30.01, -15.431], [31.145, -15.382], [13.499, -3.172], [35.365, -7.831], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.887, 116.545], [-118.549, 118.351], [-233.867, 117.72], [-233.895, 49.641], [-138.469, 16.363], [-90.494, 5.331], [-12.612, -36.255]], "c": true}], "t": 276, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.066], [20.891, -4.221], [30.035, 15.705], [-19.478, 9.677], [-43.857, 10.373], [-18.621, 4.299], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.647], [-20.891, 4.221], [-30.037, -15.418], [31.123, -15.453], [13.495, -3.192], [35.365, -7.83], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-45.957, 116.539], [-118.465, 118.273], [-234.025, 117.942], [-234.163, 49.738], [-138.547, 16.423], [-90.629, 5.362], [-12.612, -36.255]], "c": true}], "t": 277, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.065], [20.887, -4.249], [30.059, 15.694], [-19.466, 9.715], [-43.846, 10.429], [-18.621, 4.299], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.279, -4.645], [-20.887, 4.249], [-30.061, -15.406], [31.104, -15.514], [13.492, -3.209], [35.365, -7.83], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.017, 116.533], [-118.393, 118.206], [-234.161, 118.132], [-234.394, 49.822], [-138.613, 16.475], [-90.745, 5.389], [-12.612, -36.255]], "c": true}], "t": 278, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.09, 4.063], [20.883, -4.272], [30.08, 15.685], [-19.456, 9.748], [-43.837, 10.476], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.643], [-20.883, 4.272], [-30.081, -15.396], [31.087, -15.566], [13.489, -3.224], [35.365, -7.829], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.069, 116.528], [-118.331, 118.149], [-234.277, 118.295], [-234.59, 49.893], [-138.67, 16.519], [-90.844, 5.412], [-12.612, -36.255]], "c": true}], "t": 279, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.062], [20.88, -4.291], [30.097, 15.677], [-19.447, 9.776], [-43.829, 10.516], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.642], [-20.88, 4.291], [-30.098, -15.388], [31.074, -15.61], [13.487, -3.236], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.112, 116.523], [-118.279, 118.101], [-234.374, 118.431], [-234.756, 49.954], [-138.718, 16.556], [-90.927, 5.432], [-12.612, -36.255]], "c": true}], "t": 280, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.061], [20.878, -4.307], [30.111, 15.67], [-19.44, 9.798], [-43.822, 10.55], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.641], [-20.878, 4.307], [-30.112, -15.381], [31.062, -15.647], [13.485, -3.246], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.148, 116.52], [-118.236, 118.061], [-234.455, 118.545], [-234.894, 50.004], [-138.758, 16.587], [-90.996, 5.448], [-12.612, -36.255]], "c": true}], "t": 281, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.06], [20.876, -4.321], [30.123, 15.665], [-19.434, 9.817], [-43.817, 10.577], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.64], [-20.875, 4.321], [-30.123, -15.375], [31.053, -15.676], [13.483, -3.255], [35.365, -7.828], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.177, 116.517], [-118.201, 118.028], [-234.521, 118.637], [-235.005, 50.044], [-138.79, 16.612], [-91.052, 5.461], [-12.612, -36.255]], "c": true}], "t": 282, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.874, -4.331], [30.132, 15.66], [-19.429, 9.832], [-43.813, 10.598], [-18.621, 4.298], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.639], [-20.874, 4.331], [-30.132, -15.371], [31.046, -15.7], [13.482, -3.261], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.2, 116.515], [-118.173, 118.003], [-234.573, 118.71], [-235.093, 50.076], [-138.816, 16.632], [-91.096, 5.471], [-12.612, -36.255]], "c": true}], "t": 283, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.873, -4.339], [30.139, 15.657], [-19.426, 9.843], [-43.81, 10.614], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.639], [-20.873, 4.339], [-30.139, -15.368], [31.04, -15.717], [13.481, -3.266], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.217, 116.513], [-118.153, 117.984], [-234.611, 118.764], [-235.158, 50.1], [-138.835, 16.646], [-91.129, 5.479], [-12.612, -36.255]], "c": true}], "t": 284, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.059], [20.872, -4.344], [30.144, 15.655], [-19.424, 9.85], [-43.808, 10.625], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.872, 4.344], [-30.144, -15.365], [31.036, -15.729], [13.48, -3.269], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.229, 116.512], [-118.139, 117.97], [-234.638, 118.801], [-235.204, 50.116], [-138.848, 16.656], [-91.152, 5.484], [-12.612, -36.255]], "c": true}], "t": 285, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.694117665291, 0.435294121504, 0.345098048449, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 7.087, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Thumb", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 321, "st": -40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Nail - PATH", "parent": 4, "tt": 1, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-215.854, 53.1, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.1, "y": 1}, "o": {"x": 0.05, "y": 0.7}, "t": 59, "s": [{"i": [[0, 0], [4.928, -11.595], [12.465, -0.87], [0, 0], [-14.56, 12.656], [0, 0], [-8.406, -10.146]], "o": [[7.827, 9.856], [-4.928, 11.595], [0, 0], [-20.722, -16.616], [0, 0], [12.755, -3.189], [0, 0]], "v": [[38.416, -22.549], [43.344, 11.946], [14.936, 32.527], [-32.966, 35.719], [-32.314, -26.028], [3.631, -34.724], [38.126, -23.129]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 95, "s": [{"i": [[0, 0], [4.852, -12.202], [12.469, -0.832], [0, 0], [-10.995, 10.445], [0, 0], [-8.483, -10.765]], "o": [[7.901, 10.455], [-4.852, 12.202], [0, 0], [-15.966, -14.079], [0, 0], [12.744, -3.277], [0, 0]], "v": [[40.886, -22.623], [46.055, 13.814], [17.765, 35.338], [-19.6, 30.341], [-19.67, -21.264], [5.987, -35.71], [40.592, -23.237]], "c": true}]}, {"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 122, "s": [{"i": [[0, 0], [4.232, -12.502], [12.921, -1.766], [0, 0], [-10.742, 11.216], [0, 0], [-9.55, -10.068]], "o": [[8.923, 9.803], [-4.232, 12.502], [0, 0], [-17.559, -12.801], [0, 0], [13.042, -4.219], [0, 0]], "v": [[43.524, -16.699], [51.346, 19.151], [23.347, 42.689], [-15.873, 40.533], [-19.404, -10.788], [6.329, -27.088], [43.177, -17.287]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 140, "s": [{"i": [[0, 0], [5.977, -11.693], [12.492, 0.343], [0, 0], [-11.927, 9.366], [0, 0], [-7.435, -11.514]], "o": [[6.885, 11.15], [-5.977, 11.693], [0, 0], [-14.573, -15.516], [0, 0], [12.995, -2.066], [0, 0]], "v": [[41.831, -31.462], [43.556, 5.299], [13.369, 24.072], [-23.361, 15.588], [-18.585, -35.795], [8.315, -47.769], [41.596, -32.101]], "c": true}]}, {"t": 175, "s": [{"i": [[0, 0], [4.928, -11.595], [12.465, -0.87], [0, 0], [-14.56, 12.656], [0, 0], [-8.406, -10.146]], "o": [[7.827, 9.856], [-4.928, 11.595], [0, 0], [-20.722, -16.616], [0, 0], [12.755, -3.189], [0, 0]], "v": [[38.416, -22.549], [43.344, 11.946], [14.936, 32.527], [-32.966, 35.719], [-32.314, -26.028], [3.631, -34.724], [38.126, -23.129]], "c": true}], "h": 1}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 188, "s": [{"i": [[0, 0], [4.928, -11.595], [12.465, -0.87], [0, 0], [-14.56, 12.656], [0, 0], [-8.406, -10.146]], "o": [[7.827, 9.856], [-4.928, 11.595], [0, 0], [-20.722, -16.616], [0, 0], [12.755, -3.189], [0, 0]], "v": [[38.416, -22.549], [43.344, 11.946], [14.936, 32.527], [-32.966, 35.719], [-32.314, -26.028], [3.631, -34.724], [38.126, -23.129]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.154, "y": 1}, "t": 218, "s": [{"i": [[0, 0], [4.852, -12.202], [12.469, -0.832], [0, 0], [-10.995, 10.445], [0, 0], [-8.483, -10.765]], "o": [[7.901, 10.455], [-4.852, 12.202], [0, 0], [-15.966, -14.079], [0, 0], [12.744, -3.277], [0, 0]], "v": [[40.886, -22.623], [46.055, 13.814], [17.765, 35.338], [-19.6, 30.341], [-19.67, -21.264], [5.987, -35.71], [40.592, -23.237]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 222, "s": [{"i": [[0, 0], [4.852, -12.202], [12.469, -0.832], [0, 0], [-10.995, 10.445], [0, 0], [-8.483, -10.765]], "o": [[7.901, 10.455], [-4.852, 12.202], [0, 0], [-15.966, -14.079], [0, 0], [12.744, -3.277], [0, 0]], "v": [[40.886, -22.623], [46.055, 13.814], [17.765, 35.338], [-19.6, 30.341], [-19.67, -21.264], [5.987, -35.71], [40.592, -23.237]], "c": true}]}, {"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 234, "s": [{"i": [[0, 0], [4.232, -12.502], [12.921, -1.766], [0, 0], [-10.742, 11.216], [0, 0], [-9.55, -10.068]], "o": [[8.923, 9.803], [-4.232, 12.502], [0, 0], [-17.559, -12.801], [0, 0], [13.042, -4.219], [0, 0]], "v": [[43.524, -16.699], [51.346, 19.151], [23.347, 42.689], [-15.873, 40.533], [-19.404, -10.788], [6.329, -27.088], [43.177, -17.287]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 252, "s": [{"i": [[0, 0], [5.977, -11.693], [12.492, 0.343], [0, 0], [-11.927, 9.366], [0, 0], [-7.435, -11.514]], "o": [[6.885, 11.15], [-5.977, 11.693], [0, 0], [-14.573, -15.516], [0, 0], [12.995, -2.066], [0, 0]], "v": [[41.831, -31.462], [43.556, 5.299], [13.369, 24.072], [-23.361, 15.588], [-18.585, -35.795], [8.315, -47.769], [41.596, -32.101]], "c": true}]}, {"t": 287, "s": [{"i": [[0, 0], [4.928, -11.595], [12.465, -0.87], [0, 0], [-14.56, 12.656], [0, 0], [-8.406, -10.146]], "o": [[7.827, 9.856], [-4.928, 11.595], [0, 0], [-20.722, -16.616], [0, 0], [12.755, -3.189], [0, 0]], "v": [[38.416, -22.549], [43.344, 11.946], [14.936, 32.527], [-32.966, 35.719], [-32.314, -26.028], [3.631, -34.724], [38.126, -23.129]], "c": true}], "h": 1}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.75686275959, 0.549019634724, 0.474509805441, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 7.087, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Nail", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 321, "st": -40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Thumb - PATH", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.7]}, "t": 59, "s": [17.7]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 95, "s": [15.7]}, {"i": {"x": [0.28], "y": [1]}, "o": {"x": [0.2], "y": [0]}, "t": 147, "s": [15.7]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.18], "y": [0]}, "t": 201, "s": [17.7]}, {"i": {"x": [0.28], "y": [1]}, "o": {"x": [0.2], "y": [0]}, "t": 259, "s": [15.7]}, {"t": 313, "s": [17.7]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 122, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 0.6}, "o": {"x": 0.604, "y": 0.604}, "t": 145, "s": [-20, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.28, "y": 1}, "o": {"x": 0.2, "y": 0}, "t": 147, "s": [-20, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.8, "y": 0.8}, "t": 201, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 234, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.6, "y": 0.6}, "o": {"x": 0.604, "y": 0.604}, "t": 257, "s": [-20, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.28, "y": 1}, "o": {"x": 0.2, "y": 0}, "t": 259, "s": [-20, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 313, "s": [0, 0, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 49, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 25, "nm": "Elevation", "np": 8, "mn": "ADBE Drop Shadow", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Shadow Color", "mn": "ADBE Drop Shadow-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0, 0.25], "ix": 1}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Drop Shadow-0002", "ix": 2, "v": {"a": 0, "k": 63.75, "ix": 2}}, {"ty": 0, "nm": "Direction", "mn": "ADBE Drop Shadow-0003", "ix": 3, "v": {"a": 0, "k": 180, "ix": 3}}, {"ty": 0, "nm": "Distance", "mn": "ADBE Drop Shadow-0004", "ix": 4, "v": {"a": 0, "k": 4, "ix": 4}}, {"ty": 0, "nm": "Softness", "mn": "ADBE Drop Shadow-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 7, "nm": "Shadow Only", "mn": "ADBE Drop Shadow-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.1, "y": 1}, "o": {"x": 0.05, "y": 0.7}, "t": 59, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.058], [20.871, -4.348], [30.147, 15.653], [-19.422, 9.856], [-43.806, 10.633], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.871, 4.348], [-30.147, -15.364], [31.033, -15.738], [13.48, -3.272], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.238, 116.511], [-118.128, 117.96], [-234.659, 118.83], [-235.238, 50.129], [-138.858, 16.664], [-91.169, 5.488], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 95, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.488], [29.847, 13.237], [-19.539, 8.925], [-43.754, 11.046], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.488], [-29.851, -12.983], [31.217, -14.259], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.615, 122.088], [-218.286, 112.728], [-217.801, 52.593], [-139.373, 12.524], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 122, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.49, -5.931], [30.698, 11.123], [-18.869, 10.266], [-42.877, 14.071], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.49, 5.931], [-30.684, -10.869], [30.147, -16.402], [13.194, -4.33], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.746, 122.8], [-214.825, 120.486], [-218.536, 60.463], [-143.094, 15.021], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 140, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.232, -2.05], [28.119, 16.594], [-20.439, 6.609], [-44.737, 5.921], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.232, 2.05], [-28.153, -16.343], [32.655, -10.559], [13.766, -1.822], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-124.248, 123.637], [-223.166, 102.718], [-215.742, 43.041], [-133.212, 12.294], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"t": 175, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.058], [20.871, -4.348], [30.147, 15.653], [-19.422, 9.856], [-43.806, 10.633], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.871, 4.348], [-30.147, -15.364], [31.033, -15.738], [13.48, -3.272], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.238, 116.511], [-118.128, 117.96], [-234.659, 118.83], [-235.238, 50.129], [-138.858, 16.664], [-91.169, 5.488], [-12.612, -36.255]], "c": true}], "h": 1}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 188, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.058], [20.871, -4.348], [30.147, 15.653], [-19.422, 9.856], [-43.806, 10.633], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.871, 4.348], [-30.147, -15.364], [31.033, -15.738], [13.48, -3.272], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.238, 116.511], [-118.128, 117.96], [-234.659, 118.83], [-235.238, 50.129], [-138.858, 16.664], [-91.169, 5.488], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.154, "y": 1}, "t": 218, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.488], [29.847, 13.237], [-19.539, 8.925], [-43.754, 11.046], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.488], [-29.851, -12.983], [31.217, -14.259], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.615, 122.088], [-218.286, 112.728], [-217.801, 52.593], [-139.373, 12.524], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 222, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.854, -4.488], [29.847, 13.237], [-19.539, 8.925], [-43.754, 11.046], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.854, 4.488], [-29.851, -12.983], [31.217, -14.259], [13.464, -3.399], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-117.615, 122.088], [-218.286, 112.728], [-217.801, 52.593], [-139.373, 12.524], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0.56, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 234, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [20.49, -5.931], [30.698, 11.123], [-18.869, 10.266], [-42.877, 14.071], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-20.49, 5.931], [-30.684, -10.869], [30.147, -16.402], [13.194, -4.33], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-113.746, 122.8], [-214.825, 120.486], [-218.536, 60.463], [-143.094, 15.021], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 252, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.218, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.107, 4.203], [21.232, -2.05], [28.119, 16.594], [-20.439, 6.609], [-44.737, 5.921], [-18.617, 4.337], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.298, -4.802], [-21.232, 2.05], [-28.153, -16.343], [32.655, -10.559], [13.766, -1.822], [35.357, -7.895], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.226, 97.821], [115.466, 195.08], [32.184, 139.048], [-41.137, 117.012], [-124.248, 123.637], [-223.166, 102.718], [-215.742, 43.041], [-133.212, 12.294], [-81.375, 3.213], [-12.612, -36.255]], "c": true}]}, {"t": 287, "s": [{"i": [[-2.609, 1.739], [-87.592, 74.129], [-32.219, -40.908], [25.237, -53.227], [55.367, -3.479], [27.304, 14.385], [26.089, 4.058], [20.871, -4.348], [30.147, 15.653], [-19.422, 9.856], [-43.806, 10.633], [-18.621, 4.297], [-16.233, 10.726]], "o": [[104.165, -73.861], [39.748, -33.639], [29.647, 37.643], [-33.816, 71.321], [-10.146, 0.58], [-24.064, -12.677], [-29.278, -4.638], [-20.871, 4.348], [-30.147, -15.364], [31.033, -15.738], [13.48, -3.272], [35.365, -7.827], [2.609, -2.029]], "v": [[-4.785, -41.762], [88.687, -303.495], [289.167, -149.936], [310.227, 97.821], [115.466, 195.08], [32.184, 139.048], [-46.238, 116.511], [-118.128, 117.96], [-234.659, 118.83], [-235.238, 50.129], [-138.858, 16.664], [-91.169, 5.488], [-12.612, -36.255]], "c": true}], "h": 1}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.694117665291, 0.435294121504, 0.345098048449, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 7.087, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Thumb", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 321, "st": -40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": ".onSurfaceHome", "cl": "onSurfaceHome", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 65, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 77, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 89, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 119, "s": [0]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 141, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 150, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 231, "s": [0]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 253, "s": [100]}, {"t": 262, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 1429, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rectangle Independent Corners", "np": 19, "mn": "Pseudo/0.16410552199068107", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Align", "mn": "Pseudo/0.16410552199068107-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}, {"ty": 6, "nm": "Size", "mn": "Pseudo/0.16410552199068107-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "w", "mn": "Pseudo/0.16410552199068107-0003", "ix": 3, "v": {"a": 0, "k": 414, "ix": 3}}, {"ty": 0, "nm": "h", "mn": "Pseudo/0.16410552199068107-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.8], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 123, "s": [581]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.312], "y": [4.051]}, "t": 141, "s": [910]}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 150, "s": [910]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [581]}, {"i": {"x": [0.8], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 235, "s": [581]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.312], "y": [4.051]}, "t": 253, "s": [910]}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 262, "s": [910]}, {"t": 292, "s": [581]}], "ix": 4}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Rounding", "mn": "Pseudo/0.16410552199068107-0006", "ix": 6, "v": 0}, {"ty": 0, "nm": "tl", "mn": "Pseudo/0.16410552199068107-0007", "ix": 7, "v": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 123, "s": [90]}, {"t": 141, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 150, "s": [200]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [90]}, {"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 235, "s": [90]}, {"t": 253, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 262, "s": [200]}, {"t": 292, "s": [90]}], "ix": 7}}, {"ty": 0, "nm": "tr", "mn": "Pseudo/0.16410552199068107-0008", "ix": 8, "v": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 123, "s": [90]}, {"t": 141, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 150, "s": [200]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [90]}, {"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 235, "s": [90]}, {"t": 253, "s": [200], "h": 1}, {"i": {"x": [0.58], "y": [1]}, "o": {"x": [0.42], "y": [0]}, "t": 262, "s": [200]}, {"t": 292, "s": [90]}], "ix": 8}}, {"ty": 0, "nm": "br", "mn": "Pseudo/0.16410552199068107-0009", "ix": 9, "v": {"a": 0, "k": 135, "ix": 9}}, {"ty": 0, "nm": "bl", "mn": "Pseudo/0.16410552199068107-0010", "ix": 10, "v": {"a": 0, "k": 135, "ix": 10}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Alignment", "mn": "Pseudo/0.16410552199068107-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "X Anchor %", "mn": "Pseudo/0.16410552199068107-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 0, "nm": "Y Anchor %", "mn": "Pseudo/0.16410552199068107-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "X Position", "mn": "Pseudo/0.16410552199068107-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15}}, {"ty": 0, "nm": "Y Position", "mn": "Pseudo/0.16410552199068107-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0017", "ix": 17, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 123, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.772, 0], [0, 0], [0, -49.772], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.772], [0, 0], [49.772, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.167], [-116.817, -581.35], [116.817, -581.35], [207, -491.167], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 124, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.09, 0], [0, 0], [0, -50.09], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.09], [0, 0], [50.09, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.689], [-116.241, -582.448], [116.241, -582.448], [207, -491.689], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 125, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.651, 0], [0, 0], [0, -50.651], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.651], [0, 0], [50.651, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.605], [-115.225, -584.38], [115.225, -584.38], [207, -492.605], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 126, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.485, 0], [0, 0], [0, -51.485], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -51.485], [0, 0], [51.485, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -493.959], [-113.713, -587.246], [113.713, -587.246], [207, -493.959], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 127, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-52.63, 0], [0, 0], [0, -52.63], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -52.63], [0, 0], [52.63, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.808], [-111.638, -591.17], [111.638, -591.17], [207, -495.808], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 128, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.132, 0], [0, 0], [0, -54.133], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -54.133], [0, 0], [54.133, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.219], [-108.916, -596.303], [108.916, -596.303], [207, -498.219], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 129, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.05, 0], [0, 0], [0, -56.05], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -56.05], [0, 0], [56.05, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -501.281], [-105.442, -602.839], [105.442, -602.839], [207, -501.281], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 130, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.455, 0], [0, 0], [0, -58.455], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.455], [0, 0], [58.455, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -505.109], [-101.083, -611.026], [101.083, -611.026], [207, -505.109], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 131, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.443, 0], [0, 0], [0, -61.443], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -61.443], [0, 0], [61.443, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -509.862], [-95.67, -621.193], [95.669, -621.193], [207, -509.862], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 132, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-65.136, 0], [0, 0], [0, -65.136], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -65.136], [0, 0], [65.136, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.772], [-88.979, -633.792], [88.979, -633.792], [207, -515.772], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 133, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.689, 0], [0, 0], [0, -69.689], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -69.689], [0, 0], [69.689, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.201], [-80.73, -649.472], [80.73, -649.472], [207, -523.201], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 134, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-75.292, 0], [0, 0], [0, -75.292], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -75.292], [0, 0], [75.292, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.789], [-70.578, -669.211], [70.578, -669.211], [207, -532.789], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 135, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-82.119, 0], [0, 0], [0, -82.119], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -82.119], [0, 0], [82.119, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -545.809], [-58.206, -694.603], [58.206, -694.603], [207, -545.809], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 136, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.124, 0], [0, 0], [0, -90.124], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -90.124], [0, 0], [90.124, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -565.184], [-43.703, -728.481], [43.703, -728.481], [207, -565.184], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 137, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-98.484, 0], [0, 0], [0, -98.484], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -98.484], [0, 0], [98.484, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -597.873], [-28.555, -776.318], [28.555, -776.318], [207, -597.873], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 138, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.33, 0], [0, 0], [0, -105.33], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.33], [0, 0], [105.33, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -653.5], [-16.151, -844.349], [16.151, -844.349], [207, -653.5], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 139, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.245, 0], [0, 0], [0, -109.245], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.245], [0, 0], [109.245, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -700.008], [-9.056, -897.952], [9.056, -897.952], [207, -700.008], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 140, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 141, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.387], [-7, -911.387], [7, -911.387], [207, -711.387], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 142, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.036], [-7, -911.036], [7, -911.036], [207, -711.036], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 144, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.362], [-7, -910.362], [7, -910.362], [207, -710.362], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 146, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 150, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.251, 0], [0, 0], [0, -110.251], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.251], [0, 0], [110.251, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -709.533], [-7.234, -909.299], [7.234, -909.299], [207, -709.533], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 151, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.855, 0], [0, 0], [0, -109.855], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.855], [0, 0], [109.855, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -708.106], [-7.951, -907.155], [7.951, -907.155], [207, -708.106], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 152, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.183, 0], [0, 0], [0, -109.183], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.183], [0, 0], [109.183, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -705.681], [-9.169, -903.511], [9.169, -903.511], [207, -705.681], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 153, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-108.224, 0], [0, 0], [0, -108.224], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -108.224], [0, 0], [108.224, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -702.223], [-10.906, -898.317], [10.906, -898.317], [207, -702.223], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 154, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-106.972, 0], [0, 0], [0, -106.972], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -106.972], [0, 0], [106.972, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -697.707], [-13.174, -891.533], [13.174, -891.533], [207, -697.707], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.423, 0], [0, 0], [0, -105.422], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.422], [0, 0], [105.423, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -692.117], [-15.983, -883.134], [15.983, -883.134], [207, -692.117], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-103.574, 0], [0, 0], [0, -103.574], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -103.574], [0, 0], [103.574, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -685.448], [-19.332, -873.116], [19.332, -873.116], [207, -685.448], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-101.431, 0], [0, 0], [0, -101.431], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -101.431], [0, 0], [101.431, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -677.717], [-23.215, -861.502], [23.215, -861.502], [207, -677.717], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-99.003, 0], [0, 0], [0, -99.003], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -99.003], [0, 0], [99.003, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -668.96], [-27.614, -848.347], [27.614, -848.347], [207, -668.96], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-96.309, 0], [0, 0], [0, -96.309], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -96.309], [0, 0], [96.309, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -659.241], [-32.495, -833.746], [32.495, -833.746], [207, -659.241], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-93.374, 0], [0, 0], [0, -93.374], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -93.374], [0, 0], [93.374, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -648.653], [-37.814, -817.839], [37.814, -817.839], [207, -648.653], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.232, 0], [0, 0], [0, -90.232], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -90.232], [0, 0], [90.232, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -637.317], [-43.507, -800.81], [43.507, -800.81], [207, -637.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-86.925, 0], [0, 0], [0, -86.925], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -86.925], [0, 0], [86.925, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -625.39], [-49.498, -782.892], [49.498, -782.892], [207, -625.39], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-83.505, 0], [0, 0], [0, -83.505], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -83.505], [0, 0], [83.505, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -613.051], [-55.696, -764.355], [55.696, -764.355], [207, -613.051], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-76.546, 0], [0, 0], [0, -76.546], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -76.546], [0, 0], [76.546, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -587.949], [-68.304, -726.645], [68.304, -726.645], [207, -587.949], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-73.126, 0], [0, 0], [0, -73.126], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -73.126], [0, 0], [73.126, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -575.61], [-74.502, -708.108], [74.502, -708.108], [207, -575.61], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.819, 0], [0, 0], [0, -69.819], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -69.819], [0, 0], [69.819, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -563.683], [-80.493, -690.19], [80.493, -690.19], [207, -563.683], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-66.677, 0], [0, 0], [0, -66.677], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -66.677], [0, 0], [66.677, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -552.347], [-86.186, -673.161], [86.186, -673.161], [207, -552.347], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-63.742, 0], [0, 0], [0, -63.742], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -63.742], [0, 0], [63.742, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -541.759], [-91.505, -657.254], [91.505, -657.254], [207, -541.759], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.048, 0], [0, 0], [0, -61.048], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -61.048], [0, 0], [61.048, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.04], [-96.386, -642.653], [96.386, -642.653], [207, -532.04], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.62, 0], [0, 0], [0, -58.62], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.62], [0, 0], [58.62, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.283], [-100.785, -629.498], [100.785, -629.498], [207, -523.283], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.477, 0], [0, 0], [0, -56.477], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -56.477], [0, 0], [56.477, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.552], [-104.668, -617.884], [104.668, -617.884], [207, -515.552], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.628, 0], [0, 0], [0, -54.629], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -54.629], [0, 0], [54.628, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -508.884], [-108.017, -607.866], [108.017, -607.866], [207, -508.884], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 174, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-53.079, 0], [0, 0], [0, -53.079], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -53.079], [0, 0], [53.079, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -503.293], [-110.826, -599.467], [110.826, -599.467], [207, -503.293], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 175, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.827, 0], [0, 0], [0, -51.827], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -51.827], [0, 0], [51.827, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.777], [-113.094, -592.683], [113.094, -592.683], [207, -498.777], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 176, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.868, 0], [0, 0], [0, -50.868], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.868], [0, 0], [50.868, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.319], [-114.831, -587.489], [114.831, -587.489], [207, -495.319], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 177, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.196, 0], [0, 0], [0, -50.196], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.196], [0, 0], [50.196, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.894], [-116.049, -583.845], [116.049, -583.845], [207, -492.894], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 178, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.8, 0], [0, 0], [0, -49.8], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.8], [0, 0], [49.8, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.467], [-116.766, -581.701], [116.766, -581.701], [207, -491.467], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 179, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 180, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 235, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.772, 0], [0, 0], [0, -49.772], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.772], [0, 0], [49.772, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.167], [-116.817, -581.35], [116.817, -581.35], [207, -491.167], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 236, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.09, 0], [0, 0], [0, -50.09], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.09], [0, 0], [50.09, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.689], [-116.241, -582.448], [116.241, -582.448], [207, -491.689], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 237, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.651, 0], [0, 0], [0, -50.651], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.651], [0, 0], [50.651, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.605], [-115.225, -584.38], [115.225, -584.38], [207, -492.605], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 238, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.485, 0], [0, 0], [0, -51.485], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -51.485], [0, 0], [51.485, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -493.959], [-113.713, -587.246], [113.713, -587.246], [207, -493.959], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 239, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-52.63, 0], [0, 0], [0, -52.63], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -52.63], [0, 0], [52.63, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.808], [-111.638, -591.17], [111.638, -591.17], [207, -495.808], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 240, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.132, 0], [0, 0], [0, -54.133], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -54.133], [0, 0], [54.133, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.219], [-108.916, -596.303], [108.916, -596.303], [207, -498.219], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 241, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.05, 0], [0, 0], [0, -56.05], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -56.05], [0, 0], [56.05, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -501.281], [-105.442, -602.839], [105.442, -602.839], [207, -501.281], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 242, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.455, 0], [0, 0], [0, -58.455], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.455], [0, 0], [58.455, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -505.109], [-101.083, -611.026], [101.083, -611.026], [207, -505.109], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 243, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.443, 0], [0, 0], [0, -61.443], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -61.443], [0, 0], [61.443, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -509.862], [-95.67, -621.193], [95.669, -621.193], [207, -509.862], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 244, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-65.136, 0], [0, 0], [0, -65.136], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -65.136], [0, 0], [65.136, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.772], [-88.979, -633.792], [88.979, -633.792], [207, -515.772], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 245, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.689, 0], [0, 0], [0, -69.689], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -69.689], [0, 0], [69.689, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.201], [-80.73, -649.472], [80.73, -649.472], [207, -523.201], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 246, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-75.292, 0], [0, 0], [0, -75.292], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -75.292], [0, 0], [75.292, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.789], [-70.578, -669.211], [70.578, -669.211], [207, -532.789], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 247, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-82.119, 0], [0, 0], [0, -82.119], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -82.119], [0, 0], [82.119, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -545.809], [-58.206, -694.603], [58.206, -694.603], [207, -545.809], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 248, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.124, 0], [0, 0], [0, -90.124], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -90.124], [0, 0], [90.124, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -565.184], [-43.703, -728.481], [43.703, -728.481], [207, -565.184], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 249, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-98.484, 0], [0, 0], [0, -98.484], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -98.484], [0, 0], [98.484, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -597.873], [-28.555, -776.318], [28.555, -776.318], [207, -597.873], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 250, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.33, 0], [0, 0], [0, -105.33], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.33], [0, 0], [105.33, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -653.5], [-16.151, -844.349], [16.151, -844.349], [207, -653.5], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 251, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.245, 0], [0, 0], [0, -109.245], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.245], [0, 0], [109.245, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -700.008], [-9.056, -897.952], [9.056, -897.952], [207, -700.008], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 252, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 253, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.387], [-7, -911.387], [7, -911.387], [207, -711.387], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 254, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -711.036], [-7, -911.036], [7, -911.036], [207, -711.036], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 256, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.362], [-7, -910.362], [7, -910.362], [207, -710.362], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 258, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710], [-7, -910], [7, -910], [207, -710], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 262, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.251, 0], [0, 0], [0, -110.251], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.251], [0, 0], [110.251, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -709.533], [-7.234, -909.299], [7.234, -909.299], [207, -709.533], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 263, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.855, 0], [0, 0], [0, -109.855], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.855], [0, 0], [109.855, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -708.106], [-7.951, -907.155], [7.951, -907.155], [207, -708.106], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 264, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-109.183, 0], [0, 0], [0, -109.183], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -109.183], [0, 0], [109.183, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -705.681], [-9.169, -903.511], [9.169, -903.511], [207, -705.681], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 265, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-108.224, 0], [0, 0], [0, -108.224], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -108.224], [0, 0], [108.224, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -702.223], [-10.906, -898.317], [10.906, -898.317], [207, -702.223], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 266, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-106.972, 0], [0, 0], [0, -106.972], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -106.972], [0, 0], [106.972, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -697.707], [-13.174, -891.533], [13.174, -891.533], [207, -697.707], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 267, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-105.423, 0], [0, 0], [0, -105.422], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -105.422], [0, 0], [105.423, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -692.117], [-15.983, -883.134], [15.983, -883.134], [207, -692.117], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 268, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-103.574, 0], [0, 0], [0, -103.574], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -103.574], [0, 0], [103.574, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -685.448], [-19.332, -873.116], [19.332, -873.116], [207, -685.448], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 269, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-101.431, 0], [0, 0], [0, -101.431], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -101.431], [0, 0], [101.431, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -677.717], [-23.215, -861.502], [23.215, -861.502], [207, -677.717], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 270, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-99.003, 0], [0, 0], [0, -99.003], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -99.003], [0, 0], [99.003, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -668.96], [-27.614, -848.347], [27.614, -848.347], [207, -668.96], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 271, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-96.309, 0], [0, 0], [0, -96.309], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -96.309], [0, 0], [96.309, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -659.241], [-32.495, -833.746], [32.495, -833.746], [207, -659.241], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 272, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-93.374, 0], [0, 0], [0, -93.374], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -93.374], [0, 0], [93.374, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -648.653], [-37.814, -817.839], [37.814, -817.839], [207, -648.653], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 273, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-90.232, 0], [0, 0], [0, -90.232], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -90.232], [0, 0], [90.232, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -637.317], [-43.507, -800.81], [43.507, -800.81], [207, -637.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 274, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-86.925, 0], [0, 0], [0, -86.925], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -86.925], [0, 0], [86.925, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -625.39], [-49.498, -782.892], [49.498, -782.892], [207, -625.39], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 275, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-83.505, 0], [0, 0], [0, -83.505], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -83.505], [0, 0], [83.505, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -613.051], [-55.696, -764.355], [55.696, -764.355], [207, -613.051], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 276, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-76.546, 0], [0, 0], [0, -76.546], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -76.546], [0, 0], [76.546, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -587.949], [-68.304, -726.645], [68.304, -726.645], [207, -587.949], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 278, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-73.126, 0], [0, 0], [0, -73.126], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -73.126], [0, 0], [73.126, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -575.61], [-74.502, -708.108], [74.502, -708.108], [207, -575.61], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 279, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-69.819, 0], [0, 0], [0, -69.819], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -69.819], [0, 0], [69.819, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -563.683], [-80.493, -690.19], [80.493, -690.19], [207, -563.683], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 280, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-66.677, 0], [0, 0], [0, -66.677], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -66.677], [0, 0], [66.677, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -552.347], [-86.186, -673.161], [86.186, -673.161], [207, -552.347], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 281, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-63.742, 0], [0, 0], [0, -63.742], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -63.742], [0, 0], [63.742, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -541.759], [-91.505, -657.254], [91.505, -657.254], [207, -541.759], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 282, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-61.048, 0], [0, 0], [0, -61.048], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -61.048], [0, 0], [61.048, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -532.04], [-96.386, -642.653], [96.386, -642.653], [207, -532.04], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 283, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-58.62, 0], [0, 0], [0, -58.62], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -58.62], [0, 0], [58.62, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -523.283], [-100.785, -629.498], [100.785, -629.498], [207, -523.283], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 284, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-56.477, 0], [0, 0], [0, -56.477], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -56.477], [0, 0], [56.477, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -515.552], [-104.668, -617.884], [104.668, -617.884], [207, -515.552], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 285, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-54.628, 0], [0, 0], [0, -54.629], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -54.629], [0, 0], [54.628, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -508.884], [-108.017, -607.866], [108.017, -607.866], [207, -508.884], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 286, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-53.079, 0], [0, 0], [0, -53.079], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -53.079], [0, 0], [53.079, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -503.293], [-110.826, -599.467], [110.826, -599.467], [207, -503.293], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 287, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-51.827, 0], [0, 0], [0, -51.827], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -51.827], [0, 0], [51.827, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -498.777], [-113.094, -592.683], [113.094, -592.683], [207, -498.777], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 288, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.868, 0], [0, 0], [0, -50.868], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.868], [0, 0], [50.868, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -495.319], [-114.831, -587.489], [114.831, -587.489], [207, -495.319], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 289, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-50.196, 0], [0, 0], [0, -50.196], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -50.196], [0, 0], [50.196, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -492.894], [-116.049, -583.845], [116.049, -583.845], [207, -492.894], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 290, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.8, 0], [0, 0], [0, -49.8], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -49.8], [0, 0], [49.8, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491.467], [-116.766, -581.701], [116.766, -581.701], [207, -491.467], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 291, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-49.671, 0], [0, 0], [0, -49.671], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -49.671], [0, 0], [49.671, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -491], [-117, -581], [117, -581], [207, -491], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 292, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.925, 0.753, 0.424, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.925, 0.753, 0.424, 1], "t": 320, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"k": [{"s": [0, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0], "t": 320, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 11986", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 321, "st": -40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": ".onSurfaceHome", "cl": "onSurfaceHome", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141, "s": [95]}, {"t": 170, "s": [0], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 252, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 253, "s": [95]}, {"t": 282, "s": [0], "h": 1}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 1376, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rectangle Independent Corners", "np": 19, "mn": "Pseudo/0.16410552199068107", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Align", "mn": "Pseudo/0.16410552199068107-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}, {"ty": 6, "nm": "Size", "mn": "Pseudo/0.16410552199068107-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "w", "mn": "Pseudo/0.16410552199068107-0003", "ix": 3, "v": {"a": 0, "k": 414, "ix": 3}}, {"ty": 0, "nm": "h", "mn": "Pseudo/0.16410552199068107-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.268]}, "t": 141, "s": [857]}, {"t": 179, "s": [1204], "h": 1}, {"t": 231, "s": [857], "h": 1}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.05], "y": [0.268]}, "t": 253, "s": [857]}, {"t": 291, "s": [1204], "h": 1}], "ix": 4}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Rounding", "mn": "Pseudo/0.16410552199068107-0006", "ix": 6, "v": 0}, {"ty": 0, "nm": "tl", "mn": "Pseudo/0.16410552199068107-0007", "ix": 7, "v": {"a": 1, "k": [{"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 141, "s": [200]}, {"t": 253, "s": [200]}], "ix": 7}}, {"ty": 0, "nm": "tr", "mn": "Pseudo/0.16410552199068107-0008", "ix": 8, "v": {"a": 1, "k": [{"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 141, "s": [200]}, {"t": 253, "s": [200]}], "ix": 8}}, {"ty": 0, "nm": "br", "mn": "Pseudo/0.16410552199068107-0009", "ix": 9, "v": {"a": 0, "k": 135, "ix": 9}}, {"ty": 0, "nm": "bl", "mn": "Pseudo/0.16410552199068107-0010", "ix": 10, "v": {"a": 0, "k": 135, "ix": 10}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Alignment", "mn": "Pseudo/0.16410552199068107-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "X Anchor %", "mn": "Pseudo/0.16410552199068107-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13}}, {"ty": 0, "nm": "Y Anchor %", "mn": "Pseudo/0.16410552199068107-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "X Position", "mn": "Pseudo/0.16410552199068107-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15}}, {"ty": 0, "nm": "Y Position", "mn": "Pseudo/0.16410552199068107-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 6, "nm": "Rectangle Independent Corners", "mn": "Pseudo/0.16410552199068107-0017", "ix": 17, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -657], [-7, -857], [7, -857], [207, -657], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 141, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.063], [-7, -910.063], [7, -910.063], [207, -710.063], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 142, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -753.007], [-7, -953.007], [7, -953.007], [207, -753.007], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 143, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -785.49], [-7, -985.49], [7, -985.49], [207, -785.49], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 144, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -811.374], [-7, -1011.374], [7, -1011.374], [207, -811.374], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 145, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -832.839], [-7, -1032.839], [7, -1032.839], [207, -832.839], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 146, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -851.125], [-7, -1051.125], [7, -1051.125], [207, -851.125], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 147, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -866.998], [-7, -1066.998], [7, -1066.998], [207, -866.998], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 148, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -880.965], [-7, -1080.965], [7, -1080.965], [207, -880.965], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 149, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -893.379], [-7, -1093.379], [7, -1093.379], [207, -893.379], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 150, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -904.497], [-7, -1104.497], [7, -1104.497], [207, -904.497], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 151, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -914.513], [-7, -1114.513], [7, -1114.513], [207, -914.513], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 152, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -923.578], [-7, -1123.578], [7, -1123.578], [207, -923.578], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 153, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -931.81], [-7, -1131.81], [7, -1131.81], [207, -931.81], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 154, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -939.307], [-7, -1139.307], [7, -1139.307], [207, -939.307], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -946.146], [-7, -1146.146], [7, -1146.146], [207, -946.146], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -952.394], [-7, -1152.394], [7, -1152.394], [207, -952.394], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -958.107], [-7, -1158.106], [7, -1158.106], [207, -958.107], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -963.331], [-7, -1163.331], [7, -1163.331], [207, -963.331], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -968.107], [-7, -1168.107], [7, -1168.107], [207, -968.107], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -972.472], [-7, -1172.472], [7, -1172.472], [207, -972.472], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -976.455], [-7, -1176.455], [7, -1176.455], [207, -976.455], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -980.085], [-7, -1180.085], [7, -1180.085], [207, -980.085], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -983.384], [-7, -1183.384], [7, -1183.384], [207, -983.384], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -986.375], [-7, -1186.375], [7, -1186.375], [207, -986.375], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 165, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -989.077], [-7, -1189.078], [7, -1189.078], [207, -989.077], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -991.508], [-7, -1191.508], [7, -1191.508], [207, -991.508], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -993.682], [-7, -1193.682], [7, -1193.682], [207, -993.682], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -995.614], [-7, -1195.614], [7, -1195.614], [207, -995.614], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -997.317], [-7, -1197.317], [7, -1197.317], [207, -997.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -998.803], [-7, -1198.803], [7, -1198.803], [207, -998.803], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1000.082], [-7, -1200.082], [7, -1200.082], [207, -1000.082], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1001.164], [-7, -1201.165], [7, -1201.165], [207, -1001.164], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1002.06], [-7, -1202.06], [7, -1202.06], [207, -1002.06], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 174, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1002.776], [-7, -1202.776], [7, -1202.776], [207, -1002.776], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 175, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1003.702], [-7, -1203.702], [7, -1203.702], [207, -1003.702], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 177, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1004], [-7, -1204], [7, -1204], [207, -1004], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 230, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -657], [-7, -857], [7, -857], [207, -657], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 231, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -657], [-7, -857], [7, -857], [207, -657], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 253, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -710.063], [-7, -910.063], [7, -910.063], [207, -710.063], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 254, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -753.007], [-7, -953.007], [7, -953.007], [207, -753.007], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 255, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -785.49], [-7, -985.49], [7, -985.49], [207, -785.49], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 256, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -811.374], [-7, -1011.374], [7, -1011.374], [207, -811.374], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 257, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -832.839], [-7, -1032.839], [7, -1032.839], [207, -832.839], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 258, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -851.125], [-7, -1051.125], [7, -1051.125], [207, -851.125], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 259, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -866.998], [-7, -1066.998], [7, -1066.998], [207, -866.998], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 260, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -880.965], [-7, -1080.965], [7, -1080.965], [207, -880.965], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 261, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -893.379], [-7, -1093.379], [7, -1093.379], [207, -893.379], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 262, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -904.497], [-7, -1104.497], [7, -1104.497], [207, -904.497], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 263, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -914.513], [-7, -1114.513], [7, -1114.513], [207, -914.513], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 264, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -923.578], [-7, -1123.578], [7, -1123.578], [207, -923.578], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 265, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.507]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.507], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -931.81], [-7, -1131.81], [7, -1131.81], [207, -931.81], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 266, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -939.307], [-7, -1139.307], [7, -1139.307], [207, -939.307], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 267, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -946.146], [-7, -1146.146], [7, -1146.146], [207, -946.146], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 268, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -952.394], [-7, -1152.394], [7, -1152.394], [207, -952.394], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 269, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -958.107], [-7, -1158.106], [7, -1158.106], [207, -958.107], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 270, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -963.331], [-7, -1163.331], [7, -1163.331], [207, -963.331], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 271, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -968.107], [-7, -1168.107], [7, -1168.107], [207, -968.107], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 272, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -972.472], [-7, -1172.472], [7, -1172.472], [207, -972.472], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 273, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -976.455], [-7, -1176.455], [7, -1176.455], [207, -976.455], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 274, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -980.085], [-7, -1180.085], [7, -1180.085], [207, -980.085], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 275, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -983.384], [-7, -1183.384], [7, -1183.384], [207, -983.384], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 276, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -986.375], [-7, -1186.375], [7, -1186.375], [207, -986.375], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 277, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -989.077], [-7, -1189.078], [7, -1189.078], [207, -989.077], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 278, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -991.508], [-7, -1191.508], [7, -1191.508], [207, -991.508], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 279, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -993.682], [-7, -1193.682], [7, -1193.682], [207, -993.682], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 280, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -995.614], [-7, -1195.614], [7, -1195.614], [207, -995.614], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 281, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -997.317], [-7, -1197.317], [7, -1197.317], [207, -997.317], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 282, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -998.803], [-7, -1198.803], [7, -1198.803], [207, -998.803], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 283, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1000.082], [-7, -1200.082], [7, -1200.082], [207, -1000.082], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 284, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1001.164], [-7, -1201.165], [7, -1201.165], [207, -1001.164], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 285, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1002.06], [-7, -1202.06], [7, -1202.06], [207, -1002.06], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 286, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1002.776], [-7, -1202.776], [7, -1202.776], [207, -1002.776], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 287, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-110.38, 0], [0, 0], [0, -110.38], [0, 0], [74.507, 0], [0, 0], [0, 74.506]], "o": [[0, -110.38], [0, 0], [110.38, 0], [0, 0], [0, 74.506], [0, 0], [-74.507, 0], [0, 0]], "v": [[-207, -1003.702], [-7, -1203.702], [7, -1203.702], [207, -1003.702], [207, -135], [72, 0], [-72, 0], [-207, -135]], "c": true}], "t": 289, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.925, 0.753, 0.424, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.925, 0.753, 0.424, 1], "t": 320, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"k": [{"s": [0, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [0, 0], "t": 320, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 11986", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 140, "op": 321, "st": -40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": ".surfaceHome", "cl": "surfaceHome", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 206, "ix": 3}, "y": {"a": 0, "k": 446, "ix": 4}}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [412, 892], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"k": [{"s": [0.365, 0.259, 0, 1], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.365, 0.259, 0, 1], "t": 320, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Volume", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 321, "st": -40, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Part03_Demonstration_Loop_V02", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 446, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [206, 446, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 412, "h": 892, "ip": 786, "op": 1040, "st": 786, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Part02_Charade_Loop_V02", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 446, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [206, 446, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 412, "h": 892, "ip": 320, "op": 786, "st": 320, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Part01_ThumbDemo_V02", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206, 446, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [206, 446, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 412, "h": 892, "ip": 0, "op": 321, "st": 0, "bm": 0}], "markers": [{"tm": 141, "cm": "", "dr": 0}, {"tm": 641, "cm": "", "dr": 0}, {"tm": 1002, "cm": "", "dr": 0}]}