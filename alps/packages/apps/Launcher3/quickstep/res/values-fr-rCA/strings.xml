<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin"><PERSON><PERSON><PERSON></string>
    <string name="recent_task_option_freeform">Forme libre</string>
    <string name="recent_task_option_desktop">Bureau</string>
    <string name="recents_empty_message">Aucun élément récent</string>
    <string name="accessibility_app_usage_settings">Paramètres d\'utilisation de l\'application</string>
    <string name="recents_clear_all">Tout effacer</string>
    <string name="accessibility_recent_apps">Applications récentes</string>
    <string name="task_view_closed">Tâche terminée</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minute</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> restant aujourd\'hui</string>
    <string name="title_app_suggestions">Suggestions d\'applications</string>
    <string name="all_apps_prediction_tip">Votre application prévue</string>
    <string name="hotseat_edu_title_migrate">Obtenez des suggestions d\'applications sur la rangée inférieure de votre écran d\'accueil</string>
    <string name="hotseat_edu_title_migrate_landscape">Obtenez des suggestions d\'applications sur la ligne des favoris de votre écran d\'accueil</string>
    <string name="hotseat_edu_message_migrate">Accédez facilement à vos applications les plus utilisées directement sur l\'écran d\'accueil. Les suggestions changeront en fonction de vos routines. Les applications de la rangée du bas se déplaceront vers votre écran d\'accueil.</string>
    <string name="hotseat_edu_message_migrate_landscape">Accédez facilement à vos applications les plus utilisées directement sur l\'écran d\'accueil. Les suggestions changeront en fonction de vos routines. Les applications de la ligne des favoris passeront à votre écran d\'accueil.</string>
    <string name="hotseat_edu_accept">Obtenez des suggestions d\'applications</string>
    <string name="hotseat_edu_dismiss">Non merci</string>
    <string name="hotseat_prediction_settings">Paramètres</string>
    <string name="hotseat_auto_enrolled">Les applications les plus utilisées apparaissent ici et changent en fonction des routines</string>
    <string name="hotseat_tip_no_empty_slots">Faites glisser les applications de la ligne du bas pour obtenir des suggestions d\'applications</string>
    <string name="hotseat_tip_gaps_filled">Suggestions d\'applications ajoutées à l\'espace vide</string>
    <string name="hotsaet_tip_prediction_enabled">Suggestions d\'applications activées</string>
    <string name="hotsaet_tip_prediction_disabled">Les suggestions d\'applications sont désactivées</string>
    <string name="hotseat_prediction_content_description">Application prévue : <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Faites pivoter votre appareil</string>
    <string name="gesture_tutorial_rotation_prompt">Faites pivoter votre appareil pour compléter le tutoriel de navigation gestuelle</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Veillez à faire glisser le curseur depuis l\'extrême droite ou l\'extrême gauche</string>
    <string name="back_gesture_feedback_cancelled">Veillez à glisser du bord droit ou gauche vers le milieu de l\'écran et à relâcher la pression</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Vous avez appris à glisser de la droite pour retourner en arrière. Ensuite, vous apprendrez à changer d\'application.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Vous avez effectué le geste de retour en arrière. Prochainement, apprenez à changer d\'application.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Vous avez accompli le geste de retour en arrière</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Veillez à ne pas balayer trop près du bas de l\'écran</string>
    <string name="back_gesture_tutorial_confirm_subtitle">Pour changer la sensibilité du geste de retour en arrière, allez dans Paramètres.</string>
    <string name="back_gesture_intro_title">Glissez pour revenir en arrière</string>
    <string name="back_gesture_intro_subtitle">Pour revenir au dernier écran, faites glisser votre doigt depuis le bord gauche ou droit vers le milieu de l\'écran.</string>
    <string name="back_gesture_spoken_intro_subtitle">Pour revenir au dernier écran, faites glisser deux doigts depuis le bord gauche ou droit vers le milieu de l\'écran.</string>
    <string name="back_gesture_tutorial_title">Retour</string>
    <string name="back_gesture_tutorial_subtitle">Balayer du bord gauche ou droit vers le milieu de l\'écran</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Veillez à balayer vers le haut à partir du bord inférieur de l\'écran</string>
    <string name="home_gesture_feedback_overview_detected">Assurez-vous de ne pas faire de pause avant de relâcher</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Veillez à effectuer un balayage droit vers le haut</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Vous avez terminé le geste de retour à la maison. La prochaine étape consiste à apprendre comment revenir en arrière.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Vous avez accompli le geste de retour à l\'accueil</string>
    <string name="home_gesture_intro_title">Glissez pour retourner à l\'accueil</string>
    <string name="home_gesture_intro_subtitle">Glissez vers le haut depuis le bas de l\'écran. Ce geste vous ramène toujours à l\'écran d\'accueil.</string>
    <string name="home_gesture_spoken_intro_subtitle">Balayez vers le haut avec 2 doigts depuis le bas de l\'écran. Ce geste vous ramène toujours à l\'écran d\'accueil.</string>
    <string name="home_gesture_tutorial_title">Rentrer à la maison</string>
    <string name="home_gesture_tutorial_subtitle">Balayez vers le haut depuis le bas de votre écran</string>
    <string name="home_gesture_tutorial_success">Bon travail!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Veillez à balayer vers le haut à partir du bord inférieur de l\'écran</string>
    <string name="overview_gesture_feedback_home_detected">Essayez de maintenir la fenêtre plus longtemps avant de la relâcher</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Veillez à effectuer un balayage droit vers le haut, puis faites une pause</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Vous avez appris à utiliser les gestes. Pour désactiver les gestes, allez dans Paramètres.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Vous avez accompli le geste de changer d\'application</string>
    <string name="overview_gesture_intro_title">Glissez pour changer d\'application</string>
    <string name="overview_gesture_intro_subtitle">Pour passer d\'une application à l\'autre, faites glisser votre doigt vers le haut depuis le bas de l\'écran et maintenez cette position, puis relâchez.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Pour passer d\'une application à l\'autre, faites glisser 2 doigts vers le haut depuis le bas de l\'écran et maintenez cette position, puis relâchez.</string>
    <string name="overview_gesture_tutorial_title">Changer d’application</string>
    <string name="overview_gesture_tutorial_subtitle">Balayez vers le haut depuis le bas de l\'écran, maintenez la touche enfoncée, puis relâchez-la.</string>
    <string name="overview_gesture_tutorial_success">Bien joué!</string>
    <string name="gesture_tutorial_confirm_title">Tous ensemble</string>
    <string name="gesture_tutorial_action_button_label">Terminé</string>
    <string name="gesture_tutorial_action_button_label_settings">Paramètres</string>
    <string name="gesture_tutorial_try_again">Réessayer</string>
    <string name="gesture_tutorial_nice">C\'est bien!</string>
    <string name="gesture_tutorial_step">Tutoriel <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Tout est prêt!</string>
    <string name="allset_hint">Balayez vers le haut pour aller à la maison</string>
    <string name="allset_button_hint">Touchez le bouton Accueil pour accéder à l\'écran d\'accueil</string>
    <string name="allset_description_generic">Vous êtes prêt à utiliser votre <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Appareil</string>
    <string name="allset_navigation_settings"><annotation id="link">Paramètres de navigation du système</annotation></string>
    <string name="action_share">Partager</string>
    <string name="action_screenshot">Capture d\'écran</string>
    <string name="action_split">Partager</string>
    <string name="action_save_app_pair">Sauvegarder la paire d’applications</string>
    <string name="toast_split_select_app">Touchez une autre application pour utiliser l’écran partagé</string>
    <string name="toast_contextual_split_select_app">Choisir une autre application pour utiliser l’écran partagé</string>
    <string name="toast_split_select_app_cancel"><b>Annuler</b></string>
    <string name="toast_split_select_cont_desc">Quitter la sélection de l\'écran partagé</string>
    <string name="toast_split_app_unsupported">Choisir une autre application pour utiliser l’écran partagé</string>
    <string name="blocked_by_policy">Cette action n\'est pas autorisée par l\'application ou votre organisation</string>
    <string name="split_widgets_not_supported">Les widgets ne sont pas pris en charge actuellement. Sélectionnez une autre application.</string>
    <string name="skip_tutorial_dialog_title">Ignorer le tutoriel de navigation?</string>
    <string name="skip_tutorial_dialog_subtitle">Vous pouvez le trouver plus tard dans l\'application <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">Annuler</string>
    <string name="gesture_tutorial_action_button_label_skip">Ignorer</string>
    <string name="accessibility_rotate_button">Faire pivoter l\'écran</string>
    <string name="taskbar_edu_a11y_title">Éducation dans la barre des tâches</string>
    <string name="taskbar_edu_splitscreen">Faites glisser une application sur le côté pour utiliser deux applications à la fois</string>
    <string name="taskbar_edu_stashing">Glissez lentement vers le haut pour afficher la barre des tâches</string>
    <string name="taskbar_edu_suggestions">Obtenir des suggestions d\'applications en fonction de vos habitudes</string>
    <string name="taskbar_edu_pinning">Appuyez longuement sur le séparateur pour épingler la barre des tâches</string>
    <string name="taskbar_edu_features">En faire plus avec la barre des tâches</string>
    <string name="taskbar_edu_pinning_title">Toujours afficher la barre des tâches</string>
    <string name="taskbar_edu_pinning_standalone">Pour toujours afficher la barre des tâches en bas de l\'écran, appuyez longuement sur le séparateur.</string>
    <string name="taskbar_search_edu_title">Appuyez longuement sur la touche d\'action pour rechercher ce qui se trouve sur votre écran</string>
    <string name="taskbar_edu_search_disclosure">Ce produit utilise la partie sélectionnée de votre écran pour effectuer des recherches. Les <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Politique de confidentialité <xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> et les <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Conditions d\'utilisation<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> de Google s\'appliquent.</string>
    <string name="taskbar_edu_close">Fermer</string>
    <string name="taskbar_edu_done">Terminé</string>
    <string name="taskbar_button_home">Domicile</string>
    <string name="taskbar_button_a11y">Accessibilité</string>
    <string name="taskbar_button_back">Précédent</string>
    <string name="taskbar_button_ime_switcher">Sélecteur IME</string>
    <string name="taskbar_button_recents">Récent</string>
    <string name="taskbar_button_notifications">Notifications</string>
    <string name="taskbar_button_quick_settings">Paramètres rapides</string>
    <string name="taskbar_a11y_title">Barre des tâches</string>
    <string name="taskbar_a11y_shown_title">Barre des tâches affichée</string>
    <string name="taskbar_a11y_hidden_title">Barre des tâches cachée</string>
    <string name="taskbar_phone_a11y_title">Barre de navigation</string>
    <string name="always_show_taskbar">Toujours afficher la barre des tâches</string>
    <string name="change_navigation_mode">Modifier le mode de navigation</string>
    <string name="taskbar_divider_a11y_title">Séparateur de la barre des tâches</string>
    <string name="move_drop_target_top_or_left">Déplacez en haut/à gauche</string>
    <string name="move_drop_target_bottom_or_right">Déplacer en bas/à droite</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Afficher # autre appli.}one{Afficher # autre appli.}other{Afficher # autres applis.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Afficher # appli de bureau.}one{Afficher # appli de bureau.}other{Afficher # applis de bureau.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> et <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Bulle</string>
    <string name="bubble_bar_overflow_description">Débordement</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> de <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> et <xliff:g id="bubble_count" example="4">%2$d</xliff:g> supplémentaires</string>
    <string name="app_name">Launcher3</string>
</resources>