<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <string name="overscroll_plugin_factory_class" translatable="false" />
    <string name="task_overlay_factory_class" translatable="false"/>

    <string-array name="back_gesture_blocking_activities" translatable="false">
        <item>com.android.launcher3/com.android.quickstep.interaction.GestureSandboxActivity</item>
    </string-array>

    <string name="stats_log_manager_class" translatable="false">com.android.quickstep.logging.StatsLogCompatManager</string>
    <string name="test_information_handler_class" translatable="false">com.android.quickstep.QuickstepTestInformationHandler</string>
    <string name="window_manager_proxy_class" translatable="false">com.android.quickstep.util.SystemWindowManagerProxy</string>
    <string name="widget_holder_factory_class" translatable="false">com.android.launcher3.uioverrides.QuickstepWidgetHolder$QuickstepHolderFactory</string>
    <string name="instant_app_resolver_class" translatable="false">com.android.quickstep.InstantAppResolverImpl</string>
    <string name="app_launch_tracker_class" translatable="false">com.android.launcher3.appprediction.PredictionAppTracker</string>
    <string name="main_process_initializer_class" translatable="false">com.android.quickstep.QuickstepProcessInitializer</string>
    <string name="model_delegate_class" translatable="false">com.android.launcher3.model.QuickstepModelDelegate</string>
    <string name="secondary_display_predictions_class" translatable="false">com.android.launcher3.secondarydisplay.SecondaryDisplayPredictionsImpl</string>
    <string name="taskbar_model_callbacks_factory_class" translatable="false">com.android.launcher3.taskbar.TaskbarModelCallbacksFactory</string>
    <string name="taskbar_view_callbacks_factory_class" translatable="false">com.android.launcher3.taskbar.TaskbarViewCallbacksFactory</string>
    <string name="launcher_restore_event_logger_class" translatable="false">com.android.quickstep.LauncherRestoreEventLoggerImpl</string>
    <string name="plugin_manager_wrapper_class" translatable="false">com.android.launcher3.uioverrides.plugins.PluginManagerWrapperImpl</string>
    <string name="taskbar_edu_tooltip_controller_class" translatable="false">com.android.launcher3.taskbar.TaskbarEduTooltipController</string>

    <string name="nav_handle_long_press_handler_class" translatable="false"></string>
    <string name="assist_utils_class" translatable="false"></string>
    <string name="assist_state_manager_class" translatable="false"></string>
    <string name="api_wrapper_class" translatable="false">com.android.launcher3.uioverrides.SystemApiWrapper</string>

    <!-- The number of thumbnails and icons to keep in the cache. The thumbnail cache size also
         determines how many thumbnails will be fetched in the background. -->
    <integer name="recentsThumbnailCacheSize">3</integer>
    <integer name="recentsIconCacheSize">12</integer>
    <integer name="recentsScrollHapticMinGapMillis">20</integer>

    <!-- Assistant Gesture -->
    <integer name="assistant_gesture_min_time_threshold">200</integer>
    <integer name="assistant_gesture_corner_deg_threshold">20</integer>

    <string name="wellbeing_provider_pkg" translatable="false"/>

    <integer name="max_depth_blur_radius">23</integer>

    <!-- Accessibility actions -->
    <item type="id" name="action_move_to_top_or_left" />
    <item type="id" name="action_move_to_bottom_or_right" />

    <!-- The max scale for the wallpaper when it's zoomed in -->
    <item name="config_wallpaperMaxScale" format="float" type="dimen">
        @*android:dimen/config_wallpaperMaxScale
    </item>

    <string name="setup_wizard_pkg" translatable="false" />

    <!-- This is a float because it is converted to dp later in DeviceProfile -->
    <item name="taskbar_icon_size" type="dimen" format="float">44</item>
</resources>
