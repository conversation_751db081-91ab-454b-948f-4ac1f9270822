<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android">

    <style name="TextAppearance.GestureTutorial"
        parent="@android:style/Theme.DeviceDefault.DayNight" />

    <style name="TextAppearance.GestureTutorial.CallToAction"
        parent="@android:style/Theme.DeviceDefault.DayNight" />

    <style name="TextAppearance.GestureTutorial.Title"
        parent="TextAppearance.GestureTutorial">
        <item name="android:gravity">center</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">28sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Subtitle"
        parent="TextAppearance.GestureTutorial">
        <item name="android:gravity">center</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:textSize">21sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Feedback.Title"
        parent="TextAppearance.GestureTutorial">
        <item name="android:gravity">start</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">google-sans</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:textSize">36sp</item>
        <item name="android:lineHeight">44sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MenuButton"
        parent="TextAppearance.GestureTutorial.Feedback.Title">
        <item name="android:gravity">center</item>
        <item name="fontWeight">400</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MenuButton.Home"
        parent="TextAppearance.GestureTutorial.MenuButton">
        <item name="android:textColor">?attr/secondaryHome</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MenuButton.Back"
        parent="TextAppearance.GestureTutorial.MenuButton">
        <item name="android:textColor">?attr/secondaryBack</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MenuButton.Overview"
        parent="TextAppearance.GestureTutorial.MenuButton">
        <item name="android:textColor">?attr/secondaryOverview</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Feedback.Title.AllSet"
        parent="TextAppearance.GestureTutorial.Feedback.Title">
        <item name="android:letterSpacing">0.03</item>
        <item name="android:lineHeight">44sp</item>
        <item name="android:textSize">@dimen/allset_page_allset_text_size</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Dialog.Title"
        parent="TextAppearance.GestureTutorial.Feedback.Title">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:fontFamily">google-sans</item>
        <item name="android:lineHeight">32sp</item>
        <item name="android:textSize">24sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Feedback.Subtitle"
        parent="TextAppearance.GestureTutorial">
        <item name="android:gravity">start</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">google-sans-text</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineHeight">20sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Feedback.Subtitle.AllSet"
        parent="TextAppearance.GestureTutorial.Feedback.Subtitle">
        <item name="android:textSize">18sp</item>
        <item name="android:lineHeight">24sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Dialog.Subtitle"
        parent="TextAppearance.GestureTutorial.Feedback.Subtitle">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:fontFamily">google-sans-text</item>
        <item name="android:letterSpacing">0.025</item>
        <item name="android:lineHeight">20sp</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Feedback.Subtext"
        parent="TextAppearance.GestureTutorial.Feedback.Subtitle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/colorAccent</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="TextAppearance.GestureTutorial.Feedback.Subtext.Dark"
        parent="TextAppearance.GestureTutorial.Feedback.Subtext">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="TextAppearance.GestureTutorial.ButtonLabel"
        parent="TextAppearance.GestureTutorial.CallToAction">
        <item name="android:gravity">center</item>
        <item name="android:textColor">?androidprv:attr/materialColorOnPrimary</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">google-sans-text-medium</item>
    </style>

    <style name="TextAppearance.GestureTutorial.ButtonLabel.Home"
        parent="TextAppearance.GestureTutorial.ButtonLabel">
        <item name="android:textColor">?attr/onSurfaceHome</item>
    </style>

    <style name="TextAppearance.GestureTutorial.ButtonLabel.Back"
        parent="TextAppearance.GestureTutorial.ButtonLabel">
        <item name="android:textColor">?attr/onSurfaceBack</item>
    </style>

    <style name="TextAppearance.GestureTutorial.ButtonLabel.Overview"
        parent="TextAppearance.GestureTutorial.ButtonLabel">
        <item name="android:textColor">?attr/onSurfaceOverview</item>
    </style>

    <style name="TextAppearance.GestureTutorial.CancelButtonLabel"
        parent="TextAppearance.GestureTutorial.ButtonLabel">
        <item name="android:textColor">?android:attr/colorAccent</item>
    </style>

    <style name="TextAppearance.GestureTutorial.TextButtonLabel"
        parent="TextAppearance.GestureTutorial.ButtonLabel">
        <item name="android:textColor">?android:attr/colorAccent</item>
    </style>

    <style name="TextAppearance.GestureTutorial.LinkText"
        parent="TextAppearance.GestureTutorial.Feedback.Subtitle">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle"
        parent="TextAppearance.GestureTutorial">
        <item name="android:textSize">36sp</item>
        <item name="android:fontFamily">google-sans</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle.Home"
        parent="TextAppearance.GestureTutorial.MainTitle">
        <item name="android:textColor">?attr/secondaryHome</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle.Back"
        parent="TextAppearance.GestureTutorial.MainTitle">
        <item name="android:textColor">?attr/secondaryBack</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle.Overview"
        parent="TextAppearance.GestureTutorial.MainTitle">
        <item name="android:textColor">?attr/secondaryOverview</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle.Success.Home"
        parent="TextAppearance.GestureTutorial.MainTitle.Home">
        <item name="android:textColor">?attr/secondaryHome</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle.Success.Back"
        parent="TextAppearance.GestureTutorial.MainTitle.Back">
        <item name="android:textColor">?attr/secondaryBack</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainTitle.Success.Overview"
        parent="TextAppearance.GestureTutorial.MainTitle.Overview">
        <item name="android:textColor">?attr/secondaryOverview</item>
    </style>

    <style name="TextAppearance.GestureTutorial.MainSubtitle"
        parent="TextAppearance.GestureTutorial.Subtitle">
        <item name="android:textSize">16sp</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="android:fontFamily">google-sans-text</item>
        <item name="android:textColor">?attr/tutorialSubtitle</item>
    </style>

    <style name="AllSetTheme" parent="@android:style/Theme.DeviceDefault.Light.NoActionBar">
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!--
      Can be applied to views to color things like ripples and list highlights the workspace text
      color.
    -->
    <style name="ThemeControlHighlightWorkspaceColor">
        <item name="android:colorControlHighlight">?attr/workspaceTextColor</item>
    </style>

    <style name="OverviewActionButton"
        parent="@android:style/Widget.DeviceDefault.Button.Borderless">
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:textColor">@color/overview_button</item>
        <item name="android:drawableTint">@color/overview_button</item>
        <item name="android:tint">?android:attr/textColorPrimary</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="OverviewClearAllButton" parent="@android:style/Widget.DeviceDefault.Button">
        <item name="android:background">@drawable/bg_overview_clear_all_button</item>
        <item name="android:minWidth">96dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- Icon displayed on the taskbar -->
    <style name="BaseIcon.Workspace.Taskbar" >
        <item name="iconDisplay">taskbar</item>
    </style>

    <style name="TaskbarEdu.Button.Done" parent="@android:style/Widget.Material.Button">
        <item name="android:background">@drawable/button_taskbar_edu_colored</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">36dp</item>
    </style>

    <style name="TextAppearance.TaskbarEduTooltip.Title" parent="@android:style/TextAppearance.DeviceDefault.DialogWindowTitle">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:fontFamily">google-sans</item>
        <item name="android:textSize">24sp</item>
    </style>

    <style name="TextAppearance.TaskbarEduTooltip.Subtext" parent="android:TextAppearance.Material.Body1">
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:fontFamily">google-sans-text</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="KeyboardQuickSwitchText">
        <item name="fontFamily">google-sans-text</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?androidprv:attr/materialColorOnSurface</item>
        <item name="lineHeight">20sp</item>
    </style>

    <style name="KeyboardQuickSwitchText.OnBackground" parent="KeyboardQuickSwitchText">
        <item name="android:textColor">?androidprv:attr/materialColorOnSurface</item>
    </style>

    <style name="GestureTutorialActivity" parent="@style/AppTheme">
        <item name="background">@android:color/transparent</item>
        <item name="tutorialSubtitle">@android:color/black</item>
        <item name="surfaceContainer">?androidprv:attr/materialColorSurfaceContainer</item>
        <item name="onSurfaceHome">?androidprv:attr/materialColorPrimaryFixed</item>
        <item name="surfaceHome">@android:color/system_accent1_300</item>
        <item name="secondaryHome">?androidprv:attr/materialColorOnPrimaryFixedVariant</item>
        <item name="onSurfaceBack">?androidprv:attr/materialColorTertiaryFixed</item>
        <item name="surfaceBack">@android:color/system_accent3_300</item>
        <item name="secondaryBack">?androidprv:attr/materialColorOnTertiaryFixedVariant</item>
        <item name="onSurfaceOverview">?androidprv:attr/materialColorPrimaryFixed</item>
        <item name="surfaceOverview">@android:color/system_accent2_300</item>
        <item name="secondaryOverview">?androidprv:attr/materialColorOnSecondaryFixedVariant</item>
    </style>

    <style name="rotate_prompt_title" parent="TextAppearance.GestureTutorial.Dialog.Title">
        <item name="android:textColor">?androidprv:attr/materialColorOnSurface</item>
    </style>

    <style name="rotate_prompt_subtitle" parent="TextAppearance.GestureTutorial.Dialog.Subtitle">
        <item name="android:textColor">?androidprv:attr/materialColorOnSurfaceVariant</item>
    </style>

    <style name="ArrowTipTaskbarStyle">
        <item name="arrowTipBackground">?androidprv:attr/materialColorSurfaceContainer</item>
        <item name="arrowTipTextColor">?androidprv:attr/materialColorOnSurface</item>
    </style>

    <style name="IconAppChipMenuTextStyle">
        <item name="android:fontFamily">google-sans-text-medium</item>
        <item name="android:textSize">@dimen/task_thumbnail_icon_menu_text_size</item>
        <item name="android:textColor">?androidprv:attr/materialColorOnSurface</item>
        <item name="android:letterSpacing">0.025</item>
        <item name="android:lineHeight">20sp</item>
    </style>

    <style name="WidgetPickerActivityTheme" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="widgetsTheme">@style/WidgetContainerTheme</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="pageIndicatorDotColor">@color/page_indicator_dot_color_light</item>
    </style>
</resources>
