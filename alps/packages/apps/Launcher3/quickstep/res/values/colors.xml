<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources xmlns:androidprv="http://schemas.android.com/apk/prv/res/android">

    <color name="chip_hint_foreground_color">#fff</color>
    <color name="chip_scrim_start_color">#39000000</color>

    <color name="all_apps_label_text">#61000000</color>
    <color name="all_apps_label_text_dark">#61FFFFFF</color>
    <color name="all_apps_prediction_row_separator">#3c000000</color>
    <color name="all_apps_prediction_row_separator_dark">#3cffffff</color>

    <!-- Taskbar -->
    <color name="taskbar_nav_icon_selection_ripple">#E0E0E0</color>
    <color name="taskbar_nav_icon_light_color_on_home">#ffffff</color>
    <!-- The dark navigation button color is only used in the rare cases that taskbar isn't drawing
    its background and the underlying app has requested dark buttons. -->
    <color name="taskbar_nav_icon_dark_color_on_home">#99000000</color>
    <color name="taskbar_stashed_handle_light_color">#EBffffff</color>
    <color name="taskbar_stashed_handle_dark_color">#99000000</color>
    <color name="taskbar_running_app_indicator_color">#646464</color>

    <!-- Floating rotation button -->
    <color name="floating_rotation_button_light_color">#ffffff</color>
    <color name="floating_rotation_button_dark_color">#99000000</color>

    <!-- Gesture navigation tutorial -->
    <color name="gesture_tutorial_back_arrow_color">#FFFFFFFF</color>

    <color name="gesture_tutorial_fake_wallpaper_color">#f9f9f9</color> <!-- White -->
    <color name="gesture_tutorial_ripple_color">#A0C2F9</color> <!-- Light Blue -->
    <color name="gesture_tutorial_fake_task_view_color">#6DA1FF</color> <!-- Light Blue -->
    <!-- Must contrast gesture_tutorial_fake_wallpaper_color -->
    <color name="gesture_tutorial_fake_previous_task_view_color">#3C4043</color> <!-- Gray -->
    <color name="gesture_tutorial_taskbar_color">#E8EAED</color>

    <!-- Mock hotseat -->
    <color name="mock_app_icon">#BDC1C6</color>
    <color name="mock_search_bar">#3C4043</color>

    <!-- Mock conversation -->
    <color name="mock_conversation_background">#f1f3f4</color>
    <color name="mock_conversation_top_bar">#e8eaed</color>
    <color name="mock_conversation_top_bar_item">#dadce0</color>
    <color name="mock_conversation_sent_message">#bdc1c6</color>
    <color name="mock_conversation_received_message">#e8eaed</color>
    <color name="mock_conversation_message_input">#dadce0</color>
    <color name="mock_conversation_profile_icon">#dadce0</color>

    <!-- Mock conversations list -->
    <color name="mock_list_background">#dadce0</color>
    <color name="mock_list_top_bar">#e8eaed</color>
    <color name="mock_list_top_bar_item">#f8f9fa</color>
    <color name="mock_list_profile_icon">#9aa0a6</color>
    <color name="mock_list_preview_message">#bdc1c6</color>
    <color name="mock_list_button">#bdc1c6</color>

    <!-- Mock web page -->
    <color name="mock_webpage_background">#f1f3f4</color>
    <color name="mock_webpage_url_bar">#6e7175</color>
    <color name="mock_webpage_url_bar_item">#9a9a9a</color>
    <color name="mock_webpage_top_bar">#e8eaed</color>
    <color name="mock_webpage_top_bar_item">#80868b</color>
    <color name="mock_webpage_page_text">#bdc1c6</color>

    <color name="all_set_page_background">#FFFFFFFF</color>

    <!-- Recents overview -->
    <color name="recents_filter_icon">#333333</color>

    <!-- Lottie light theme colors. -->
    <color name="lottie_blue400">#669df6</color>
    <color name="lottie_blue600">#1a73e8</color>
    <color name="lottie_green400">#5bb974</color>
    <color name="lottie_green600">#1e8e3e</color>
    <color name="lottie_grey200">#e8eaed</color>
    <color name="lottie_grey600">#80868b</color>
    <color name="lottie_grey700">#5f6368</color>
    <color name="lottie_red600">#d93025</color>
    <color name="lottie_yellow400">#fcc934</color>
    <color name="lottie_yellow600">#f9ab00</color>

    <!-- Turn on work apps button -->
    <color name="work_turn_on_stroke">?androidprv:attr/colorAccentPrimaryVariant</color>
    <color name="work_fab_bg_color">?androidprv:attr/materialColorPrimaryFixedDim</color>
    <color name="work_fab_icon_color">?androidprv:attr/materialColorOnPrimaryFixed</color>
</resources>