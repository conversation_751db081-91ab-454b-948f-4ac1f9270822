<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <declare-styleable name="AllSetLinkSpan">
        <attr name="android:textSize"/>
        <attr name="android:fontFamily"/>
    </declare-styleable>

    <!--
         TaskView specific attributes. These attributes are used to customize a TaskView view in
         XML files.
     -->
    <declare-styleable name="TaskView">
        <!-- Border color for a keyboard quick switch task views -->
        <attr name="focusBorderColor" format="color" />
        <attr name="hoverBorderColor" format="color" />
    </declare-styleable>

    <declare-styleable name="ClearAllButton">
        <!-- focus border color for overview clear all button views -->
        <attr name="focusBorderColor" />
    </declare-styleable>

    <!--
         Gesture nav edu specific attributes. These attributes are used to customize Gesture nav edu
         view lottie animation colors in XML files.
     -->
    <declare-styleable name="RootSandboxLayout">
        <attr name="tutorialSubtitle" format="color" />
        <attr name="surfaceContainer" format="color" />
        <attr name="onSurfaceHome" format="color" />
        <attr name="surfaceHome" format="color" />
        <attr name="secondaryHome" format="color" />
        <attr name="onSurfaceBack" format="color" />
        <attr name="surfaceBack" format="color" />
        <attr name="secondaryBack" format="color" />
        <attr name="onSurfaceOverview" format="color" />
        <attr name="surfaceOverview" format="color" />
        <attr name="secondaryOverview" format="color" />
    </declare-styleable>
</resources>
