<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources xmlns:androidprv="http://schemas.android.com/apk/prv/res/android">

    <color name="gesture_tutorial_back_arrow_color">#99000000</color>

    <color name="gesture_tutorial_fake_wallpaper_color">#000000</color> <!-- Black -->

    <color name="mock_webpage_url_bar">#202124</color>
    <color name="mock_webpage_url_bar_item">#3c4043</color>

    <color name="all_set_page_background">#FF000000</color>

    <!-- Turn on work apps button -->
    <color name="work_turn_on_stroke">?androidprv:attr/colorAccentSecondaryVariant</color>
    <color name="work_fab_bg_color">?androidprv:attr/materialColorPrimaryFixedDim</color>
    <color name="work_fab_icon_color">?androidprv:attr/materialColorOnPrimaryFixed</color>
</resources>