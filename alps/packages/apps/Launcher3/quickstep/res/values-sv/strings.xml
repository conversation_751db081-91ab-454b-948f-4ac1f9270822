<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Nåla fast</string>
    <string name="recent_task_option_freeform">Friform</string>
    <string name="recent_task_option_desktop">Skrivbord</string>
    <string name="recents_empty_message">Inga senast använda objekt</string>
    <string name="accessibility_app_usage_settings">Applikation användningsinställningar</string>
    <string name="recents_clear_all">Ta bort alla</string>
    <string name="accessibility_recent_apps">Senaste appar</string>
    <string name="task_view_closed">Uppdrag stängt</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minut</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> antal kvar idag</string>
    <string name="title_app_suggestions">Appförslag</string>
    <string name="all_apps_prediction_tip">Dina förutsedda appar</string>
    <string name="hotseat_edu_title_migrate">Få appförslag på den nedre raden på startskärmen</string>
    <string name="hotseat_edu_title_migrate_landscape">Få appförslag på favoritraden på startskärmen</string>
    <string name="hotseat_edu_message_migrate">Få enkelt åtkomst till dina mest använda appar direkt på startskärmen. Förslagen kommer att ändras baserat på dina rutiner. Appar på den nedre raden flyttas upp till din startskärm.</string>
    <string name="hotseat_edu_message_migrate_landscape">Få enkelt åtkomst till dina mest använda appar direkt på startskärmen. Förslagen kommer att ändras baserat på dina rutiner. Appar i favoritraden flyttas till din startskärm.</string>
    <string name="hotseat_edu_accept">Få appförslag</string>
    <string name="hotseat_edu_dismiss">Nej, tack</string>
    <string name="hotseat_prediction_settings">Inställningar</string>
    <string name="hotseat_auto_enrolled">Mest använda appar visas här och ändras utifrån rutiner</string>
    <string name="hotseat_tip_no_empty_slots">Dra appar från nedersta linjen för att få app-rekommendationer</string>
    <string name="hotseat_tip_gaps_filled">Appförslag har lagts till i tomt utrymme</string>
    <string name="hotsaet_tip_prediction_enabled">Appförslag har aktiverats</string>
    <string name="hotsaet_tip_prediction_disabled">App-rekommendationer är avaktiverade</string>
    <string name="hotseat_prediction_content_description">Förutspådd app: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Rotera din enhet</string>
    <string name="gesture_tutorial_rotation_prompt">Rotera enheten för att slutföra rörelsenavigeringsvägledningen</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Se till att du sveper från den högra eller vänstra kanten</string>
    <string name="back_gesture_feedback_cancelled">Se till att du sveper från höger eller vänster kant till mitten av skärmen och släpper</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Du lärde dig hur du sveper från höger för att gå tillbaka. Nästa steg är att lära dig hur du byter app.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Du är klar med vägledningen för gå tillbaka-rörelsen. Nästa steg är att lära dig hur du byter app.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Du är klar med vägledningen för bakåt-rörelsen</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Se till att du inte sveper för nära botten av skärmen</string>
    <string name="back_gesture_tutorial_confirm_subtitle">För att ändra känsligheten för bakåtgesten, gå till Inställningar.</string>
    <string name="back_gesture_intro_title">Svep för att gå tillbaka</string>
    <string name="back_gesture_intro_subtitle">För att gå tillbaka till den sista skärmen sveper du från vänster eller höger kant till mitten av skärmen.</string>
    <string name="back_gesture_spoken_intro_subtitle">För att gå tillbaka till föregående skärm, svep med 2 fingrar från vänster eller höger kant till mitten av skärmen.</string>
    <string name="back_gesture_tutorial_title">Gå tillbaka</string>
    <string name="back_gesture_tutorial_subtitle">Svep från vänster eller höger sida till skärmens mitt</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Se till att du sveper uppåt från skärmens nedre kant</string>
    <string name="home_gesture_feedback_overview_detected">Se till att du inte pausar innan du släpper taget</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Se till att du sveper rakt upp</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Du är klar med vägledningen för Hem-rörelsen. Nästkommande: Lär dig hur du går bakåt.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Du är klar med vägledningen för Hem-rörelsen</string>
    <string name="home_gesture_intro_title">Svep för att gå till startsidan</string>
    <string name="home_gesture_intro_subtitle">Svep uppåt från botten av skärmen. Den här gesten tar dig alltid till startskärmen.</string>
    <string name="home_gesture_spoken_intro_subtitle">Svep upp med 2 fingrar från botten av skärmen. Denna rörelse tar dig alltid till hemskärmen.</string>
    <string name="home_gesture_tutorial_title">Gå hem</string>
    <string name="home_gesture_tutorial_subtitle">Dra upp från botten av skärmen</string>
    <string name="home_gesture_tutorial_success">Bra jobbat!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Se till att du sveper uppåt från skärmens nedre kant</string>
    <string name="overview_gesture_feedback_home_detected">Försök att hålla fönstret längre innan du släpper med fingret</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Se till att du sveper rakt upp, stanna sen</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Du lärde dig hur man använder gester. För att stänga av gester, gå till Inställningar.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Du är klar med rörelsen byt appar</string>
    <string name="overview_gesture_intro_title">Svep för att växla appar</string>
    <string name="overview_gesture_intro_subtitle">Om du vill växla mellan appar sveper du uppåt från botten av skärmen, håller ned och släpper sedan.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Om du vill växla mellan appar sveper du uppåt med två fingrar från botten av skärmen, håller ned och släpper sedan.</string>
    <string name="overview_gesture_tutorial_title">Byt appar</string>
    <string name="overview_gesture_tutorial_subtitle">Dra upp från botten av skärmen, håll, sedan släpper du.</string>
    <string name="overview_gesture_tutorial_success">Bra gjort!</string>
    <string name="gesture_tutorial_confirm_title">Klar</string>
    <string name="gesture_tutorial_action_button_label">Färdig</string>
    <string name="gesture_tutorial_action_button_label_settings">Inställningar</string>
    <string name="gesture_tutorial_try_again">Försök igen</string>
    <string name="gesture_tutorial_nice">Bra!</string>
    <string name="gesture_tutorial_step">Handledning <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Klar!</string>
    <string name="allset_hint">Dra upp för att gå Hem</string>
    <string name="allset_button_hint">Tryck på Hem-knappen för att gå tillbaka till hemskärmen.</string>
    <string name="allset_description_generic">Du är klar att börja använda din <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Enhet</string>
    <string name="allset_navigation_settings"><annotation id="link">Inställningar för systemnavigering</annotation></string>
    <string name="action_share">Dela</string>
    <string name="action_screenshot">Skärmbild</string>
    <string name="action_split">Delning</string>
    <string name="action_save_app_pair">Spara app-par</string>
    <string name="toast_split_select_app">Tryck på en annan app för att använda delad skärm</string>
    <string name="toast_contextual_split_select_app">Välj en annan app för att använda delad skärm</string>
    <string name="toast_split_select_app_cancel"><b>Avbryt</b></string>
    <string name="toast_split_select_cont_desc">Avsluta val av delad skärm</string>
    <string name="toast_split_app_unsupported">Välj en annan app för att använda delad skärm</string>
    <string name="blocked_by_policy">Denna åtgärd tillåts inte av appen eller din organisation.</string>
    <string name="split_widgets_not_supported">Widgetar stöds inte för närvarande. Välj en annan app.</string>
    <string name="skip_tutorial_dialog_title">Hoppa över navigeringsguiden?</string>
    <string name="skip_tutorial_dialog_subtitle">Du hittar detta senare i <xliff:g id="name">%1$s</xliff:g> appen</string>
    <string name="gesture_tutorial_action_button_label_cancel">Avbryt</string>
    <string name="gesture_tutorial_action_button_label_skip">Hoppa över</string>
    <string name="accessibility_rotate_button">Rotera skärmen</string>
    <string name="taskbar_edu_a11y_title">Vägledning om aktivitetsfältet</string>
    <string name="taskbar_edu_splitscreen">Dra en app åt sidan för att använda 2 appar samtidigt</string>
    <string name="taskbar_edu_stashing">Långsamt svep uppåt för att visa aktivitetsfältet</string>
    <string name="taskbar_edu_suggestions">Få app-rekommendationer baserat på dina rutiner</string>
    <string name="taskbar_edu_pinning">Långt tryck på avdelaren för att fästa aktivitetsfältet</string>
    <string name="taskbar_edu_features">Gör mer med aktivitetsfältet</string>
    <string name="taskbar_edu_pinning_title">Visa alltid aktivitetsfältet</string>
    <string name="taskbar_edu_pinning_standalone">För att alltid visa aktivitetsfältet längst ner på skärmen, tryck länge på avdelaren.</string>
    <string name="taskbar_search_edu_title">Långt tryck på åtgärdsknappen för att söka vad som finns på skärmen</string>
    <string name="taskbar_edu_search_disclosure">Den här produkten använder den valda delen av din skärm för att söka. Googles <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Sekretesspolicy<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> och <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Användarvillkor<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> tillämpas.</string>
    <string name="taskbar_edu_close">Stäng</string>
    <string name="taskbar_edu_done">Färdig</string>
    <string name="taskbar_button_home">Hemskärm</string>
    <string name="taskbar_button_a11y">Tillgänglighet</string>
    <string name="taskbar_button_back">Tillbaka</string>
    <string name="taskbar_button_ime_switcher">IME-omkopplare</string>
    <string name="taskbar_button_recents">Senaste</string>
    <string name="taskbar_button_notifications">Aviseringar</string>
    <string name="taskbar_button_quick_settings">Snabbinställningar</string>
    <string name="taskbar_a11y_title">Aktivitetsfält</string>
    <string name="taskbar_a11y_shown_title">Aktivitetsfältet visas</string>
    <string name="taskbar_a11y_hidden_title">Aktivitetsfältet dolt</string>
    <string name="taskbar_phone_a11y_title">Navigationsfält</string>
    <string name="always_show_taskbar">Visa alltid aktivitetsfältet</string>
    <string name="change_navigation_mode">Ändra navigeringsläge</string>
    <string name="taskbar_divider_a11y_title">Aktivitetsfältets avdelare</string>
    <string name="move_drop_target_top_or_left">Flytta till toppen/vänster</string>
    <string name="move_drop_target_bottom_or_right">Flytta till botten/höger</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Visa # app till.}other{Visa # appar till.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Visa # datorapp.}other{Visa # datorappar.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> och <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Bubbla</string>
    <string name="bubble_bar_overflow_description">Rinner över</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> från <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> och <xliff:g id="bubble_count" example="4">%2$d</xliff:g> mer</string>
    <string name="app_name">Launcher3</string>
</resources>