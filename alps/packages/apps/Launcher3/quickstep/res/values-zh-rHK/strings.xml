<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">釘選</string>
    <string name="recent_task_option_freeform">自由形式</string>
    <string name="recent_task_option_desktop">桌面</string>
    <string name="recents_empty_message">無最近項目</string>
    <string name="accessibility_app_usage_settings">應用程式使用設定</string>
    <string name="recents_clear_all">全部清除</string>
    <string name="accessibility_recent_apps">最近使用的應用程式</string>
    <string name="task_view_closed">任務已關閉</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 分鐘</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> 今天剩餘</string>
    <string name="title_app_suggestions">應用程式建議</string>
    <string name="all_apps_prediction_tip">您的預測應用程式</string>
    <string name="hotseat_edu_title_migrate">在主畫面底部獲得應用程式建議</string>
    <string name="hotseat_edu_title_migrate_landscape">在主畫面的我的最愛行上獲得應用程式建議</string>
    <string name="hotseat_edu_message_migrate">在主畫面上輕鬆存取最常用的應用程式。建議會根據使用情況改變。最底行的應用程式將會移至主畫面。</string>
    <string name="hotseat_edu_message_migrate_landscape">在主畫面上輕鬆存取最常用的應用程式。建議會根據使用情況改變。我的最愛行中的應用程式將會移至主畫面。</string>
    <string name="hotseat_edu_accept">獲得應用程式建議</string>
    <string name="hotseat_edu_dismiss">不用了，謝謝</string>
    <string name="hotseat_prediction_settings">設定</string>
    <string name="hotseat_auto_enrolled">最常用的應用程式會出現在此，並根據使用情況改變</string>
    <string name="hotseat_tip_no_empty_slots">將應用程式拖出底部行以獲得應用程式建議</string>
    <string name="hotseat_tip_gaps_filled">應用程式建議已新增到空白處</string>
    <string name="hotsaet_tip_prediction_enabled">應用程式建議已啟用</string>
    <string name="hotsaet_tip_prediction_disabled">應用程式建議已停用</string>
    <string name="hotseat_prediction_content_description">預測的應用程式：<xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">旋轉您的裝置</string>
    <string name="gesture_tutorial_rotation_prompt">旋轉您的裝置，以完成手勢導覽教學</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">確保從最右側或最左側滑動</string>
    <string name="back_gesture_feedback_cancelled">確保從螢幕右側或左側向中間滑動，並鬆開</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">您已學會如何從右滑動完成返回。接下來，學習如何切換應用程式。</string>
    <string name="back_gesture_feedback_complete_with_follow_up">您完成了「返回」手勢。接下來，學習如何切換應用程式。</string>
    <string name="back_gesture_feedback_complete_without_follow_up">您完成了返回手勢</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">滑動時請勿過於接近螢幕的底部</string>
    <string name="back_gesture_tutorial_confirm_subtitle">欲調整返回手勢的靈敏度，請移至「設定」。</string>
    <string name="back_gesture_intro_title">滑動以返回</string>
    <string name="back_gesture_intro_subtitle">如需返回前一個頁面，請從螢幕的左側或右側向中間滑動。</string>
    <string name="back_gesture_spoken_intro_subtitle">如需返回前一個頁面，請用兩隻手指從螢幕的左側或右側向中間滑動。</string>
    <string name="back_gesture_tutorial_title">返回</string>
    <string name="back_gesture_tutorial_subtitle">從螢幕左側或右側向中間滑動</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">確保從螢幕底部向上滑動</string>
    <string name="home_gesture_feedback_overview_detected">確保在放手之前不要停頓</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">滑動時確保垂直</string>
    <string name="home_gesture_feedback_complete_with_follow_up">您完成了「進入首頁」手勢。接下來，學習如何返回。</string>
    <string name="home_gesture_feedback_complete_without_follow_up">您完成了進入「主頁」手勢</string>
    <string name="home_gesture_intro_title">滑動以回到主頁</string>
    <string name="home_gesture_intro_subtitle">從螢幕底部向上滑動。這個手勢始終適用於回到主頁的操作。</string>
    <string name="home_gesture_spoken_intro_subtitle">用兩隻手指從螢幕底部向上滑動。這個手勢始終適用於回到主頁的操作。</string>
    <string name="home_gesture_tutorial_title">前往主螢幕</string>
    <string name="home_gesture_tutorial_subtitle">從螢幕底部向上滑動</string>
    <string name="home_gesture_tutorial_success">做得好！</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">確保從螢幕底部向上滑動</string>
    <string name="overview_gesture_feedback_home_detected">嘗試在鬆開前按住該視窗更長時間</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">確保垂直向上滑動，然後暫停</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">您已學會了如何使用手勢。如需關閉手勢，請前往「設定」。</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">您已完成切換應用程式手勢</string>
    <string name="overview_gesture_intro_title">滑動以切換應用程式</string>
    <string name="overview_gesture_intro_subtitle">欲在應用程式之間切換，請從畫面底部向上劃並按住，然後鬆開。</string>
    <string name="overview_gesture_spoken_intro_subtitle">欲在應用程式之間切換，請用兩根手指從畫面底部向上劃並按住，然後鬆開。</string>
    <string name="overview_gesture_tutorial_title">切換應用程式</string>
    <string name="overview_gesture_tutorial_subtitle">從螢幕底部向上滑動，按住，然後鬆開</string>
    <string name="overview_gesture_tutorial_success">不錯！</string>
    <string name="gesture_tutorial_confirm_title">一切準備就緒</string>
    <string name="gesture_tutorial_action_button_label">完成</string>
    <string name="gesture_tutorial_action_button_label_settings">設定</string>
    <string name="gesture_tutorial_try_again">再試一次</string>
    <string name="gesture_tutorial_nice">很好！</string>
    <string name="gesture_tutorial_step">指南<xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">一切準備就緒！</string>
    <string name="allset_hint">向上滑動回到主頁</string>
    <string name="allset_button_hint">按首頁按鈕以切換至首頁</string>
    <string name="allset_description_generic">您已準備好開始使用 <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">裝置</string>
    <string name="allset_navigation_settings"><annotation id="link">系統瀏覽設定</annotation></string>
    <string name="action_share">分享</string>
    <string name="action_screenshot">螢幕截圖</string>
    <string name="action_split">分割</string>
    <string name="action_save_app_pair">保留應用程式對</string>
    <string name="toast_split_select_app">輕觸另一個應用程式以使用分屏功能</string>
    <string name="toast_contextual_split_select_app">選擇另一個應用程式以使用分屏功能</string>
    <string name="toast_split_select_app_cancel"><b>取消</b></string>
    <string name="toast_split_select_cont_desc">離開分屏選擇</string>
    <string name="toast_split_app_unsupported">選擇另一個應用程式以使用分屏功能</string>
    <string name="blocked_by_policy">應用程式或您的組織不允許這個操作</string>
    <string name="split_widgets_not_supported">目前尚不支援小工具。請選擇其他應用程式。</string>
    <string name="skip_tutorial_dialog_title">跳過導航指南？</string>
    <string name="skip_tutorial_dialog_subtitle">您日後可在<xliff:g id="name">%1$s</xliff:g>應用程式中找到它</string>
    <string name="gesture_tutorial_action_button_label_cancel">取消</string>
    <string name="gesture_tutorial_action_button_label_skip">略過</string>
    <string name="accessibility_rotate_button">旋轉螢幕</string>
    <string name="taskbar_edu_a11y_title">工作列教學</string>
    <string name="taskbar_edu_splitscreen">將應用程式拖至側面以同時使用兩個應用程式</string>
    <string name="taskbar_edu_stashing">緩慢向上滑動，以顯示工作列</string>
    <string name="taskbar_edu_suggestions">根據您的日常活動獲得應用程式建議</string>
    <string name="taskbar_edu_pinning">長按分隔線以固定工作列</string>
    <string name="taskbar_edu_features">利用工作列完成更多操作</string>
    <string name="taskbar_edu_pinning_title">始終顯示工作列</string>
    <string name="taskbar_edu_pinning_standalone">如需在螢幕底部始終顯示工作列，請長按分隔線。</string>
    <string name="taskbar_search_edu_title">長按動作鍵以搜尋螢幕上的內容</string>
    <string name="taskbar_edu_search_disclosure">此產品使用您螢幕上已選定的部分進行搜尋。Google<xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>《私隱政策》<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g>及<xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>《服務條款》<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g>適用。</string>
    <string name="taskbar_edu_close">關閉</string>
    <string name="taskbar_edu_done">完成</string>
    <string name="taskbar_button_home">家居</string>
    <string name="taskbar_button_a11y">協助工具捷徑</string>
    <string name="taskbar_button_back">上一頁</string>
    <string name="taskbar_button_ime_switcher">IME 切換器</string>
    <string name="taskbar_button_recents">最新的</string>
    <string name="taskbar_button_notifications">通知</string>
    <string name="taskbar_button_quick_settings">快速設定</string>
    <string name="taskbar_a11y_title">工作列</string>
    <string name="taskbar_a11y_shown_title">顯示工作列</string>
    <string name="taskbar_a11y_hidden_title">隱藏工作列</string>
    <string name="taskbar_phone_a11y_title">導航列</string>
    <string name="always_show_taskbar">始終顯示工作列</string>
    <string name="change_navigation_mode">變更導覽模式</string>
    <string name="taskbar_divider_a11y_title">工作列分隔線</string>
    <string name="move_drop_target_top_or_left">向左上角移動</string>
    <string name="move_drop_target_bottom_or_right">向右下角移動</string>
    <string name="quick_switch_overflow">"{count,plural, =1{顯示另外 # 個應用程式。}other{顯示另外 # 個應用程式。}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{顯示 # 個桌面應用程式。}other{顯示 # 個桌面應用程式。}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> 及 <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">氣泡</string>
    <string name="bubble_bar_overflow_description">滿溢</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> 來自 <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> 及 <xliff:g id="bubble_count" example="4">%2$d</xliff:g> 等</string>
    <string name="app_name">Launcher3</string>
</resources>