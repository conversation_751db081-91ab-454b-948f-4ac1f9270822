<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">"Fijar"</string>
    <string name="recent_task_option_freeform">"Formato libre"</string>
    <string name="recent_task_option_desktop">"Escritorio"</string>
    <string name="recents_empty_message">"No hay elementos recientes"</string>
    <string name="accessibility_app_usage_settings">"Configuración de uso de la app"</string>
    <string name="recents_clear_all">"Cerrar todo"</string>
    <string name="accessibility_recent_apps">"Apps recientes"</string>
    <string name="task_view_closed">"Se cerró la tarea"</string>
    <string name="task_contents_description_with_remaining_time">"<xliff:g id="TASK_DESCRIPTION">%1$s</xliff:g> (<xliff:g id="REMAINING_TIME">%2$s</xliff:g>)"</string>
    <string name="shorter_duration_less_than_one_minute">"&lt; 1 minuto"</string>
    <string name="time_left_for_app">"Tiempo restante: <xliff:g id="TIME">%1$s</xliff:g>"</string>
    <string name="title_app_suggestions">"Sugerencias de aplicaciones"</string>
    <string name="all_apps_prediction_tip">"Predicción de tus apps"</string>
    <string name="hotseat_edu_title_migrate">"Obtén sugerencias de aplicaciones en la fila inferior de la pantalla principal"</string>
    <string name="hotseat_edu_title_migrate_landscape">"Obtén sugerencias de apps en la fila de favoritos de la pantalla principal"</string>
    <string name="hotseat_edu_message_migrate">"Accede fácilmente en la pantalla principal a las apps que más usas. Las sugerencias cambiarán según tus rutinas. Las apps de la fila inferior se desplazarán hacia arriba en la pantalla principal."</string>
    <string name="hotseat_edu_message_migrate_landscape">"Accede fácilmente en la pantalla principal a las apps que más usas. Las sugerencias cambiarán según tus rutinas. Se moverán a la pantalla principal las apps que estén en la fila de favoritos."</string>
    <string name="hotseat_edu_accept">"Obtén sugerencias de aplicaciones"</string>
    <string name="hotseat_edu_dismiss">"No, gracias"</string>
    <string name="hotseat_prediction_settings">"Configuración"</string>
    <string name="hotseat_auto_enrolled">"Las apps que más se usan se muestran aquí y cambian según las rutinas"</string>
    <string name="hotseat_tip_no_empty_slots">"Arrastra apps fuera de la fila inferior para obtener sugerencias"</string>
    <string name="hotseat_tip_gaps_filled">"Se agregaron sugerencias de aplicaciones a un espacio vacío"</string>
    <string name="hotsaet_tip_prediction_enabled">"Sugerencias de apps habilitadas"</string>
    <string name="hotsaet_tip_prediction_disabled">"Las sugerencias de aplicaciones están inhabilitadas"</string>
    <string name="hotseat_prediction_content_description">"Predicción de app: <xliff:g id="TITLE">%1$s</xliff:g>"</string>
    <string name="gesture_tutorial_rotation_prompt_title">"Rota el dispositivo"</string>
    <string name="gesture_tutorial_rotation_prompt">"Rota el dispositivo para completar el instructivo de la navegación por gestos"</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">"Asegúrate de deslizar desde el extremo derecho o izquierdo"</string>
    <string name="back_gesture_feedback_cancelled">"Recuerda deslizar desde el borde izquierdo o derecho hacia el centro de la pantalla y, luego, soltar"</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">"Ya sabes deslizar el dedo desde la derecha para ir atrás. Ahora, descubre cómo cambiar de app."</string>
    <string name="back_gesture_feedback_complete_with_follow_up">"Completaste el gesto \"Atrás\". A continuación, obtén información para cambiar de app."</string>
    <string name="back_gesture_feedback_complete_without_follow_up">"Completaste el gesto para ir atrás"</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">"Asegúrate de no deslizar muy cerca de la parte inferior de la pantalla"</string>
    <string name="back_gesture_tutorial_confirm_subtitle">"Cambia sensibilidad de gesto \"Atrás\" en Configuración"</string>
    <string name="back_gesture_intro_title">"Desliza para ir atrás"</string>
    <string name="back_gesture_intro_subtitle">"Desliza el dedo desde el borde derecho o izquierdo hasta el centro para volver a la última pantalla."</string>
    <string name="back_gesture_spoken_intro_subtitle">"Para volver la pantalla anterior, desliza 2 dedos desde el borde izquierdo o derecho hacia el centro de la pantalla."</string>
    <string name="back_gesture_tutorial_title">"Volver atrás"</string>
    <string name="back_gesture_tutorial_subtitle">"Desliza desde el borde izquierdo o derecho hacia el centro de la pantalla"</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">"Asegúrate de deslizar hacia arriba desde el borde inferior de la pantalla"</string>
    <string name="home_gesture_feedback_overview_detected">"Asegúrate de no detenerte antes de soltar"</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">"Asegúrate de deslizar el dedo directamente hacia arriba"</string>
    <string name="home_gesture_feedback_complete_with_follow_up">"Completaste el gesto para ir a la página principal. A continuación, obtén información para volver."</string>
    <string name="home_gesture_feedback_complete_without_follow_up">"Completaste el gesto para ir a la página principal"</string>
    <string name="home_gesture_intro_title">"Desliza para ir a la página principal"</string>
    <string name="home_gesture_intro_subtitle">"Desliza hacia arriba desde la parte inferior de la pantalla. Este gesto te llevará a la pantalla principal."</string>
    <string name="home_gesture_spoken_intro_subtitle">"Desliza hacia arriba desde la parte inferior. Este gesto te llevará siempre a la pantalla principal."</string>
    <string name="home_gesture_tutorial_title">"Ve a la pantalla principal"</string>
    <string name="home_gesture_tutorial_subtitle">"Desliza hacia arriba desde la parte inferior de la pantalla"</string>
    <string name="home_gesture_tutorial_success">"¡Bien hecho!"</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">"Asegúrate de deslizar hacia arriba desde el borde inferior de la pantalla"</string>
    <string name="overview_gesture_feedback_home_detected">"Intenta mantener presionada la ventana más tiempo antes de soltarla"</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">"Asegúrate de deslizar directamente hacia arriba y detenerte"</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">"Ya sabes cómo usar los gestos. Para desactivarlos, ve a Configuración."</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">"Completaste el gesto para cambiar de app"</string>
    <string name="overview_gesture_intro_title">"Desliza para cambiar de app"</string>
    <string name="overview_gesture_intro_subtitle">"Desliza el dedo hacia arriba desde la parte inferior, mantenlo presionado y suéltalo."</string>
    <string name="overview_gesture_spoken_intro_subtitle">"Para alternar entre apps, desliza el dedo hacia arriba, mantén presionado y, luego, suelta."</string>
    <string name="overview_gesture_tutorial_title">"Cambia de app"</string>
    <string name="overview_gesture_tutorial_subtitle">"Desliza hacia arriba desde la parte inferior de la pantalla, mantenla presionada y, luego, suelta"</string>
    <string name="overview_gesture_tutorial_success">"¡Bien hecho!"</string>
    <string name="gesture_tutorial_confirm_title">"Listo"</string>
    <string name="gesture_tutorial_action_button_label">"Listo"</string>
    <string name="gesture_tutorial_action_button_label_settings">"Configuración"</string>
    <string name="gesture_tutorial_try_again">"Vuelve a intentarlo"</string>
    <string name="gesture_tutorial_nice">"¡Genial!"</string>
    <string name="gesture_tutorial_step">"Instructivo <xliff:g id="CURRENT">%1$d</xliff:g>/<xliff:g id="TOTAL">%2$d</xliff:g>"</string>
    <string name="allset_title">"Todo listo"</string>
    <string name="allset_hint">"Desliza el dedo hacia arriba para ir a la página principal"</string>
    <string name="allset_button_hint">"Presiona el botón de inicio para ir a la pantalla principal"</string>
    <string name="allset_description_generic">"Ya puedes comenzar a usar tu <xliff:g id="DEVICE">%1$s</xliff:g>."</string>
    <string name="default_device_name">"dispositivo"</string>
    <string name="allset_navigation_settings"><annotation id="link">"Configuración de navegación del sistema"</annotation></string>
    <string name="action_share">"Compartir"</string>
    <string name="action_screenshot">"Captura de pantalla"</string>
    <string name="action_split">"Pantalla dividida"</string>
    <string name="action_save_app_pair">"Guardar vinculación"</string>
    <string name="toast_split_select_app">"Presiona otra app para usar la pantalla dividida"</string>
    <string name="toast_contextual_split_select_app">"Elige otra app para usar la pantalla dividida"</string>
    <string name="toast_split_select_app_cancel"><b>"Cancelar"</b></string>
    <string name="toast_split_select_cont_desc">"Salir de la selección de pantalla dividida"</string>
    <string name="toast_split_app_unsupported">"Elige otra app para usar la pantalla dividida"</string>
    <string name="blocked_by_policy">"La app o tu organización no permiten realizar esta acción"</string>
    <string name="split_widgets_not_supported">"En este momento, los widgets no son compatibles; selecciona otra app"</string>
    <string name="skip_tutorial_dialog_title">"¿Omitir el instructivo de navegación?"</string>
    <string name="skip_tutorial_dialog_subtitle">"Puedes encontrarlo en la app de <xliff:g id="NAME">%1$s</xliff:g>"</string>
    <string name="gesture_tutorial_action_button_label_cancel">"Cancelar"</string>
    <string name="gesture_tutorial_action_button_label_skip">"Omitir"</string>
    <string name="accessibility_rotate_button">"Girar pantalla"</string>
    <string name="taskbar_edu_a11y_title">"Información sobre la barra de tareas"</string>
    <string name="taskbar_edu_splitscreen">"Arrastra una app hacia un lado para usar 2 apps a la vez"</string>
    <string name="taskbar_edu_stashing">"Desliza lento hacia arriba para ver la Barra de tareas"</string>
    <string name="taskbar_edu_suggestions">"Recibe sugerencias de apps basadas en tu rutina"</string>
    <string name="taskbar_edu_pinning">"Mantén presionado el divisor para fijar la Barra de tareas"</string>
    <string name="taskbar_edu_features">"Aprovecha mejor la Barra de tareas"</string>
    <string name="taskbar_edu_pinning_title">"Mostrar siempre la Barra de tareas"</string>
    <string name="taskbar_edu_pinning_standalone">"Mantén presionado el divisor para mostrar siempre la Barra de tareas en la parte inferior de la pantalla"</string>
    <string name="taskbar_search_edu_title">"Mantén presionada la tecla de acción para buscar qué hay en la pantalla"</string>
    <string name="taskbar_edu_search_disclosure">"Este producto usa la parte seleccionada de la pantalla para buscar. Se aplican la <xliff:g id="BEGIN_PRIVACY_LINK">&lt;a href="%1$s"&gt;</xliff:g>Política de Privacidad<xliff:g id="END_PRIVACY_LINK">&lt;/a&gt;</xliff:g> y las <xliff:g id="BEGIN_TOS_LINK">&lt;a href="%2$s"&gt;</xliff:g>Condiciones del Servicio<xliff:g id="END_TOS_LINK">&lt;/a&gt;</xliff:g> de Google."</string>
    <string name="taskbar_edu_close">"Cerrar"</string>
    <string name="taskbar_edu_done">"Listo"</string>
    <string name="taskbar_button_home">"Botón de inicio"</string>
    <string name="taskbar_button_a11y">"Accesibilidad"</string>
    <string name="taskbar_button_back">"Atrás"</string>
    <string name="taskbar_button_ime_switcher">"Botón de IME"</string>
    <string name="taskbar_button_recents">"Recientes"</string>
    <string name="taskbar_button_notifications">"Notificaciones"</string>
    <string name="taskbar_button_quick_settings">"Config. rápida"</string>
    <string name="taskbar_a11y_title">"Barra de tareas"</string>
    <string name="taskbar_a11y_shown_title">"Barra de tareas visible"</string>
    <string name="taskbar_a11y_hidden_title">"Barra de tareas oculta"</string>
    <string name="taskbar_phone_a11y_title">"Barra de navegación"</string>
    <string name="always_show_taskbar">"Barra de tareas visible"</string>
    <string name="change_navigation_mode">"Cambiar el modo de navegación"</string>
    <string name="taskbar_divider_a11y_title">"Divisor de la Barra de tareas"</string>
    <string name="move_drop_target_top_or_left">"Mover a la parte superior o izquierda"</string>
    <string name="move_drop_target_bottom_or_right">"Mover a la parte inferior o derecha"</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Mostrar # aplicación más.}other{Mostrar # aplicaciones más.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Mostrar # aplicación para ordenadores.}other{Mostrar # aplicaciones para ordenadores.}}"</string>
    <string name="quick_switch_split_task">"<xliff:g id="APP_NAME_1">%1$s</xliff:g> y <xliff:g id="APP_NAME_2">%2$s</xliff:g>"</string>
    <string name="bubble_bar_bubble_fallback_description">"Burbuja"</string>
    <string name="bubble_bar_overflow_description">"Ampliada"</string>
    <string name="bubble_bar_bubble_description">"<xliff:g id="NOTIFICATION_TITLE">%1$s</xliff:g> de <xliff:g id="APP_NAME">%2$s</xliff:g>"</string>
    <string name="bubble_bar_description_multiple_bubbles">"<xliff:g id="BUBBLE_BAR_BUBBLE_DESCRIPTION">%1$s</xliff:g> y <xliff:g id="BUBBLE_COUNT">%2$d</xliff:g> más"</string>
</resources>