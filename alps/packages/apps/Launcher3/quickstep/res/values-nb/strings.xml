<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">PIN</string>
    <string name="recent_task_option_freeform">Frihånd</string>
    <string name="recent_task_option_desktop">Skrivebord</string>
    <string name="recents_empty_message">Ingen nylige elementer</string>
    <string name="accessibility_app_usage_settings">Innstillinger for app-bruk</string>
    <string name="recents_clear_all">Tøm alle</string>
    <string name="accessibility_recent_apps">Nylige apper</string>
    <string name="task_view_closed">Oppgave avsluttet</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minutt</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> igjen i dag</string>
    <string name="title_app_suggestions">App-forslag</string>
    <string name="all_apps_prediction_tip">De antatte appene dine</string>
    <string name="hotseat_edu_title_migrate">Få appforslag på den nederste raden på startskjermen</string>
    <string name="hotseat_edu_title_migrate_landscape">Få appforslag på favoritter-raden på startskjermen</string>
    <string name="hotseat_edu_message_migrate">Få enkel tilgang til de mest brukte appene dine direkte på startskjermen. Forslagene vil endres basert på rutinene dine. Apper på nederste rad flyttes opp til startskjermen.</string>
    <string name="hotseat_edu_message_migrate_landscape">Få enkel tilgang til de mest brukte appene dine direkte på startskjermen. Forslagene vil endres basert på rutinene dine. Apper i favoritter-raden flyttes til startskjermen.</string>
    <string name="hotseat_edu_accept">Få appforslag</string>
    <string name="hotseat_edu_dismiss">Nei takk</string>
    <string name="hotseat_prediction_settings">Innstillinger</string>
    <string name="hotseat_auto_enrolled">Mest brukte apper vises her, og endres basert på rutiner</string>
    <string name="hotseat_tip_no_empty_slots">Dra apper bort fra den nederste raden for å få forslag om apper</string>
    <string name="hotseat_tip_gaps_filled">Appforslag lagt til på tomt område</string>
    <string name="hotsaet_tip_prediction_enabled">Appforslag aktivert</string>
    <string name="hotsaet_tip_prediction_disabled">Forslag om apper er deaktivert</string>
    <string name="hotseat_prediction_content_description">Forutsett app: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Roter enheten</string>
    <string name="gesture_tutorial_rotation_prompt">Roter enheten for å fullføre veiledningen for håndbevegelsesnavigering</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Pass på at du sveiper fra ytterst til høyre eller helt til venstre</string>
    <string name="back_gesture_feedback_cancelled">Pass på at du sveiper fra den høyre eller venstre kanten mot midten av skjermen, og slipp</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Du lærte hvordan du sveiper fra høyre for å gå tilbake. Neste, finn ut hvordan du bytter apper.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Du har fullført tilbake-håndbevegelsen. Neste, finn ut hvordan du bytter apper.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Du fullførte tilbake-håndbevegelsen</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Pass på at du ikke sveiper for nær bunnen av skjermen</string>
    <string name="back_gesture_tutorial_confirm_subtitle">For å endre følsomheten for tilbake-håndbevegelsen, gå til Innstillinger.</string>
    <string name="back_gesture_intro_title">Sveip for å gå tilbake</string>
    <string name="back_gesture_intro_subtitle">For å gå tilbake til forrige skjerm, sveip fra den venstre eller høyre kanten til midten av skjermen.</string>
    <string name="back_gesture_spoken_intro_subtitle">For å gå tilbake til forrige skjerm, sveip med 2 fingre fra den venstre eller høyre kanten til midten av skjermen.</string>
    <string name="back_gesture_tutorial_title">Gå tilbake</string>
    <string name="back_gesture_tutorial_subtitle">Sveip fra den venstre eller høyre kanten til midten av skjermen</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Pass på at du sveiper opp fra bunnen av skjermen</string>
    <string name="home_gesture_feedback_overview_detected">Pass på at du ikke tar pause før du slipper</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Pass på at du sveiper rett opp</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Du har fullført bevegelsen for å gå til startsiden. Lær hvordan du går tilbake.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Du fullførte gå til startskjerm-håndbevegelsen</string>
    <string name="home_gesture_intro_title">Sveip for å gå til startskjerm</string>
    <string name="home_gesture_intro_subtitle">Sveip opp fra nederst på skjermen. Denne håndbevegelsen bringer deg til startskjermen.</string>
    <string name="home_gesture_spoken_intro_subtitle">Sveip opp med 2 fingre fra nederst på skjermen. Denne håndbevegelsen bringer deg alltid til startskjermen.</string>
    <string name="home_gesture_tutorial_title">Gå til startskjerm</string>
    <string name="home_gesture_tutorial_subtitle">Sveip opp fra nederst på skjermen</string>
    <string name="home_gesture_tutorial_success">Godt jobba!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Pass på at du sveiper opp fra bunnen av skjermen</string>
    <string name="overview_gesture_feedback_home_detected">Prøv å holde på vinduet lenger før du slipper</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Pass på at du sveiper rett opp, før du stopper midlertidig</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Du har lært hvordan du bruker håndbevegelser. For å slå av håndbevegelser, gå til Innstillinger.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Du fullførte håndbevegelsen for å bytte apper</string>
    <string name="overview_gesture_intro_title">Sveip for å bytte apper</string>
    <string name="overview_gesture_intro_subtitle">For å bytte mellom apper, sveip opp fra nederst på skjermen, hold, og deretter slipp.</string>
    <string name="overview_gesture_spoken_intro_subtitle">For å bytte mellom apper, sveip opp med 2 fingre fra nederst på skjermen, hold, og deretter slipp.</string>
    <string name="overview_gesture_tutorial_title">Bytt apper</string>
    <string name="overview_gesture_tutorial_subtitle">Sveip opp fra bunnen av skjermen, hold, og deretter slipp</string>
    <string name="overview_gesture_tutorial_success">Godt gjort!</string>
    <string name="gesture_tutorial_confirm_title">Ferdig</string>
    <string name="gesture_tutorial_action_button_label">Ferdig</string>
    <string name="gesture_tutorial_action_button_label_settings">Innstillinger</string>
    <string name="gesture_tutorial_try_again">Prøv igjen</string>
    <string name="gesture_tutorial_nice">Flott!</string>
    <string name="gesture_tutorial_step">Veiledning <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Alt klart!</string>
    <string name="allset_hint">Sveip oppover for å gå til start</string>
    <string name="allset_button_hint">Trykk på hjem-knappen for å gå til startskjermen</string>
    <string name="allset_description_generic">Du er klar til å begynne å bruke din <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Enhet</string>
    <string name="allset_navigation_settings"><annotation id="link">Systemnavigasjonsinnstillinger</annotation></string>
    <string name="action_share">Del</string>
    <string name="action_screenshot">Skjermbilde</string>
    <string name="action_split">Splitt</string>
    <string name="action_save_app_pair">Lagre app-paring</string>
    <string name="toast_split_select_app">Berør en annen app for å bruke delt skjerm</string>
    <string name="toast_contextual_split_select_app">Velg en annen app for å bruke delt skjerm</string>
    <string name="toast_split_select_app_cancel"><b>Avbryt</b></string>
    <string name="toast_split_select_cont_desc">Avslutt valg av delt skjerm</string>
    <string name="toast_split_app_unsupported">Velg en annen app for å bruke delt skjerm</string>
    <string name="blocked_by_policy">Handlingen tillater ikke at det tas skjermbilder</string>
    <string name="split_widgets_not_supported">Widgeter støttes for øyeblikket ikke. Velg en annen app.</string>
    <string name="skip_tutorial_dialog_title">Hoppe over veiledning?</string>
    <string name="skip_tutorial_dialog_subtitle">Du kan finne dette senere i appen <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">Avbrutt</string>
    <string name="gesture_tutorial_action_button_label_skip">Hopp over</string>
    <string name="accessibility_rotate_button">Rotér skjermen</string>
    <string name="taskbar_edu_a11y_title">Opplæring om oppgavelinje</string>
    <string name="taskbar_edu_splitscreen">Dra en app til siden for å bruke 2 apper samtidig</string>
    <string name="taskbar_edu_stashing">Sveip sakte oppover for å vise oppgavelinjen</string>
    <string name="taskbar_edu_suggestions">Få forslag til apper basert på rutinen din</string>
    <string name="taskbar_edu_pinning">Trykk og hold på skillelinjen for å feste oppgavelinjen</string>
    <string name="taskbar_edu_features">Gjør mer med oppgavelinjen</string>
    <string name="taskbar_edu_pinning_title">Vis alltid oppgavelinjen</string>
    <string name="taskbar_edu_pinning_standalone">Trykk og hold på skillelinjen for å alltid vise oppgavelinjen nederst på skjermen.</string>
    <string name="taskbar_search_edu_title">Trykk og hold på handlingstasten for å søke etter det som er på skjermen</string>
    <string name="taskbar_edu_search_disclosure">Dette produktet bruker den valgte delen av skjerme for å søke. Googles <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\«%1$s\»&gt;</xliff:g>Personvernregler<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> og <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\«%2$s\»&gt;</xliff:g>Bruksbetingelser<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> gjelder.</string>
    <string name="taskbar_edu_close">Nærme</string>
    <string name="taskbar_edu_done">Ferdig</string>
    <string name="taskbar_button_home">Startskjerm</string>
    <string name="taskbar_button_a11y">Tilgjengelighet</string>
    <string name="taskbar_button_back">Tilbake</string>
    <string name="taskbar_button_ime_switcher">IME-bryter</string>
    <string name="taskbar_button_recents">Nylig</string>
    <string name="taskbar_button_notifications">Påminnelser</string>
    <string name="taskbar_button_quick_settings">Hurtiginnstillinger</string>
    <string name="taskbar_a11y_title">Oppgavelinje</string>
    <string name="taskbar_a11y_shown_title">Oppgavelinje vist</string>
    <string name="taskbar_a11y_hidden_title">Oppgavelinje skjult</string>
    <string name="taskbar_phone_a11y_title">Navigasjonsfelt</string>
    <string name="always_show_taskbar">Alltid vis oppgavelinje</string>
    <string name="change_navigation_mode">Endre navigeringsmodus</string>
    <string name="taskbar_divider_a11y_title">Oppgavelinjedeler</string>
    <string name="move_drop_target_top_or_left">Flytt øverst til venstre</string>
    <string name="move_drop_target_bottom_or_right">Flytt nederst til høyre</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Vis # app til.}other{Vis # apper til.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Vis # datamaskinprogram.}other{Vis # datamaskinprogrammer.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> og <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Boble</string>
    <string name="bubble_bar_overflow_description">Overflyt</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> fra <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> og <xliff:g id="bubble_count" example="4">%2$d</xliff:g> mer</string>
    <string name="app_name">Launcher3</string>
</resources>