<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Attacher</string>
    <string name="recent_task_option_freeform">Freeform</string>
    <string name="recent_task_option_desktop">Ordinateur</string>
    <string name="recents_empty_message">Aucun élément récent</string>
    <string name="accessibility_app_usage_settings">Paramètres d’utilisation de l’application</string>
    <string name="recents_clear_all">Tout effacer</string>
    <string name="accessibility_recent_apps">Applications récentes</string>
    <string name="task_view_closed">Tâche terminée</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minute</string>
    <string name="time_left_for_app">Il reste <xliff:g id="time" example="7 minutes">%1$s</xliff:g> aujourd\'hui</string>
    <string name="title_app_suggestions">Suggestions d\'applications</string>
    <string name="all_apps_prediction_tip">Vos applications prévues</string>
    <string name="hotseat_edu_title_migrate">Obtenez des suggestions d\'applications sur la ligne inférieure de votre écran d\'accueil</string>
    <string name="hotseat_edu_title_migrate_landscape">Obtenez des suggestions d\'applications sur la ligne des favoris de votre écran d\'accueil</string>
    <string name="hotseat_edu_message_migrate">Accédez facilement à vos applications les plus utilisées directement sur l\'écran d\'accueil. Les suggestions changeront en fonction de vos routines. Les applications sur la ligne inférieure se déplaceront vers votre écran d\'accueil.</string>
    <string name="hotseat_edu_message_migrate_landscape">Accédez facilement à vos applications les plus utilisées directement sur l\'écran d\'accueil. Les suggestions changeront en fonction de vos routines. Les applications sur la ligne des favoris se déplaceront vers votre écran d\'accueil.</string>
    <string name="hotseat_edu_accept">Obtenez des suggestions d\'applications</string>
    <string name="hotseat_edu_dismiss">Non, merci.</string>
    <string name="hotseat_prediction_settings">Paramètres</string>
    <string name="hotseat_auto_enrolled">Les applications les plus utilisées apparaissent ici et changent en fonction des routines</string>
    <string name="hotseat_tip_no_empty_slots">Faites glisser les applications de la ligne inférieure pour obtenir des suggestions d’applications</string>
    <string name="hotseat_tip_gaps_filled">Suggestions d\'applications ajoutées à l\'espace vide</string>
    <string name="hotsaet_tip_prediction_enabled">Suggestions d\'applications activées</string>
    <string name="hotsaet_tip_prediction_disabled">Les suggestions d’applications sont désactivées</string>
    <string name="hotseat_prediction_content_description">Application prédéfinie : <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Faites pivoter votre appareil</string>
    <string name="gesture_tutorial_rotation_prompt">Faites pivoter votre appareil pour terminer le tutoriel de navigation gestuelle</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Assurez-vous de faire glisser votre doigt depuis le bord le plus à droite ou le plus à gauche</string>
    <string name="back_gesture_feedback_cancelled">Assurez-vous de faire glisser votre doigt depuis le bord droit ou gauche vers le milieu de l\'écran et relâchez.</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Vous avez appris à faire glisser votre doigt depuis la droite pour revenir en arrière.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Vous avez terminé le geste de retour. Ensuite, découvrez comment changer d\'application.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Vous avez effectué le geste de retour</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Assurez-vous de ne pas faire glisser votre doigt trop près du bas de l\'écran.</string>
    <string name="back_gesture_tutorial_confirm_subtitle">Pour modifier la sensibilité du geste de retour, allez dans Paramètres.</string>
    <string name="back_gesture_intro_title">Faites glisser votre doigt pour revenir</string>
    <string name="back_gesture_intro_subtitle">Pour revenir au dernier écran, faites glisser votre doigt depuis le bord gauche ou droit vers le milieu de l\'écran.</string>
    <string name="back_gesture_spoken_intro_subtitle">Pour revenir au dernier écran, faites glisser 2 doigts du bord gauche ou droit vers le milieu de l\'écran.</string>
    <string name="back_gesture_tutorial_title">Retour</string>
    <string name="back_gesture_tutorial_subtitle">Faites glisser votre doigt depuis le bord gauche ou droit vers le milieu de l\'écran</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Assurez-vous de faire glisser votre doigt vers le haut depuis le bord inférieur de l\'écran.</string>
    <string name="home_gesture_feedback_overview_detected">Assurez-vous de ne pas faire de pause avant de relâcher</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Assurez-vous de faire glisser votre doigt vers le haut</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Vous avez accompli le geste du retour à l’accueil. La prochaine étape consiste à apprendre comment revenir en arrière.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">Vous avez effectué le geste de retour à l\'accueil</string>
    <string name="home_gesture_intro_title">Faites glisser votre doigt pour revenir à l\'accueil</string>
    <string name="home_gesture_intro_subtitle">Faites glisser votre doigt vers le haut depuis le bas de l\'écran. Ce geste vous amène toujours à l\'écran d\'accueil.</string>
    <string name="home_gesture_spoken_intro_subtitle">Faites glisser 2 doigts vers le haut depuis le bas de l\'écran. Ce geste vous amène toujours à l\'écran d\'accueil.</string>
    <string name="home_gesture_tutorial_title">Retour à la page d’accueil</string>
    <string name="home_gesture_tutorial_subtitle">Faites glisser votre doigt vers le haut depuis le bas de votre écran</string>
    <string name="home_gesture_tutorial_success">Bon travail !</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Assurez-vous de faire glisser votre doigt vers le haut depuis le bord inférieur de l\'écran.</string>
    <string name="overview_gesture_feedback_home_detected">Essayez de maintenir la fenêtre plus longtemps avant de la relâcher</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Assurez-vous de faire glisser votre doigt vers le haut, puis faites une pause</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Vous avez appris à utiliser les gestes. Pour désactiver les gestes, accédez à Paramètres.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Vous avez effectué le geste de changement d\'application</string>
    <string name="overview_gesture_intro_title">Faites glisser votre doigt pour changer d\'application</string>
    <string name="overview_gesture_intro_subtitle">Pour basculer entre les applications, faites glisser votre doigt depuis le bas de votre écran, maintenez enfoncé, puis relâchez.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Pour basculer entre les applications, faites glisser 2 doigts depuis le bas de votre écran, maintenez enfoncé, puis relâchez.</string>
    <string name="overview_gesture_tutorial_title">Basculer entre les applications</string>
    <string name="overview_gesture_tutorial_subtitle">Faites glisser votre doigt vers le haut depuis le bas de votre écran, maintenez enfoncé, puis relâchez</string>
    <string name="overview_gesture_tutorial_success">Bravo !</string>
    <string name="gesture_tutorial_confirm_title">Tout est prêt</string>
    <string name="gesture_tutorial_action_button_label">OK</string>
    <string name="gesture_tutorial_action_button_label_settings">Paramètres</string>
    <string name="gesture_tutorial_try_again">Réessayer</string>
    <string name="gesture_tutorial_nice">Bien !</string>
    <string name="gesture_tutorial_step">Tutoriel <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Tout est prêt !</string>
    <string name="allset_hint">Faites glisser votre doigt vers le haut pour revenir à l\'accueil</string>
    <string name="allset_button_hint">Appuyez sur le bouton Accueil pour accéder à votre écran d\'accueil</string>
    <string name="allset_description_generic">Vous êtes prêt à commencer à utiliser votre <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Appareil</string>
    <string name="allset_navigation_settings"><annotation id="link">Configuration de la navigation du système</annotation></string>
    <string name="action_share">Partager</string>
    <string name="action_screenshot">Capture d’écran</string>
    <string name="action_split">Séparer</string>
    <string name="action_save_app_pair">Enregistrer la paire d\'applications</string>
    <string name="toast_split_select_app">Cliquer sur une autre application pour utiliser l\'écran partagé</string>
    <string name="toast_contextual_split_select_app">Choisir une autre application pour utiliser l\'écran partagé</string>
    <string name="toast_split_select_app_cancel"><b>Annuler</b></string>
    <string name="toast_split_select_cont_desc">Quitter la sélection d\'écran partagé</string>
    <string name="toast_split_app_unsupported">Choisir une autre application pour utiliser l\'écran partagé</string>
    <string name="blocked_by_policy">Cette action n’est pas autorisée par l’application ou votre entreprise</string>
    <string name="split_widgets_not_supported">Les widgets ne sont actuellement pas pris en charge. Sélectionnez une autre application.</string>
    <string name="skip_tutorial_dialog_title">Ignorer le tutoriel de navigation ?</string>
    <string name="skip_tutorial_dialog_subtitle">Vous pouvez le retrouver dans l\'application <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">Annuler</string>
    <string name="gesture_tutorial_action_button_label_skip">Ignorer</string>
    <string name="accessibility_rotate_button">Faire pivoter l\'écran</string>
    <string name="taskbar_edu_a11y_title">Enseignement sur la barre des tâches</string>
    <string name="taskbar_edu_splitscreen">Faites glisser une application sur le côté pour utiliser 2 applications à la fois</string>
    <string name="taskbar_edu_stashing">Faites glisser votre doigt lentement vers le haut pour afficher la barre des tâches</string>
    <string name="taskbar_edu_suggestions">Obtenez des suggestions d\'applications en fonction de votre routine</string>
    <string name="taskbar_edu_pinning">Appuyez longuement sur le séparateur pour épingler la barre des tâches</string>
    <string name="taskbar_edu_features">Faites-en davantage avec la barre des tâches</string>
    <string name="taskbar_edu_pinning_title">Afficher toujours la barre des tâches</string>
    <string name="taskbar_edu_pinning_standalone">Pour afficher toujours la barre des tâches en bas de votre écran, appuyez longuement sur le séparateur.</string>
    <string name="taskbar_search_edu_title">Appuyez longuement sur la touche d\'action pour rechercher ce qui apparaît sur votre écran</string>
    <string name="taskbar_edu_search_disclosure">Ce produit utilise la partie sélectionnée de votre écran pour effectuer une recherche. <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Politique de confidentialité<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> et <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Conditions de service<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> de Google s\'appliquent.</string>
    <string name="taskbar_edu_close">Proche</string>
    <string name="taskbar_edu_done">OK</string>
    <string name="taskbar_button_home">Écran d\'accueil</string>
    <string name="taskbar_button_a11y">Accessibilité</string>
    <string name="taskbar_button_back">Précédent</string>
    <string name="taskbar_button_ime_switcher">Sélecteur IME</string>
    <string name="taskbar_button_recents">Récents</string>
    <string name="taskbar_button_notifications">Notifications</string>
    <string name="taskbar_button_quick_settings">Paramètres rapides</string>
    <string name="taskbar_a11y_title">Barre des tâches</string>
    <string name="taskbar_a11y_shown_title">Barre des tâches affichée</string>
    <string name="taskbar_a11y_hidden_title">Barre des tâches masquée</string>
    <string name="taskbar_phone_a11y_title">Barre de navigation</string>
    <string name="always_show_taskbar">Afficher toujours la barre des tâches</string>
    <string name="change_navigation_mode">Changer le mode de navigation</string>
    <string name="taskbar_divider_a11y_title">Diviseur de la barre des tâches</string>
    <string name="move_drop_target_top_or_left">Déplacer vers le haut/la gauche</string>
    <string name="move_drop_target_bottom_or_right">Déplacer vers le bas/la droite</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Afficher # autre appli}one{Afficher # autre appli}other{Afficher # autre applis}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Afficher # application de bureau.}one{Afficher # application de bureau.}other{Afficher # applications de bureau.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> et <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Bulle</string>
    <string name="bubble_bar_overflow_description">Débordement</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> de <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> et <xliff:g id="bubble_count" example="4">%2$d</xliff:g> plus</string>
    <string name="app_name">Launcher3</string>
</resources>