<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Blocca</string>
    <string name="recent_task_option_freeform">Forma libera</string>
    <string name="recent_task_option_desktop">Desktop</string>
    <string name="recents_empty_message">Nessun elemento recente</string>
    <string name="accessibility_app_usage_settings">Impostazioni d\'uso dell\'app</string>
    <string name="recents_clear_all">Cancella tutto</string>
    <string name="accessibility_recent_apps">Applicazioni recenti</string>
    <string name="task_view_closed">Attività chiusa</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minuto</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> rimasti oggi</string>
    <string name="title_app_suggestions">Applicazioni suggerite</string>
    <string name="all_apps_prediction_tip">Le tue app previste</string>
    <string name="hotseat_edu_title_migrate">Ricevi suggerimenti sulle app nella riga inferiore della schermata Home</string>
    <string name="hotseat_edu_title_migrate_landscape">Ricevi suggerimenti sulle app nella riga dei preferiti della schermata Home</string>
    <string name="hotseat_edu_message_migrate">Accedi in modo facile alle app più utilizzate direttamente dalla schermata Home. I suggerimenti varieranno a seconda delle tue abitudini. Le app nella riga inferiore si sposteranno in alto nella schermata Home.</string>
    <string name="hotseat_edu_message_migrate_landscape">Accedi in modo facile alle app più utilizzate direttamente dalla schermata Home. I suggerimenti varieranno a seconda delle tue abitudini. Le app nella riga dei preferiti si sposteranno nella schermata Home.</string>
    <string name="hotseat_edu_accept">Ricevi suggerimenti sulle app</string>
    <string name="hotseat_edu_dismiss">No grazie</string>
    <string name="hotseat_prediction_settings">Impostazioni</string>
    <string name="hotseat_auto_enrolled">Le app più utilizzate appaiono qui e cambiano in base alle abitudini</string>
    <string name="hotseat_tip_no_empty_slots">Trascina le app dalla riga inferiore per ottenere suggerimenti sulle app</string>
    <string name="hotseat_tip_gaps_filled">Suggerimenti sulle app aggiunti allo spazio vuoto</string>
    <string name="hotsaet_tip_prediction_enabled">Suggerimenti sulle app attivati</string>
    <string name="hotsaet_tip_prediction_disabled">I suggerimenti delle app sono disattivati</string>
    <string name="hotseat_prediction_content_description">Applicazione prevista: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Ruota il dispositivo</string>
    <string name="gesture_tutorial_rotation_prompt">Ruota il dispositivo per completare l\'esercitazione sulla navigazione con i gesti.</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Accertati di scorrere il dito dal bordo all\'estrema destra o all\'estrema sinistra</string>
    <string name="back_gesture_feedback_cancelled">Accertati di scorrere il dito dal bordo destro o sinistro al centro dello schermo, quindi lascia andare</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">Hai imparato a scorrere il dito da destra per tornare indietro. Il prossimo passo è imparare a cambiare app.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">Hai completato il gesto “indietro”. Ora impariamo a cambiare a applicazione.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">Hai completato il gesto per tornare indietro</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Accertati di non scorrere il dito troppo vicino alla parte inferiore dello schermo.</string>
    <string name="back_gesture_tutorial_confirm_subtitle">Per modificare la sensibilità del gesto di tornare indietro, vai su Impostazioni.</string>
    <string name="back_gesture_intro_title">Scorri per tornare indietro</string>
    <string name="back_gesture_intro_subtitle">Per tornare all\'ultima schermata, scorri dal bordo sinistro o destro verso il centro dello schermo.</string>
    <string name="back_gesture_spoken_intro_subtitle">Per tornare all\'ultima schermata, scorri con 2 dita dal bordo sinistro o destro verso il centro dello schermo.</string>
    <string name="back_gesture_tutorial_title">Indietro</string>
    <string name="back_gesture_tutorial_subtitle">Scorri il dito dal bordo sinistro o destro verso il centro dello schermo.</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Accertati di scorrere il dito verso l\'alto dal bordo inferiore dello schermo.</string>
    <string name="home_gesture_feedback_overview_detected">Accertati di non mettere in pausa prima di lasciarlo andare</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Accertati di scorrere il dito dritto verso l\'alto</string>
    <string name="home_gesture_feedback_complete_with_follow_up">Hai completato il gesto per tornare alla Home. Ora, scopri come tornare indietro. </string>
    <string name="home_gesture_feedback_complete_without_follow_up">Hai completato il gesto per tornare alla Home</string>
    <string name="home_gesture_intro_title">Scorri per andare alla Home</string>
    <string name="home_gesture_intro_subtitle">Scorri il dito dalla parte inferiore dello schermo. Questo gesto porta sempre alla schermata Home.</string>
    <string name="home_gesture_spoken_intro_subtitle">Scorri il dito verso l\'alto con 2 dita dalla parte inferiore dello schermo. Questo gesto porta sempre alla schermata Home.</string>
    <string name="home_gesture_tutorial_title">Vai alla home</string>
    <string name="home_gesture_tutorial_subtitle">Scorri verso l\'alto dalla parte inferiore dello schermo</string>
    <string name="home_gesture_tutorial_success">Ottimo lavoro!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Accertati di scorrere il dito verso l\'alto dal bordo inferiore dello schermo.</string>
    <string name="overview_gesture_feedback_home_detected">Provare a tenere la finestra più a lungo prima di rilasciarla</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Accertati di scorrere il dito dritto verso l\'alto, quindi fai una pausa</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">Hai imparato a usare i gesti. Per disattivare i gesti, vai su Impostazioni.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">Hai completato il gesto per passare da un\'app all\'altra</string>
    <string name="overview_gesture_intro_title">Scorri per passare da un\'app all\'altra</string>
    <string name="overview_gesture_intro_subtitle">Per passare da un\'app all\'altra, passare il dito verso l\'alto dalla parte inferiore dello schermo e tenerlo premuto, quindi rilasciarlo.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Per passare da un\'app all\'altra, passare il dito verso l\'alto con 2 dita dalla parte inferiore dello schermo e tenerlo premuto, quindi rilasciarlo.</string>
    <string name="overview_gesture_tutorial_title">Passa da un\'app all\'altra</string>
    <string name="overview_gesture_tutorial_subtitle">Scorri verso l\'alto dalla parte inferiore dello schermo, tieni premuto e poi rilascia</string>
    <string name="overview_gesture_tutorial_success">Perfetto!</string>
    <string name="gesture_tutorial_confirm_title">Tutto configurato</string>
    <string name="gesture_tutorial_action_button_label">Fine</string>
    <string name="gesture_tutorial_action_button_label_settings">Impostazioni</string>
    <string name="gesture_tutorial_try_again">Riprova</string>
    <string name="gesture_tutorial_nice">Bene!</string>
    <string name="gesture_tutorial_step">Tutorial <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">È tutto configurato!</string>
    <string name="allset_hint">Scorri verso l’alto per andare alla Home</string>
    <string name="allset_button_hint">Tocca il tasto Home per andare alla schermata Home.</string>
    <string name="allset_description_generic">Sei pronto per iniziare a usare il tuo <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">Dispositivo</string>
    <string name="allset_navigation_settings"><annotation id="link">Impostazioni di navigazione del sistema</annotation></string>
    <string name="action_share">Condividi</string>
    <string name="action_screenshot">Screenshot</string>
    <string name="action_split">Dividi</string>
    <string name="action_save_app_pair">Salva l’associazione dell’app</string>
    <string name="toast_split_select_app">Toccare un\'altra app per usare lo schermo diviso</string>
    <string name="toast_contextual_split_select_app">Scegliere un\'altra app per utilizzare lo schermo diviso</string>
    <string name="toast_split_select_app_cancel"><b>Annulla</b></string>
    <string name="toast_split_select_cont_desc">Esci dalla selezione dello schermo diviso</string>
    <string name="toast_split_app_unsupported">Scegliere un\'altra app per utilizzare lo schermo diviso</string>
    <string name="blocked_by_policy">Questa azione non è consentita dall\'app o dalla tua organizzazione</string>
    <string name="split_widgets_not_supported">I widget non sono attualmente supportati. Seleziona un\'altra app.</string>
    <string name="skip_tutorial_dialog_title">Vuoi saltare il tutorial di navigazione?</string>
    <string name="skip_tutorial_dialog_subtitle">Lo potrai trovare in seguito nell’app <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">Annulla</string>
    <string name="gesture_tutorial_action_button_label_skip">Salta</string>
    <string name="accessibility_rotate_button">Ruota lo schermo</string>
    <string name="taskbar_edu_a11y_title">Come usare la barra delle applicazioni</string>
    <string name="taskbar_edu_splitscreen">Trascina un’applicazione sul lato per usare 2 applicazioni contemporaneamente</string>
    <string name="taskbar_edu_stashing">Scorri lentamente verso l\'alto per visualizzare la barra delle applicazioni</string>
    <string name="taskbar_edu_suggestions">Ottieni suggerimenti sulle app in base alla tua routine</string>
    <string name="taskbar_edu_pinning">Premi a lungo il divisorio per bloccare la barra delle applicazioni</string>
    <string name="taskbar_edu_features">Fai di più con la barra delle applicazioni</string>
    <string name="taskbar_edu_pinning_title">Mostra sempre la barra delle applicazioni</string>
    <string name="taskbar_edu_pinning_standalone">Per visualizzare sempre la barra delle applicazioni nella parte inferiore dello schermo, tieni premuto a lungo il divisorio.</string>
    <string name="taskbar_search_edu_title">Premi a lungo il tasto azione per cercare fra i contenuti presenti sullo schermo</string>
    <string name="taskbar_edu_search_disclosure">Questo prodotto utilizza la parte selezionata dello schermo per ricercare. Si applicano i seguenti documenti di Google <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Informativa sulla Privacy<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> e <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Termini di Servizio<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g>.</string>
    <string name="taskbar_edu_close">Chiudi</string>
    <string name="taskbar_edu_done">Fine</string>
    <string name="taskbar_button_home">Schermata Home</string>
    <string name="taskbar_button_a11y">Accesso facilitato</string>
    <string name="taskbar_button_back">Indietro</string>
    <string name="taskbar_button_ime_switcher">Commutatore IME</string>
    <string name="taskbar_button_recents">Recenti</string>
    <string name="taskbar_button_notifications">Notifiche</string>
    <string name="taskbar_button_quick_settings">Impostazioni rapide</string>
    <string name="taskbar_a11y_title">Barra delle applicazioni</string>
    <string name="taskbar_a11y_shown_title">Barra delle applicazioni visualizzata</string>
    <string name="taskbar_a11y_hidden_title">Barra delle applicazioni nascosta</string>
    <string name="taskbar_phone_a11y_title">Barra di navigazione</string>
    <string name="always_show_taskbar">Mostra sempre la barra delle applicazioni</string>
    <string name="change_navigation_mode">Cambia modalità di navigazione</string>
    <string name="taskbar_divider_a11y_title">Divisorio della barra delle applicazioni</string>
    <string name="move_drop_target_top_or_left">Sposta in alto/sinistra</string>
    <string name="move_drop_target_bottom_or_right">Sposta in basso/a destra</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Mostra # altra app.}other{Mostra altre # app.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{Mostra # app desktop.}other{Mostra # app desktop.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> e <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Fumetto</string>
    <string name="bubble_bar_overflow_description">Sovrafflusso</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> da <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> e altre <xliff:g id="bubble_count" example="4">%2$d</xliff:g></string>
    <string name="app_name">Launcher3</string>
</resources>
