<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">ปักหมุด</string>
    <string name="recent_task_option_freeform">รูปแบบอิสระ</string>
    <string name="recent_task_option_desktop">เดสก์ท็อป</string>
    <string name="recents_empty_message">ไม่มีรายการล่าสุด</string>
    <string name="accessibility_app_usage_settings">การตั้งค่าการใช้งานแอป</string>
    <string name="recents_clear_all">ล้างทั้งหมด</string>
    <string name="accessibility_recent_apps">แอปล่าสุด</string>
    <string name="task_view_closed">ปิดงานแล้ว</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 นาที</string>
    <string name="time_left_for_app">เหลือ <xliff:g id="time" example="7 minutes">%1$s</xliff:g> ในวันนี้</string>
    <string name="title_app_suggestions">แนะนำแอป</string>
    <string name="all_apps_prediction_tip">แอปที่คาดการณ์ของคุณ</string>
    <string name="hotseat_edu_title_migrate">รับการแนะนำแอปในแถวด้านล่างของหน้าจอหลักของคุณ</string>
    <string name="hotseat_edu_title_migrate_landscape">รับการแนะนำแอปในแถวรายการโปรดของหน้าจอหลักของคุณ</string>
    <string name="hotseat_edu_message_migrate">เข้าถึงแอปที่ใช้บ่อยบนหน้าจอหลักได้ง่าย คำแนะนำจะเปลี่ยนไปตามกิจวัตรของคุณ แอปในแถวด้านล่างจะถูกย้ายขึ้นไปบนหน้าจอหลักของคุณ</string>
    <string name="hotseat_edu_message_migrate_landscape">เข้าถึงแอปที่ใช้บ่อยบนหน้าจอหลักได้ง่าย คำแนะนำจะเปลี่ยนไปตามกิจวัตรของคุณ แอปในแถวรายการโปรดจะถูกย้ายไปยังหน้าจอหลักของคุณ</string>
    <string name="hotseat_edu_accept">รับการแนะนำแอป</string>
    <string name="hotseat_edu_dismiss">ไม่ล่ะ ขอบคุณ</string>
    <string name="hotseat_prediction_settings">การตั้งค่า</string>
    <string name="hotseat_auto_enrolled">แอปที่ใช้บ่อยจะปรากฏที่นี่ และเปลี่ยนไปตามกิจวัตร</string>
    <string name="hotseat_tip_no_empty_slots">ลากแอปออกจากแถวด้านล่างเพื่อดูคำแนะนำแอป</string>
    <string name="hotseat_tip_gaps_filled">การแนะนำแอปถูกเพิ่มไปยังที่ว่าง</string>
    <string name="hotsaet_tip_prediction_enabled">เปิดใช้งานการแนะนำแอปแล้ว</string>
    <string name="hotsaet_tip_prediction_disabled">คำแนะนำแอปถูกปิดใช้งาน</string>
    <string name="hotseat_prediction_content_description">แอปที่คาดการณ์: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">หมุนอุปกรณ์ของคุณ</string>
    <string name="gesture_tutorial_rotation_prompt">หมุนอุปกรณ์ของคุณเพื่อให้การสอนใช้การนำทางด้วยท่าทางเสร็จสมบูรณ์</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">ต้องแน่ใจว่าคุณปัดจากขอบขวาสุดหรือขอบซ้ายสุด</string>
    <string name="back_gesture_feedback_cancelled">ต้องแน่ใจว่าคุณปัดจากขอบขวาหรือขอบซ้ายไปตรงกลางหน้าจอแล้วปล่อย</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">คุณได้เรียนรู้วิธีการปัดนิ้วจากด้านขวาเพื่อย้อนกลับแล้ว ต่อไปจะเรียนรู้วิธีการสลับแอป</string>
    <string name="back_gesture_feedback_complete_with_follow_up">คุณทำท่าทางเพื่อย้อนกลับเสร็จแล้ว ต่อไปให้เรียนรู้วิธีการสลับแอป</string>
    <string name="back_gesture_feedback_complete_without_follow_up">คุณทำท่าทางเพื่อย้อนกลับเสร็จแล้ว</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">ต้องแน่ใจว่าคุณไม่ได้ปัดใกล้กับด้านล่างสุดของหน้าจอ</string>
    <string name="back_gesture_tutorial_confirm_subtitle">หากต้องการเปลี่ยนความไวของท่าทางย้อนกลับ ให้ไปที่การตั้งค่า</string>
    <string name="back_gesture_intro_title">ปัดขึ้นเพื่อย้อนกลับ</string>
    <string name="back_gesture_intro_subtitle">หากต้องการกลับไปที่หน้าจอล่าสุด ให้ปัดนิ้วจากขอบซ้ายหรือขอบขวาไปตรงกลางหน้าจอ</string>
    <string name="back_gesture_spoken_intro_subtitle">หากต้องการกลับไปที่หน้าจอล่าสุด ให้ใช้ 2 นิ้วปัดจากขอบซ้ายหรือขอบขวาไปตรงกลางหน้าจอ</string>
    <string name="back_gesture_tutorial_title">ย้อนกลับ</string>
    <string name="back_gesture_tutorial_subtitle">ปัดจากขอบด้านซ้ายหรือขวาไปที่ตรงกลางหน้าจอ</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">ต้องแน่ใจว่าคุณปัดจากขอบล่างของหน้าจอ</string>
    <string name="home_gesture_feedback_overview_detected">ต้องแน่ใจว่าคุณไม่ได้หยุดพักก่อนที่จะปล่อยไป</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">ต้องแน่ใจว่าคุณปัดนิ้วขึ้นตรงๆ</string>
    <string name="home_gesture_feedback_complete_with_follow_up">คุณทำท่าทางเพื่อไปยังหน้าหลักเสร็จแล้ว ต่อไปให้เรียนรู้วิธีการย้อนกลับ</string>
    <string name="home_gesture_feedback_complete_without_follow_up">คุณทำท่าทางเพื่อไปยังหน้าหลักเสร็จแล้ว</string>
    <string name="home_gesture_intro_title">ปัดขึ้นเพื่อไปที่หน้าหลัก</string>
    <string name="home_gesture_intro_subtitle">ปัดนิ้วขึ้นจากด้านล่างของหน้าจอ ซึ่งจะเป็นการพาคุณกลับไปที่หน้าจอหลัก</string>
    <string name="home_gesture_spoken_intro_subtitle">ใช้ 2 นิ้วปัดจากด้านล่างของหน้าจอ ซึ่งจะเป็นการพาคุณกลับไปที่หน้าจอหลัก</string>
    <string name="home_gesture_tutorial_title">ไปหน้าหลัก</string>
    <string name="home_gesture_tutorial_subtitle">ปัดขึ้นจากด้านล่างของหน้าจอ</string>
    <string name="home_gesture_tutorial_success">ทำได้ดีมาก!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">ต้องแน่ใจว่าคุณปัดจากขอบล่างของหน้าจอ</string>
    <string name="overview_gesture_feedback_home_detected">ลองแตะหน้าต่างค้างไว้นานขึ้นก่อนที่จะปล่อย</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">ต้องแน่ใจว่าคุณปัดนิ้วขึ้นตรงๆ แล้วหยุด</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">คุณได้เรียนรู้วิธีการใช้ท่าทางแล้ว หากต้องการปิดใช้ท่าทาง ให้ไปที่การตั้งค่า</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">คุณทำท่าทางเพื่อสลับแอปเสร็จแล้ว</string>
    <string name="overview_gesture_intro_title">ปัดเพื่อสลับแอป</string>
    <string name="overview_gesture_intro_subtitle">หากต้องการสลับไปมาระหว่างแอปต่างๆ ให้ปัดขึ้นจากด้านล่างของหน้าจอค้างไว้ แล้วปล่อย</string>
    <string name="overview_gesture_spoken_intro_subtitle">หากต้องการสลับไปมาระหว่างแอปต่างๆ ให้ใช้ 2 นิ้วลากขึ้นจากด้านล่างของหน้าจอค้างไว้ แล้วปล่อย</string>
    <string name="overview_gesture_tutorial_title">สลับแอป</string>
    <string name="overview_gesture_tutorial_subtitle">ปัดขึ้นจากด้านล่างของหน้าจอ ค้างไว้ แล้วปล่อย</string>
    <string name="overview_gesture_tutorial_success">เยี่ยมไปเลย!</string>
    <string name="gesture_tutorial_confirm_title">ตั้งค่าทั้งหมดแล้ว</string>
    <string name="gesture_tutorial_action_button_label">เสร็จสิ้น</string>
    <string name="gesture_tutorial_action_button_label_settings">การตั้งค่า</string>
    <string name="gesture_tutorial_try_again">ลองอีกครั้ง</string>
    <string name="gesture_tutorial_nice">เยี่ยม!</string>
    <string name="gesture_tutorial_step">การสอนใช้งาน <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">ตั้งค่าทั้งหมดแล้ว!</string>
    <string name="allset_hint">ปัดขึ้นเพื่อไปที่หน้าหลัก</string>
    <string name="allset_button_hint">แตะปุ่มโฮมเพื่อไปที่หน้าจอเริ่มต้น</string>
    <string name="allset_description_generic">คุณพร้อมที่จะเริ่มใช้ <xliff:g id="device" example="Pixel 6">%1$s</xliff:g> ของคุณแล้ว</string>
    <string name="default_device_name">อุปกรณ์</string>
    <string name="allset_navigation_settings"><annotation id="link">การตั้งค่าการนำทางของระบบ</annotation></string>
    <string name="action_share">แชร์</string>
    <string name="action_screenshot">ภาพหน้าจอ</string>
    <string name="action_split">แยก</string>
    <string name="action_save_app_pair">บันทึกการจับคู่แอป</string>
    <string name="toast_split_select_app">แตะแอปอีกแอปหนึ่งเพื่อใช้การแบ่งหน้าจอ</string>
    <string name="toast_contextual_split_select_app">เลือกแอปอีกแอปหนึ่งเพื่อใช้การแบ่งหน้าจอ</string>
    <string name="toast_split_select_app_cancel"><b>ยกเลิก</b></string>
    <string name="toast_split_select_cont_desc">ออกจากการเลือกการแบ่งหน้าจอ</string>
    <string name="toast_split_app_unsupported">เลือกแอปอีกแอปหนึ่งเพื่อใช้การแบ่งหน้าจอ</string>
    <string name="blocked_by_policy">แอปหรือองค์กรของคุณไม่อนุญาตการดำเนินการนี้</string>
    <string name="split_widgets_not_supported">ไม่รองรับวิดเจ็ตในขณะนี้ โปรดเลือกแอปอื่น</string>
    <string name="skip_tutorial_dialog_title">ข้ามการสอนใช้งานการนำทางหรือไม่</string>
    <string name="skip_tutorial_dialog_subtitle">คุณสามารถค้นหาสิ่งนี้ได้ภายหลังในแอป <xliff:g id="name">%1$s</xliff:g></string>
    <string name="gesture_tutorial_action_button_label_cancel">ยกเลิก</string>
    <string name="gesture_tutorial_action_button_label_skip">ข้าม</string>
    <string name="accessibility_rotate_button">หมุนหน้าจอ</string>
    <string name="taskbar_edu_a11y_title">การศึกษาแถบงาน</string>
    <string name="taskbar_edu_splitscreen">ลากแอปหนึ่งแอปไปด้านข้างเพื่อใช้ 2 แอปพร้อมกัน</string>
    <string name="taskbar_edu_stashing">ลากนิ้วขึ้นช้าๆ เพื่อแสดงแถบงาน</string>
    <string name="taskbar_edu_suggestions">รับการแนะนำแอปตามกิจวัตรประจำของคุณ</string>
    <string name="taskbar_edu_pinning">กดค้างที่เส้นแบ่งเพื่อปักหมุดแถบงาน</string>
    <string name="taskbar_edu_features">ทำได้มากขึ้นบนแถบงาน</string>
    <string name="taskbar_edu_pinning_title">แสดงแถบงานเสมอ</string>
    <string name="taskbar_edu_pinning_standalone">หากต้องการให้แสดงแถบงานที่ด้านล่างของหน้าจอ ให้กดที่เส้นแบ่งค้างไว้</string>
    <string name="taskbar_search_edu_title">กดค้างที่ปุ่มดำเนินการเพื่อค้นหาสิ่งที่อยู่บนหน้าจอของคุณ</string>
    <string name="taskbar_edu_search_disclosure">ผลิตภัณฑ์นี้ใช้ส่วนที่เลือกไว้บนหน้าจอของคุณในการค้นหา <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>นโยบายความเป็นส่วนตัว<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> และ <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>เงื่อนไขการให้บริการ<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> ของ Google จะมีผลใช้งาน</string>
    <string name="taskbar_edu_close">ปิด</string>
    <string name="taskbar_edu_done">เสร็จสิ้น</string>
    <string name="taskbar_button_home">หน้าหลัก</string>
    <string name="taskbar_button_a11y">การเข้าใช้งาน</string>
    <string name="taskbar_button_back">ย้อนกลับ</string>
    <string name="taskbar_button_ime_switcher">ตัวสลับ IME</string>
    <string name="taskbar_button_recents">ล่าสุด</string>
    <string name="taskbar_button_notifications">การแจ้งเตือน</string>
    <string name="taskbar_button_quick_settings">การตั้งค่าด่วน</string>
    <string name="taskbar_a11y_title">แถบงาน</string>
    <string name="taskbar_a11y_shown_title">แถบงานที่แสดง</string>
    <string name="taskbar_a11y_hidden_title">แถบงานที่ซ่อน</string>
    <string name="taskbar_phone_a11y_title">แถบนำทาง</string>
    <string name="always_show_taskbar">แสดงแถบงานเสมอ</string>
    <string name="change_navigation_mode">เปลี่ยนโหมดการนำทาง</string>
    <string name="taskbar_divider_a11y_title">เส้นแบ่งแถบงาน</string>
    <string name="move_drop_target_top_or_left">เลื่อนไปด้านบน/ซ้าย</string>
    <string name="move_drop_target_bottom_or_right">เลื่อนไปด้านล่าง/ขวา</string>
    <string name="quick_switch_overflow">"{count,plural, =1{แสดงเพิ่มเติมอีก # แอป}other{แสดงเพิ่มเติมอีก # แอป}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{แสดงแอปบนเดสก์ท็อป # รายการ}other{แสดงแอปบนเดสก์ท็อป # รายการ}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> และ <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">กรอบคำพูด</string>
    <string name="bubble_bar_overflow_description">เมนูเพิ่มเติม</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> จาก <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> และอีก <xliff:g id="bubble_count" example="4">%2$d</xliff:g> รายการ</string>
    <string name="app_name">Launcher3</string>
</resources>