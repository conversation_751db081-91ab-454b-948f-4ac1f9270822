<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Pin</string>
    <string name="recent_task_option_freeform">Freeform</string>
    <string name="recent_task_option_desktop">Desktop</string>
    <string name="recents_empty_message">No recent items</string>
    <string name="accessibility_app_usage_settings">App usage settings</string>
    <string name="recents_clear_all">Clear all</string>
    <string name="accessibility_recent_apps">Recent apps</string>
    <string name="task_view_closed">Task closed</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minute</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> left today</string>
    <string name="title_app_suggestions">App suggestions</string>
    <string name="all_apps_prediction_tip">Your predicted apps</string>
    <string name="hotseat_edu_title_migrate">Get app suggestions on the bottom row of your Home screen</string>
    <string name="hotseat_edu_title_migrate_landscape">Get app suggestions on favorites row of your Home screen</string>
    <string name="hotseat_edu_message_migrate">Easily access your most-used apps right on the Home screen. Suggestions will change based on your routines. Apps on the bottom row will move up to your Home screen.</string>
    <string name="hotseat_edu_message_migrate_landscape">Easily access your most-used apps right on the Home screen. Suggestions will change based on your routines. Apps in favorites row will move to your Home screen.</string>
    <string name="hotseat_edu_accept">Get app suggestions</string>
    <string name="hotseat_edu_dismiss">No thanks</string>
    <string name="hotseat_prediction_settings">Settings</string>
    <string name="hotseat_auto_enrolled">Most-used apps appear here, and change based on routines</string>
    <string name="hotseat_tip_no_empty_slots">Drag apps off the bottom row to get app suggestions</string>
    <string name="hotseat_tip_gaps_filled">App suggestions added to empty space</string>
    <string name="hotsaet_tip_prediction_enabled">App suggestions enabled</string>
    <string name="hotsaet_tip_prediction_disabled">App suggestions are disabled</string>
    <string name="hotseat_prediction_content_description">Predicted app: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Rotate your device</string>
    <string name="gesture_tutorial_rotation_prompt">Rotate your device to complete the gesture navigation tutorial</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Make sure you swipe from the far-right or far-left edge</string>
    <string name="back_gesture_feedback_cancelled">Make sure you swipe from the right or left edge to the middle of the screen and let go</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">You learned how to swipe from the right to go back. Next up, learn how to switch apps.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">You completed the go back gesture. Next up, learn how to switch apps.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">You completed the go back gesture</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Make sure you don\'t swipe too close to the bottom of the screen</string>
    <string name="back_gesture_tutorial_confirm_subtitle">To change the sensitivity of the back gesture, go to Settings.</string>
    <string name="back_gesture_intro_title">Swipe to go back</string>
    <string name="back_gesture_intro_subtitle">To go back to the last screen, swipe from the left or right edge to the middle of the screen.</string>
    <string name="back_gesture_spoken_intro_subtitle">To go back to the last screen, swipe with 2 fingers from the left or right edge to the middle of the screen.</string>
    <string name="back_gesture_tutorial_title">Go back</string>
    <string name="back_gesture_tutorial_subtitle">Swipe from the left or right edge to the middle of the screen</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Make sure you swipe up from the bottom edge of the screen</string>
    <string name="home_gesture_feedback_overview_detected">Make sure you don\'t pause before letting go</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Make sure you swipe straight up</string>
    <string name="home_gesture_feedback_complete_with_follow_up">You completed the go Home gesture. Next up, learn how to go back.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">You completed the go Home gesture</string>
    <string name="home_gesture_intro_title">Swipe to go Home</string>
    <string name="home_gesture_intro_subtitle">Swipe up from the bottom of your screen. This gesture always takes you to the Home screen.</string>
    <string name="home_gesture_spoken_intro_subtitle">Swipe up with 2 fingers from the bottom of the screen. This gesture always takes you to the Home screen.</string>
    <string name="home_gesture_tutorial_title">Go Home</string>
    <string name="home_gesture_tutorial_subtitle">Swipe up from the bottom of your screen</string>
    <string name="home_gesture_tutorial_success">Great job!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Make sure you swipe up from the bottom edge of the screen</string>
    <string name="overview_gesture_feedback_home_detected">Try holding the window for longer before releasing</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Make sure you swipe straight up, then pause</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">You learned how to use gestures. To turn off gestures, go to Settings.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">You completed the switch apps gesture</string>
    <string name="overview_gesture_intro_title">Swipe to switch apps</string>
    <string name="overview_gesture_intro_subtitle">To switch between apps, swipe up from the bottom of your screen, hold, then release.</string>
    <string name="overview_gesture_spoken_intro_subtitle">To switch between apps, swipe up with 2 fingers from the bottom of your screen, hold, then release.</string>
    <string name="overview_gesture_tutorial_title">Switch apps</string>
    <string name="overview_gesture_tutorial_subtitle">Swipe up from the bottom of your screen, hold, then release</string>
    <string name="overview_gesture_tutorial_success">Well done!</string>
    <string name="gesture_tutorial_confirm_title">All set</string>
    <string name="gesture_tutorial_action_button_label">Done</string>
    <string name="gesture_tutorial_action_button_label_settings">Settings</string>
    <string name="gesture_tutorial_try_again">Try again</string>
    <string name="gesture_tutorial_nice">Nice!</string>
    <string name="gesture_tutorial_step">Tutorial <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">All set!</string>
    <string name="allset_hint">Swipe up to go Home</string>
    <string name="allset_button_hint">Touch the Home button to go to your Home screen</string>
    <string name="allset_description_generic">You\'re ready to start using your <xliff:g id="device" example="Pixel 6">%1$s</xliff:g></string>
    <string name="default_device_name">device</string>
    <string name="allset_navigation_settings"><annotation id="link">System navigation settings</annotation></string>
    <string name="action_share">Share</string>
    <string name="action_screenshot">Screenshot</string>
    <string name="action_split">Split</string>
    <string name="action_save_app_pair">Save app pair</string>
    <string name="toast_split_select_app">Touch another app to use split screen</string>
    <string name="toast_contextual_split_select_app">Choose another app to use split screen</string>
    <string name="toast_split_select_app_cancel"><b>Cancel</b></string>
    <string name="toast_split_select_cont_desc">Exit split screen selection</string>
    <string name="toast_split_app_unsupported">Choose another app to use split screen</string>
    <string name="blocked_by_policy">This action isn\'t allowed by the app or your organization</string>
    <string name="split_widgets_not_supported">Widgets currently unsupported. Select another app.</string>
    <string name="skip_tutorial_dialog_title">Skip navigation tutorial?</string>
    <string name="skip_tutorial_dialog_subtitle">You can find this later in the <xliff:g id="name">%1$s</xliff:g> app</string>
    <string name="gesture_tutorial_action_button_label_cancel">Cancel</string>
    <string name="gesture_tutorial_action_button_label_skip">Skip</string>
    <string name="accessibility_rotate_button">Rotate screen</string>
    <string name="taskbar_edu_a11y_title">Taskbar education</string>
    <string name="taskbar_edu_splitscreen">Drag an app to the side to use 2 apps at once</string>
    <string name="taskbar_edu_stashing">Slow-swipe up to show the taskbar</string>
    <string name="taskbar_edu_suggestions">Get app suggestions based on your routine</string>
    <string name="taskbar_edu_pinning">Long press on the divider to pin the taskbar</string>
    <string name="taskbar_edu_features">Do more with the taskbar</string>
    <string name="taskbar_edu_pinning_title">Always show the taskbar</string>
    <string name="taskbar_edu_pinning_standalone">To always show the taskbar on the bottom of your screen, long press the divider.</string>
    <string name="taskbar_search_edu_title">Long press the action key to search what\'s on your screen</string>
    <string name="taskbar_edu_search_disclosure">This product uses the selected part of your screen to search. Google\'s <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Privacy Policy<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> and <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Terms of Service<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> apply.</string>
    <string name="taskbar_edu_close">Close</string>
    <string name="taskbar_edu_done">Done</string>
    <string name="taskbar_button_home">Home</string>
    <string name="taskbar_button_a11y">Accessibility</string>
    <string name="taskbar_button_back">Back</string>
    <string name="taskbar_button_ime_switcher">IME switcher</string>
    <string name="taskbar_button_recents">Recents</string>
    <string name="taskbar_button_notifications">Notifications</string>
    <string name="taskbar_button_quick_settings">Quick Settings</string>
    <string name="taskbar_a11y_title">Taskbar</string>
    <string name="taskbar_a11y_shown_title">Taskbar shown</string>
    <string name="taskbar_a11y_hidden_title">Taskbar hidden</string>
    <string name="taskbar_phone_a11y_title">Navigation bar</string>
    <string name="always_show_taskbar">Always show taskbar</string>
    <string name="change_navigation_mode">Change navigation mode</string>
    <string name="taskbar_divider_a11y_title">Taskbar divider</string>
    <string name="move_drop_target_top_or_left">Move to top/left</string>
    <string name="move_drop_target_bottom_or_right">Move to bottom/right</string>
    <string name="quick_switch_overflow">{count, plural, =1 {Show # more app.} other {Show # more apps.}}</string>
    <string name="quick_switch_desktop">{count, plural, =1 {Show # desktop app.} other {Show # desktop apps.}}</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> and <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Bubble</string>
    <string name="bubble_bar_overflow_description">Overflow</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> from <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> and <xliff:g id="bubble_count" example="4">%2$d</xliff:g> more</string>
    <string name="app_name">Launcher3</string>
</resources>