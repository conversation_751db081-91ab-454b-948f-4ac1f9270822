<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">"固定"</string>
    <string name="recent_task_option_freeform">"自由窗口"</string>
    <string name="recent_task_option_desktop">"桌面"</string>
    <string name="recents_empty_message">"近期没有任何内容"</string>
    <string name="accessibility_app_usage_settings">"应用使用设置"</string>
    <string name="recents_clear_all">"全部清除"</string>
    <string name="accessibility_recent_apps">"最近用过的应用"</string>
    <string name="task_view_closed">"任务已关闭"</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">"不到 1 分钟"</string>
    <string name="time_left_for_app">"今天还可使用 <xliff:g id="TIME">%1$s</xliff:g>"</string>
    <string name="title_app_suggestions">"应用建议"</string>
    <string name="all_apps_prediction_tip">"您可能想要使用的应用"</string>
    <string name="hotseat_edu_title_migrate">"在主屏幕底部获取应用建议"</string>
    <string name="hotseat_edu_title_migrate_landscape">"在主屏幕的收藏行获取应用建议"</string>
    <string name="hotseat_edu_message_migrate">"直接在主屏幕上轻松访问您最常用的应用。系统会根据您的日常安排提供不同的建议。最下面一排中的应用会向上移到主屏幕中。"</string>
    <string name="hotseat_edu_message_migrate_landscape">"直接在主屏幕上轻松访问您最常用的应用。建议会因您的日常安排而变化，收藏行中的应用将移到主屏幕上。"</string>
    <string name="hotseat_edu_accept">"获取应用建议"</string>
    <string name="hotseat_edu_dismiss">"不用了"</string>
    <string name="hotseat_prediction_settings">"设置"</string>
    <string name="hotseat_auto_enrolled">"最常用的应用会显示在此处，显示的项目会根据日常安排而发生变化"</string>
    <string name="hotseat_tip_no_empty_slots">"将应用拖离底部，以获取应用建议"</string>
    <string name="hotseat_tip_gaps_filled">"应用建议已添加到空白区域"</string>
    <string name="hotsaet_tip_prediction_enabled">"已启用应用建议"</string>
    <string name="hotsaet_tip_prediction_disabled">"已停用应用建议"</string>
    <string name="hotseat_prediction_content_description">"预测的应用：<xliff:g id="TITLE">%1$s</xliff:g>"</string>
    <string name="gesture_tutorial_rotation_prompt_title">"请旋转设备"</string>
    <string name="gesture_tutorial_rotation_prompt">"请旋转设备，完成手势导航教程"</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">"确保从最右侧或最左侧边缘开始滑动"</string>
    <string name="back_gesture_feedback_cancelled">"确保从右侧或左侧边缘滑动到屏幕中间位置后再松开手指"</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">"您已了解如何使用“从右侧向左滑动”手势返回。接下来学习切换应用吧！"</string>
    <string name="back_gesture_feedback_complete_with_follow_up">"您完成了“返回”手势教程。接下来了解如何切换应用。"</string>
    <string name="back_gesture_feedback_complete_without_follow_up">"您完成了“返回”手势"</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">"确保滑动时手的位置不要太靠近屏幕底部"</string>
    <string name="back_gesture_tutorial_confirm_subtitle">"如要调节“返回”手势的灵敏度，请转到“设置”"</string>
    <string name="back_gesture_intro_title">"滑动即可返回"</string>
    <string name="back_gesture_intro_subtitle">"如要返回上一个屏幕，请从屏幕左侧或右侧边缘往屏幕中间滑动。"</string>
    <string name="back_gesture_spoken_intro_subtitle">"若要返回上一个屏幕，请用两根手指从屏幕左侧或右侧边缘向中间滑动。"</string>
    <string name="back_gesture_tutorial_title">"返回"</string>
    <string name="back_gesture_tutorial_subtitle">"从屏幕左侧或右侧边缘滑动到中间"</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">"确保从屏幕底部边缘向上滑动"</string>
    <string name="home_gesture_feedback_overview_detected">"松开手指前，请勿中途停顿"</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">"确保笔直向上滑动"</string>
    <string name="home_gesture_feedback_complete_with_follow_up">"您完成了“转到主屏幕”手势。接下来了解如何返回。"</string>
    <string name="home_gesture_feedback_complete_without_follow_up">"您完成了“转到主屏幕”手势"</string>
    <string name="home_gesture_intro_title">"上滑可转到主屏幕"</string>
    <string name="home_gesture_intro_subtitle">"从屏幕底部向上滑动，即可随时回到主屏幕。"</string>
    <string name="home_gesture_spoken_intro_subtitle">"用两根手指从屏幕底部向上滑动，这个手势会一律使您回到主屏幕。"</string>
    <string name="home_gesture_tutorial_title">"前往主屏幕"</string>
    <string name="home_gesture_tutorial_subtitle">"从屏幕底部向上滑动"</string>
    <string name="home_gesture_tutorial_success">"太棒了！"</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">"确保从屏幕底部边缘向上滑动"</string>
    <string name="overview_gesture_feedback_home_detected">"尝试按住窗口较长时间，然后再松开手指"</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">"确保笔直向上滑动，然后停住"</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">"您已了解如何使用手势了。如要关闭手势，请前往“设置”。"</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">"您完成了应用切换手势"</string>
    <string name="overview_gesture_intro_title">"滑动即可切换应用"</string>
    <string name="overview_gesture_intro_subtitle">"如需在应用之间切换，请从屏幕底部向上滑动，按住，然后松开。"</string>
    <string name="overview_gesture_spoken_intro_subtitle">"如需在应用之间切换，请从屏幕底部向上滑动，按住，然后松开。"</string>
    <string name="overview_gesture_tutorial_title">"切换应用"</string>
    <string name="overview_gesture_tutorial_subtitle">"从屏幕底部向上滑动后按住，然后松开"</string>
    <string name="overview_gesture_tutorial_success">"恭喜！"</string>
    <string name="gesture_tutorial_confirm_title">"大功告成"</string>
    <string name="gesture_tutorial_action_button_label">"完成"</string>
    <string name="gesture_tutorial_action_button_label_settings">"设置"</string>
    <string name="gesture_tutorial_try_again">"重试"</string>
    <string name="gesture_tutorial_nice">"很好！"</string>
    <string name="gesture_tutorial_step">"教程 <xliff:g id="CURRENT">%1$d</xliff:g>/<xliff:g id="TOTAL">%2$d</xliff:g>"</string>
    <string name="allset_title">"大功告成！"</string>
    <string name="allset_hint">"向上滑动可前往主屏幕"</string>
    <string name="allset_button_hint">"点按主屏幕按钮即可前往主屏幕"</string>
    <string name="allset_description_generic">"您可以开始使用<xliff:g id="DEVICE">%1$s</xliff:g>了"</string>
    <string name="default_device_name">"设备"</string>
    <string name="allset_navigation_settings"><annotation id="link">"系统导航设置"</annotation></string>
    <string name="action_share">"分享"</string>
    <string name="action_screenshot">"屏幕截图"</string>
    <string name="action_split">"分屏"</string>
    <string name="action_save_app_pair">"保存应用组合"</string>
    <string name="toast_split_select_app">"点按另一个应用即可使用分屏"</string>
    <string name="toast_contextual_split_select_app">"另外选择一个应用才可使用分屏模式"</string>
    <string name="toast_split_select_app_cancel"><b>"取消"</b></string>
    <string name="toast_split_select_cont_desc">"退出分屏选择模式"</string>
    <string name="toast_split_app_unsupported">"另外选择一个应用才可使用分屏模式"</string>
    <string name="blocked_by_policy">"该应用或您所在的单位不允许执行此操作"</string>
    <string name="split_widgets_not_supported">"目前不支持微件，请选择其他应用"</string>
    <string name="skip_tutorial_dialog_title">"要跳过导航教程吗？"</string>
    <string name="skip_tutorial_dialog_subtitle">"您之后可以在“<xliff:g id="NAME">%1$s</xliff:g>”应用中找到此教程"</string>
    <string name="gesture_tutorial_action_button_label_cancel">"取消"</string>
    <string name="gesture_tutorial_action_button_label_skip">"跳过"</string>
    <string name="accessibility_rotate_button">"旋转屏幕"</string>
    <string name="taskbar_edu_a11y_title">"任务栏教程"</string>
    <string name="taskbar_edu_splitscreen">"将一个应用拖到一侧，即可同时使用两个应用"</string>
    <string name="taskbar_edu_stashing">"缓慢上滑即可显示任务栏"</string>
    <string name="taskbar_edu_suggestions">"根据您的日常使用习惯获得应用建议"</string>
    <string name="taskbar_edu_pinning">"长按分隔线即可固定任务栏"</string>
    <string name="taskbar_edu_features">"体验任务栏的更多功能"</string>
    <string name="taskbar_edu_pinning_title">"始终显示任务栏"</string>
    <string name="taskbar_edu_pinning_standalone">"若要始终在屏幕底部显示任务栏，请轻触并按住分隔线"</string>
    <string name="taskbar_search_edu_title">"轻触并按住操作键，即可根据屏幕上的内容进行搜索"</string>
    <string name="taskbar_edu_search_disclosure">"此产品会根据屏幕上的所选内容进行搜索。Google 的<xliff:g id="BEGIN_PRIVACY_LINK">&lt;a href="%1$s"&gt;</xliff:g>《隐私权政策》<xliff:g id="END_PRIVACY_LINK">&lt;/a&gt;</xliff:g>和<xliff:g id="BEGIN_TOS_LINK">&lt;a href="%2$s"&gt;</xliff:g>《服务条款》<xliff:g id="END_TOS_LINK">&lt;/a&gt;</xliff:g>适用。"</string>
    <string name="taskbar_edu_close">"关闭"</string>
    <string name="taskbar_edu_done">"完成"</string>
    <string name="taskbar_button_home">"主屏幕"</string>
    <string name="taskbar_button_a11y">"无障碍功能"</string>
    <string name="taskbar_button_back">"返回"</string>
    <string name="taskbar_button_ime_switcher">"IME 切换器"</string>
    <string name="taskbar_button_recents">"最近用过"</string>
    <string name="taskbar_button_notifications">"通知"</string>
    <string name="taskbar_button_quick_settings">"快捷设置"</string>
    <string name="taskbar_a11y_title">"任务栏"</string>
    <string name="taskbar_a11y_shown_title">"任务栏已显示"</string>
    <string name="taskbar_a11y_hidden_title">"任务栏已隐藏"</string>
    <string name="taskbar_phone_a11y_title">"导航栏"</string>
    <string name="always_show_taskbar">"始终显示任务栏"</string>
    <string name="change_navigation_mode">"更改导航模式"</string>
    <string name="taskbar_divider_a11y_title">"任务栏分隔线"</string>
    <string name="move_drop_target_top_or_left">"移到顶部/左侧"</string>
    <string name="move_drop_target_bottom_or_right">"移到底部/右侧"</string>
    <string name="quick_switch_overflow">"{count,plural, =1{显示另外 # 个应用。}other{显示另外 # 个应用。}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{显示 # 款桌面应用。}other{显示 # 款桌面应用。}}"</string>
    <string name="quick_switch_split_task">"<xliff:g id="APP_NAME_1">%1$s</xliff:g>和<xliff:g id="APP_NAME_2">%2$s</xliff:g>"</string>
    <string name="bubble_bar_bubble_fallback_description">"气泡框"</string>
    <string name="bubble_bar_overflow_description">"溢出式气泡框"</string>
    <string name="bubble_bar_bubble_description">"来自“<xliff:g id="APP_NAME">%2$s</xliff:g>”的<xliff:g id="NOTIFICATION_TITLE">%1$s</xliff:g>"</string>
    <string name="bubble_bar_description_multiple_bubbles">"<xliff:g id="BUBBLE_BAR_BUBBLE_DESCRIPTION">%1$s</xliff:g>以及另外 <xliff:g id="BUBBLE_COUNT">%2$d</xliff:g> 个"</string>
    <string name="app_name">Launcher3</string>
</resources>