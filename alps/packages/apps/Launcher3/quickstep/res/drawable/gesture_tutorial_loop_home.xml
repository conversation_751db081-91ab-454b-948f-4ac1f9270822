<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <target android:name="_R_G_L_1_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="1000"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0.25"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="1000"
                    android:propertyName="fillAlpha"
                    android:startOffset="1000"
                    android:valueFrom="0.75"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="850"
                    android:valueFrom="1"
                    android:valueTo="0"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="time_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="2000"
                    android:propertyName="translateX"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <aapt:attr name="android:drawable">
        <vector
            android:width="412dp"
            android:height="892dp"
            android:viewportHeight="892"
            android:viewportWidth="412">
            <group android:name="_R_G">
                <group
                    android:name="_R_G_L_1_G"
                    android:translateX="206"
                    android:translateY="879.5">
                    <path
                        android:name="_R_G_L_1_G_D_0_P_0"
                        android:fillAlpha="0.25"
                        android:fillColor="?android:attr/colorAccent"
                        android:fillType="nonZero"
                        android:pathData=" M206 -12.5 C206,-12.5 206,12.5 206,12.5 C206,12.5 -206,12.5 -206,12.5 C-206,12.5 -206,-12.5 -206,-12.5 C-206,-12.5 206,-12.5 206,-12.5c " />
                </group>
                <group
                    android:name="_R_G_L_0_G"
                    android:translateX="206"
                    android:translateY="446" />
            </group>
            <group android:name="time_group" />
        </vector>
    </aapt:attr>
</animated-vector>