<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="recent_task_option_pin">Vastmaken</string>
    <string name="recent_task_option_freeform">Vrije vorm</string>
    <string name="recent_task_option_desktop">Desktop</string>
    <string name="recents_empty_message">Geen recente items</string>
    <string name="accessibility_app_usage_settings">Instellingen voor app-gebruik</string>
    <string name="recents_clear_all">Alles wissen</string>
    <string name="accessibility_recent_apps">Recente apps</string>
    <string name="task_view_closed">Taak gesloten</string>
    <string name="task_contents_description_with_remaining_time"><xliff:g id="task_description" example="GMail">%1$s</xliff:g>, <xliff:g id="remaining_time" example="7 minutes left today">%2$s</xliff:g></string>
    <string name="shorter_duration_less_than_one_minute">&lt; 1 minuut</string>
    <string name="time_left_for_app"><xliff:g id="time" example="7 minutes">%1$s</xliff:g> resterend vandaag</string>
    <string name="title_app_suggestions">App-suggesties</string>
    <string name="all_apps_prediction_tip">Uw voorspelde apps</string>
    <string name="hotseat_edu_title_migrate">App-suggesties ontvangen op de onderste rij op uw Startscherm</string>
    <string name="hotseat_edu_title_migrate_landscape">App-suggesties ontvangen op de rij met favorieten op uw Startscherm</string>
    <string name="hotseat_edu_message_migrate">Open eenvoudig uw meest gebruikte apps, rechtstreeks op het Startscherm. Suggesties veranderen op basis van uw routines. Apps op de onderste rij schuiven op naar uw Startscherm.</string>
    <string name="hotseat_edu_message_migrate_landscape">Open eenvoudig uw meestgebruikte apps op het Startscherm. De suggesties variëren op basis van uw gewoonten. Apps in de rij met favorieten worden naar uw Startscherm verplaatst.</string>
    <string name="hotseat_edu_accept">App-suggesties ontvangen</string>
    <string name="hotseat_edu_dismiss">Nee, bedankt.</string>
    <string name="hotseat_prediction_settings">Instellingen</string>
    <string name="hotseat_auto_enrolled">De meest gebruikte apps verschijnen hier en veranderen op basis van routines</string>
    <string name="hotseat_tip_no_empty_slots">Sleep apps uit de onderste rij om app-suggesties te ontvangen</string>
    <string name="hotseat_tip_gaps_filled">App-suggesties aan lege ruimte toegevoegd</string>
    <string name="hotsaet_tip_prediction_enabled">App-suggesties ingeschakeld</string>
    <string name="hotsaet_tip_prediction_disabled">App-suggesties zijn uitgeschakeld</string>
    <string name="hotseat_prediction_content_description">Voorspelde app: <xliff:g id="title" example="Chrome">%1$s</xliff:g></string>
    <string name="gesture_tutorial_rotation_prompt_title">Uw apparaat draaien</string>
    <string name="gesture_tutorial_rotation_prompt">Draai uw apparaat om de handleiding voor navigatie met gebaren te voltooien</string>
    <string name="back_gesture_feedback_swipe_too_far_from_edge">Zorg ervoor dat u van de uiterste rechter- naar de uiterste linkerrand veegt</string>
    <string name="back_gesture_feedback_cancelled">Zorg ervoor dat u van de rechter- of linkerrand naar het midden van het scherm veegt en laat dan los</string>
    <string name="back_gesture_feedback_complete_with_overview_follow_up">U hebt geleerd hoe u naar rechts kunt vegen om terug te gaan. Ontdek nu hoe u van app kunt wisselen.</string>
    <string name="back_gesture_feedback_complete_with_follow_up">U hebt het teruggaan-gebaar voltooid. U gaat nu leren hoe u tussen apps kunt wisselen.</string>
    <string name="back_gesture_feedback_complete_without_follow_up">U hebt het teruggaan-gebaar voltooid</string>
    <string name="back_gesture_feedback_swipe_in_nav_bar">Zorg ervoor dat u niet te dicht bij de onderkant van het scherm veegt</string>
    <string name="back_gesture_tutorial_confirm_subtitle">Ga naar Instellingen om de gevoeligheid van het teruggaan-gebaar aan te passen.</string>
    <string name="back_gesture_intro_title">Veeg om terug te gaan</string>
    <string name="back_gesture_intro_subtitle">Om terug te gaan naar het laatste scherm, veegt u van de linker- of rechterrand naar het midden van het scherm.</string>
    <string name="back_gesture_spoken_intro_subtitle">Om terug te gaan naar het laatste scherm, veegt u met 2 vingers van de linker- of de rechterrand naar het midden van het scherm.</string>
    <string name="back_gesture_tutorial_title">Ga terug</string>
    <string name="back_gesture_tutorial_subtitle">Veeg van de linker- of rechterrand naar het midden van het scherm</string>
    <string name="home_gesture_feedback_swipe_too_far_from_edge">Zorg ervoor dat u omhoog veegt vanuit de onderste rand van het scherm</string>
    <string name="home_gesture_feedback_overview_detected">Zorg ervoor dat u niet pauzeert voordat u loslaat</string>
    <string name="home_gesture_feedback_wrong_swipe_direction">Zorg ervoor dat u recht omhoog veegt</string>
    <string name="home_gesture_feedback_complete_with_follow_up">U hebt het Naar Startscherm gaan-gebaar voltooid. U gaat nu leren hoe u kunt teruggaan.</string>
    <string name="home_gesture_feedback_complete_without_follow_up">U hebt het Naar Startscherm gaan-gebaar voltooid</string>
    <string name="home_gesture_intro_title">Vegen om naar het Startscherm te gaan</string>
    <string name="home_gesture_intro_subtitle">Veeg omhoog vanaf de onderkant van uw scherm. Dit gebaar brengt u altijd naar het Startscherm.</string>
    <string name="home_gesture_spoken_intro_subtitle">Veeg omhoog met 2 vingers vanaf de onderkant van het scherm. Deze beweging brengt u altijd terug naar het startscherm.</string>
    <string name="home_gesture_tutorial_title">Naar startscherm gaan</string>
    <string name="home_gesture_tutorial_subtitle">Veeg omhoog vanaf de onderkant van uw scherm</string>
    <string name="home_gesture_tutorial_success">Uitstekend!</string>
    <string name="overview_gesture_feedback_swipe_too_far_from_edge">Zorg ervoor dat u omhoog veegt vanuit de onderste rand van het scherm</string>
    <string name="overview_gesture_feedback_home_detected">Houd het venster langer vast voordat u loslaat</string>
    <string name="overview_gesture_feedback_wrong_swipe_direction">Zorg ervoor dat u recht omhoog veegt, pauzeer dan</string>
    <string name="overview_gesture_feedback_complete_with_follow_up">U hebt geleerd hoe u gebaren kunt gebruiken. Ga naar Instellingen om gebaren uit te schakelen.</string>
    <string name="overview_gesture_feedback_complete_without_follow_up">U hebt het Tussen apps schakelen-gebaar voltooid</string>
    <string name="overview_gesture_intro_title">Vegen om tussen apps te schakelen</string>
    <string name="overview_gesture_intro_subtitle">Om tussen apps te schakelen, veegt u vanaf de onderkant van uw scherm omhoog, houdt u het vast en laat u het vervolgens weer los.</string>
    <string name="overview_gesture_spoken_intro_subtitle">Om tussen apps te schakelen, veegt u met twee vingers vanaf de onderkant van uw scherm omhoog, houdt u het vast en laat u het vervolgens weer los.</string>
    <string name="overview_gesture_tutorial_title">Schakelen tussen apps</string>
    <string name="overview_gesture_tutorial_subtitle">Veeg omhoog vanaf de onderkant van uw scherm, houd het vast en laat het vervolgens weer los</string>
    <string name="overview_gesture_tutorial_success">Goed gedaan!</string>
    <string name="gesture_tutorial_confirm_title">Alles is ingesteld</string>
    <string name="gesture_tutorial_action_button_label">Gereed</string>
    <string name="gesture_tutorial_action_button_label_settings">Instellingen</string>
    <string name="gesture_tutorial_try_again">Opnw proberen</string>
    <string name="gesture_tutorial_nice">Geweldig!</string>
    <string name="gesture_tutorial_step">Training <xliff:g id="current">%1$d</xliff:g>/<xliff:g id="total">%2$d</xliff:g></string>
    <string name="allset_title">Klaar!</string>
    <string name="allset_hint">Veeg omhoog om naar het startscherm te gaan</string>
    <string name="allset_button_hint">Raak de startknop aan om terug te gaan naar het startscherm</string>
    <string name="allset_description_generic">U kunt nu uw <xliff:g id="device" example="Pixel 6">%1$s</xliff:g> gaan gebruiken</string>
    <string name="default_device_name">Apparaat</string>
    <string name="allset_navigation_settings"><annotation id="link">Instellingen voor systeemnavigatie</annotation></string>
    <string name="action_share">Delen</string>
    <string name="action_screenshot">Schermafbeelding</string>
    <string name="action_split">Splitsen</string>
    <string name="action_save_app_pair">App-koppeling opslaan</string>
    <string name="toast_split_select_app">Een andere app aanraken om het gesplitste scherm te gebruiken</string>
    <string name="toast_contextual_split_select_app">Een andere app kiezen om het gesplitste scherm te gebruiken</string>
    <string name="toast_split_select_app_cancel"><b>Annuleren</b></string>
    <string name="toast_split_select_cont_desc">Selectie gesplitst scherm verlaten</string>
    <string name="toast_split_app_unsupported">Een andere app kiezen om het gesplitste scherm te gebruiken</string>
    <string name="blocked_by_policy">Deze actie wordt niet toegestaan door de app of uw organisatie</string>
    <string name="split_widgets_not_supported">Widgets worden momenteel niet ondersteund. Selecteer een andere app.</string>
    <string name="skip_tutorial_dialog_title">Navigatietraining overslaan?</string>
    <string name="skip_tutorial_dialog_subtitle">U kunt dit later terugvinden in de <xliff:g id="name">%1$s</xliff:g>-app</string>
    <string name="gesture_tutorial_action_button_label_cancel">Annuleren</string>
    <string name="gesture_tutorial_action_button_label_skip">Overslaan</string>
    <string name="accessibility_rotate_button">Scherm draaien</string>
    <string name="taskbar_edu_a11y_title">Uitleg taakbalk</string>
    <string name="taskbar_edu_splitscreen">Sleep een app naar de zijkant om 2 apps in één keer te gebruiken</string>
    <string name="taskbar_edu_stashing">Veeg langzaam omhoog om de taakbalk weer te geven</string>
    <string name="taskbar_edu_suggestions">Ontvang app-suggesties op basis van uw routine</string>
    <string name="taskbar_edu_pinning">Druk lang op de verdeler om de taakbalk vast te maken</string>
    <string name="taskbar_edu_features">Doe meer met de taakbalk</string>
    <string name="taskbar_edu_pinning_title">Taakbalk altijd tonen</string>
    <string name="taskbar_edu_pinning_standalone">Druk de verdeler lang in om altijd de taakbalk aan de onderkant van uw scherm te laten zien.</string>
    <string name="taskbar_search_edu_title">Druk de actietoets lang in om te zoeken wat er op uw scherm is</string>
    <string name="taskbar_edu_search_disclosure">Dit product gebruikt het geselecteerde deel van uw scherm om te zoeken. Het <xliff:g example="https://policies.google.com/privacy/embedded" id="begin_privacy_link">&lt;a href=\"%1$s\"&gt;</xliff:g>Privacybeleid<xliff:g id="end_privacy_link">&lt;/a&gt;</xliff:g> en de <xliff:g example="https://policies.google.com/terms" id="begin_tos_link">&lt;a href=\"%2$s\"&gt;</xliff:g>Servicevoorwaarden<xliff:g id="end_tos_link">&lt;/a&gt;</xliff:g> van Google zijn van toepassing.</string>
    <string name="taskbar_edu_close">Dichtbij</string>
    <string name="taskbar_edu_done">Gereed</string>
    <string name="taskbar_button_home">Startscherm</string>
    <string name="taskbar_button_a11y">Toegankelijkheid</string>
    <string name="taskbar_button_back">Terug</string>
    <string name="taskbar_button_ime_switcher">IME-schakelaar</string>
    <string name="taskbar_button_recents">Recent</string>
    <string name="taskbar_button_notifications">Meldingen</string>
    <string name="taskbar_button_quick_settings">Snelle instellingen</string>
    <string name="taskbar_a11y_title">Taakbalk</string>
    <string name="taskbar_a11y_shown_title">Taakbalk getoond</string>
    <string name="taskbar_a11y_hidden_title">Taakbalk verborgen</string>
    <string name="taskbar_phone_a11y_title">Navigatieschuifbalk</string>
    <string name="always_show_taskbar">Taakbalk altijd tonen</string>
    <string name="change_navigation_mode">Navigatiemodus wijzigen</string>
    <string name="taskbar_divider_a11y_title">Taakbalkverdeler</string>
    <string name="move_drop_target_top_or_left">Naar boven/links verplaatsen</string>
    <string name="move_drop_target_bottom_or_right">Naar beneden/rechts verplaatsen</string>
    <string name="quick_switch_overflow">"{count,plural, =1{Nog # app tonen.}other{Nog # apps tonen.}}"</string>
    <string name="quick_switch_desktop">"{count,plural, =1{# desktop-app tonen.}other{# desktop-apps tonen.}}"</string>
    <string name="quick_switch_split_task"><xliff:g id="app_name_1" example="Chrome">%1$s</xliff:g> en <xliff:g id="app_name_2" example="Gmail">%2$s</xliff:g></string>
    <string name="bubble_bar_bubble_fallback_description">Bubbel</string>
    <string name="bubble_bar_overflow_description">Overflow</string>
    <string name="bubble_bar_bubble_description"><xliff:g id="notification_title" example="some title">%1$s</xliff:g> van <xliff:g id="app_name" example="YouTube">%2$s</xliff:g></string>
    <string name="bubble_bar_description_multiple_bubbles"><xliff:g id="bubble_bar_bubble_description" example="some title from YouTube">%1$s</xliff:g> en nog <xliff:g id="bubble_count" example="4">%2$d</xliff:g></string>
    <string name="app_name">Launcher3</string>
</resources>