/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.quickstep.interaction;

import android.app.Activity;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

/** Displays one page of the gesture nav tutorial. */
public abstract class GestureSandboxFragment extends Fragment {

    void onAttachedToWindow() {}

    void onDetachedFromWindow() {}

    boolean canRecreateFragment() {
        return false;
    }

    @Nullable
    GestureSandboxFragment recreateFragment() {
        return null;
    }

    boolean shouldDisableSystemGestures() {
        return true;
    }

    void close() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            activity.setResult(Activity.RESULT_OK);
            activity.finish();
        }
    }
}
