/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.launcher3.taskbar.allapps;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.android.launcher3.ExtendedEditText;
import com.android.launcher3.allapps.ActivityAllAppsContainerView;
import com.android.launcher3.allapps.SearchUiManager;

/** Empty search container for Taskbar All Apps used as a fallback if search is not supported. */
public class TaskbarAllAppsFallbackSearchContainer extends View implements SearchUiManager {
    public TaskbarAllAppsFallbackSearchContainer(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TaskbarAllAppsFallbackSearchContainer(
            Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initializeSearch(ActivityAllAppsContainerView<?> containerView) {
        // Do nothing.
    }

    @Override
    public void resetSearch() {
        // Do nothing.
    }

    @Nullable
    @Override
    public ExtendedEditText getEditText() {
        return null;
    }
}
