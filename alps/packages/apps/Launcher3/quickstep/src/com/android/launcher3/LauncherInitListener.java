/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.launcher3;

import com.android.quickstep.util.ActivityInitListener;

import java.util.function.BiPredicate;

public class LauncherInitListener extends ActivityInitListener<Launcher> {

    /**
     * @param onInitListener a callback made when the activity is initialized. The callback should
     *                       return true to continue receiving callbacks (ie. for if the activity is
     *                       recreated).
     */
    public LauncherInitListener(BiPredicate<Launcher, Boolean> onInitListener) {
        super(onInitListener, Launcher.ACTIVITY_TRACKER);
    }

    @Override
    public boolean handleInit(Launcher launcher, boolean alreadyOnHome) {
        launcher.deferOverlayCallbacksUntilNextResumeOrStop();
        return super.handleInit(launcher, alreadyOnHome);
    }
}
