// Copyright (C) 2021 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    default_applicable_licenses: ["Android-Apache-2.0"],
}

filegroup {
    name: "launcher3-quickstep-manifest",
    srcs: ["AndroidManifest.xml"],
}

filegroup {
    name: "launcher3-quickstep-robo-src",
    path: "tests/multivalentTests",
    srcs: [
        "tests/multivalentTests/src/**/*.java",
        "tests/multivalentTests/src/**/*.kt",
    ],
}

filegroup {
    name: "launcher3-quickstep-tests-src",
    path: "tests",
    srcs: [
        "tests/multivalentTests/src/**/*.java",
        "tests/multivalentTests/src/**/*.kt",
        "tests/src/**/*.java",
        "tests/src/**/*.kt",
    ],
}

filegroup {
    name: "launcher3-quickstep-oop-tests-src",
    path: "tests",
    srcs: [
        "tests/src/com/android/quickstep/TaskbarModeSwitchRule.java",
        "tests/src/com/android/quickstep/NavigationModeSwitchRule.java",
        "tests/src/com/android/quickstep/AbstractQuickStepTest.java",
        "tests/src/com/android/quickstep/TaplOverviewIconTest.java",
        "tests/src/com/android/quickstep/TaplTestsQuickstep.java",
        "tests/src/com/android/quickstep/TaplTestsSplitscreen.java",
        "tests/src/com/android/launcher3/testcomponent/ExcludeFromRecentsTestActivity.java"
    ],
}
