<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
     xmlns:tools="http://schemas.android.com/tools"
     package="com.android.launcher3">

    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
    <uses-permission android:name="android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.START_TASKS_FROM_RECENTS"/>
    <uses-permission android:name="android.permission.REMOVE_TASKS"/>
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"/>
    <uses-permission android:name="android.permission.MANAGE_ACTIVITY_TASKS"/>
    <uses-permission android:name="android.permission.INTERNAL_SYSTEM_WINDOW"/>
    <uses-permission android:name="android.permission.STATUS_BAR"/>
    <uses-permission android:name="android.permission.STATUS_BAR_SERVICE"/>
    <uses-permission android:name="android.permission.STOP_APP_SWITCHES"/>
    <uses-permission android:name="android.permission.SET_ORIENTATION"/>
    <uses-permission android:name="android.permission.READ_FRAME_BUFFER"/>
    <uses-permission android:name="android.permission.MANAGE_ACCESSIBILITY"/>
    <uses-permission android:name="android.permission.MONITOR_INPUT"/>
    <uses-permission android:name="android.permission.ALLOW_SLIPPERY_TOUCHES"/>
    <uses-permission android:name="android.permission.ACCESS_SHORTCUTS"/>

    <uses-permission android:name="android.permission.SYSTEM_APPLICATION_OVERLAY" />
    <!-- Bug  2007558 for StatsManager -->
    <uses-permission android:name="android.permission.REGISTER_STATS_PULL_ATOM" />
    <!-- UNISOC: Modify for bug 2257799 -->
    <uses-permission android:name="android.permission.START_ACTIVITIES_FROM_BACKGROUND"/>

    <!--
    Permission required to access profiles which are otherwise hidden
    from being visible via APIs, e.g. private profile.
    -->
    <uses-permission android:name="android.permission.ACCESS_HIDDEN_PROFILES_FULL" />

    <!-- UNISOC: Add for AR.695.001103.007734.024648 -->
    <uses-permission android:name="unisoc.permission.MANAGER_UNIONPNP"/>

    <!-- Permission required to start a WidgetPickerActivity. -->
    <permission android:name="${applicationId}.permission.START_WIDGET_PICKER_ACTIVITY"
        android:protectionLevel="signature|privileged" />

    <application android:backupAgent="com.android.launcher3.LauncherBackupAgent"
         android:fullBackupOnly="true"
         android:fullBackupContent="@xml/backupscheme"
         android:hardwareAccelerated="true"
         android:icon="@drawable/ic_launcher_home"
         android:label="@string/derived_app_name"
         android:theme="@style/AppTheme"
         android:largeHeap="@bool/config_largeHeap"
         android:restoreAnyVersion="true"
         android:supportsRtl="true">

        <service android:name="com.android.quickstep.TouchInteractionService"
             android:permission="android.permission.STATUS_BAR_SERVICE"
             android:directBootAware="true"
             android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.QUICKSTEP_SERVICE"/>
            </intent-filter>
        </service>

        <activity android:name="com.android.quickstep.RecentsActivity"
             android:excludeFromRecents="true"
             android:launchMode="singleTask"
             android:clearTaskOnLaunch="true"
             android:stateNotNeeded="true"
             android:theme="@style/LauncherTheme"
             android:screenOrientation="behind"
             android:configChanges="keyboard|keyboardHidden|mcc|mnc|navigation|orientation|screenSize|screenLayout|smallestScreenSize"
             android:resizeableActivity="true"
             android:resumeWhilePausing="true"
             android:enableOnBackInvokedCallback="false"
             android:taskAffinity=""/>

        <!-- Content provider to settings search. The autority should be same as the packageName -->
        <provider android:name="com.android.quickstep.LauncherSearchIndexablesProvider"
             android:authorities="${applicationId}"
             android:grantUriPermissions="true"
             android:multiprocess="true"
             android:permission="android.permission.READ_SEARCH_INDEXABLES"
             android:exported="true">
            <intent-filter>
                <action android:name="android.content.action.SEARCH_INDEXABLES_PROVIDER"/>
            </intent-filter>
        </provider>

        <!-- FileProvider used for sharing images. -->
        <provider android:name="androidx.core.content.FileProvider"
             android:authorities="${applicationId}.overview.fileprovider"
             android:exported="false"
             android:grantUriPermissions="true">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS"
                 android:resource="@xml/overview_file_provider_paths"/>
        </provider>

        <activity android:name="com.android.launcher3.proxy.ProxyActivityStarter"
            android:theme="@style/ProxyActivityStarterTheme"
            android:launchMode="singleTask"
            android:clearTaskOnLaunch="true"
            android:exported="false"
            />

        <activity android:name="com.android.quickstep.interaction.GestureSandboxActivity"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true"
            android:theme="@style/GestureTutorialActivity"
            android:exported="true"
            android:configChanges="orientation">
            <intent-filter>
                <action android:name="com.android.quickstep.action.GESTURE_SANDBOX"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <!--
        Activity following gesture nav onboarding.
        It's protected by android.permission.REBOOT to ensure that only system apps can start it
        (setup wizard already has this permission)
        -->
        <activity android:name="com.android.quickstep.interaction.AllSetActivity"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true"
            android:permission="android.permission.REBOOT"
            android:theme="@style/AllSetTheme"
            android:label="@string/allset_title"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.quickstep.action.GESTURE_ONBOARDING_ALL_SET"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:name="com.android.launcher3.WidgetPickerActivity"
            android:theme="@style/WidgetPickerActivityTheme"
            android:excludeFromRecents="true"
            android:autoRemoveFromRecents="true"
            android:showOnLockScreen="true"
            android:launchMode="singleTop"
            android:exported="true"
            android:permission="android.permission.START_WIDGET_PICKER_ACTIVITY">
            <intent-filter>
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

    </application>

</manifest>
