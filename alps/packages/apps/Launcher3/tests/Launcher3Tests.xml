<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<configuration description="Runs Launcher3 tests.">
    <option name="test-suite-tag" value="apct" />
    <option name="test-suite-tag" value="apct-instrumentation" />

    <option name="max-tmp-logcat-file" value="104857600" /> <!-- 100 * 1024 * 1024 -->

    <logger class="com.android.tradefed.log.FileLogger">
        <option name="max-log-size" value="20" />
    </logger>

    <!-- Disables the "Ramdump uploader to betterbug" -->
    <option name="post-boot-command" value="am broadcast --async --user 0 -a com.google.gservices.intent.action.GSERVICES_OVERRIDE -e betterbug_enable_ramdump_uploader false" />

    <target_preparer class="com.android.tradefed.targetprep.DeviceSetup">
        <option name="set-test-harness" value="true" />

        <option name="run-command" value="svc nfc disable" />
        <option name="run-command" value="settings put global ble_scan_always_enabled 0" />
        <option name="run-command" value="svc bluetooth disable" />

        <option name="run-command" value="pm uninstall com.google.android.apps.nexuslauncher" />
        <option name="run-command" value="pm uninstall com.google.android.apps.nexuslauncher.out_of_proc_tests" />
        <option name="run-command" value="pm uninstall com.google.android.apps.nexuslauncher.tests" />
        <option name="run-command" value="pm disable com.google.android.googlequicksearchbox" />

        <option name="run-command" value="input keyevent 82" />
        <option name="run-command" value="settings delete secure assistant" />
        <option name="run-command" value="settings put global airplane_mode_on 1" />
        <option name="run-command" value="am broadcast -a android.intent.action.AIRPLANE_MODE" />

        <option name="run-command" value="setprop debug.wm.disable_deprecated_target_sdk_dialog 1"/>

        <option name="run-command" value="settings put system pointer_location 1" />
        <option name="run-command" value="settings put system show_touches 1" />
    </target_preparer>

    <target_preparer class="com.android.tradefed.targetprep.suite.SuiteApkInstaller">
        <option name="cleanup-apks" value="true" />
        <option name="test-file-name" value="Launcher3Tests.apk" />
        <option name="test-file-name" value="Launcher3.apk" />
    </target_preparer>

    <metrics_collector class="com.android.tradefed.device.metric.FilePullerLogCollector">
        <option name="directory-keys" value="/data/user/0/com.android.launcher3/files" />
        <option name="collect-on-run-ended-only" value="true" />
    </metrics_collector>

    <test class="com.android.tradefed.testtype.AndroidJUnitTest" >
        <option name="package" value="com.android.launcher3.tests" />
        <option name="runner" value="androidx.test.runner.AndroidJUnitRunner" />
        <option name="hidden-api-checks" value="false" />
    </test>
</configuration>
