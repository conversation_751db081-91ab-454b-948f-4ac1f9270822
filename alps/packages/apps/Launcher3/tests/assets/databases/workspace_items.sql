DROP TABLE IF EXISTS 'favorites';
CREATE TABLE favorites (_id INTEGER PRIMARY KEY,title TEXT,intent TEXT,container INTEGER,screen INTEGER,cellX INTEGER,cellY INTEGER,spanX INTEGER,spanY INTEGER,itemType INTEGER,appWidgetId INTEGER NOT NULL DEFAULT -1,icon BLOB,appWidgetProvider TEXT,modified INTEGER NOT NULL DEFAULT 0,restored INTEGER NOT NULL DEFAULT 0,profileId INTEGER DEFAULT 0,rank INTEGER NOT NULL DEFAULT 0,options INTEGER NOT NULL DEFAULT 0,appWidgetSource INTEGER NOT NULL DEFAULT -1);
INSERT INTO 'favorites' VALUES(1,'Phone','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.dialer/.extensions.GoogleDialtactsActivity;end',-101,0,0,0,1,1,0,-1,NULL,NULL,0,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(2,'Messages','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.messaging/.ui.ConversationListActivity;end',-101,1,1,0,1,1,0,-1,NULL,NULL,0,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(3,'Play Store','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.vending/.AssetBrowserActivity;end',-101,2,2,0,1,1,0,-1,NULL,NULL,0,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(4,'Chrome','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.chrome/com.google.android.apps.chrome.Main;end',-101,3,3,0,1,1,0,-1,NULL,NULL,0,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(6,'Settings','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.settings/.Settings;end',-100,0,0,1,1,1,0,-1,X'',NULL,1693590010654,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(7,'Drive','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.docs/.app.NewMainProxyActivity;end',-100,0,1,2,1,1,0,-1,X'',NULL,1693589533751,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(8,'Better Bug','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.internal.betterbug/com.google.android.apps.betterbug.bugslist.BugsListActivity;end',16,0,0,0,1,1,0,-1,X'',NULL,1693589597917,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(9,'Flag Flipper','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.theflippinapp/.MainActivity;end',-100,0,3,2,1,1,0,-1,X'',NULL,1693589546863,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(10,NULL,NULL,-100,0,1,3,3,2,4,3,NULL,'com.google.android.apps.docs/com.google.android.apps.docs.drive.widget.suggestion.SuggestionAppWidgetProvider',1693589554018,0,0,0,0,-112);
INSERT INTO 'favorites' VALUES(11,'Scan','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.docs;component=com.google.android.apps.docs/.app.NewMainProxyActivity;S.shortcut_id=launcher_shortcut_scan;end',-100,0,0,3,1,1,6,-1,X'',NULL,1693589559601,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(12,'Upload','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.docs;component=com.google.android.apps.docs/.app.NewMainProxyActivity;S.shortcut_id=launcher_shortcut_upload;end',-100,0,0,4,1,1,6,-1,X'',NULL,1693589576040,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(13,'Search','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.docs;component=com.google.android.apps.docs/.app.NewMainProxyActivity;S.shortcut_id=launcher_shortcut_search;end',-100,0,4,4,1,1,6,-1,X'',NULL,1693589582487,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(15,'BluetoothCompanionApp','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.bluetooth.companion/.MainActivity;end',-100,0,1,1,1,1,0,-1,X'',NULL,1693589594115,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(16,'Better Bug',NULL,-100,0,3,1,1,1,2,-1,NULL,NULL,1693589600675,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(17,'Better Bug','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.internal.betterbug/com.google.android.apps.betterbug.bugslist.BugsListActivity;end',16,0,1,0,1,1,0,-1,X'',NULL,1693589597936,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(18,'Better Bug','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.internal.betterbug/com.google.android.apps.betterbug.bugslist.BugsListActivity;end',16,0,0,1,1,1,0,-1,X'',NULL,1693589603123,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(19,'Incognito Tab','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.android.chrome;component=com.android.chrome/com.google.android.apps.chrome.Main;S.shortcut_id=dynamic-new-incognito-tab-shortcut;end',-100,0,2,2,1,1,6,-1,X'',NULL,1693589609963,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(20,NULL,NULL,-100,1,0,0,3,2,4,5,NULL,'com.google.android.deskclock/com.android.alarmclock.AnalogAppWidgetProvider',1693589630235,0,0,0,0,-112);
INSERT INTO 'favorites' VALUES(21,NULL,NULL,-100,1,1,3,2,2,4,6,NULL,'com.android.chrome/org.chromium.chrome.browser.quickactionsearchwidget.QuickActionSearchWidgetProvider$QuickActionSearchWidgetProviderDino',1693589677239,0,0,0,0,-112);
INSERT INTO 'favorites' VALUES(22,'New tab','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.android.chrome;component=com.android.chrome/com.google.android.apps.chrome.Main;S.shortcut_id=new-tab-shortcut;end',36,0,0,0,1,1,6,-1,X'',NULL,1693589694253,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(25,'Selfie','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.GoogleCamera;component=com.google.android.GoogleCamera/com.android.camera.CameraLauncher;S.shortcut_id=selfie;end',33,0,0,0,1,1,6,-1,X'',NULL,1693589686832,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(26,'Calculator','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.calculator/com.android.calculator2.Calculator;end',-100,1,4,2,1,1,0,-1,X'',NULL,1693589656954,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(27,'Calendar','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.calendar/com.android.calendar.AllInOneActivity;end',-100,1,4,0,1,1,0,-1,X'',NULL,1693589660516,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(28,'New event','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.calendar;component=com.google.android.calendar/com.android.calendar.AllInOneActivity;S.shortcut_id=launcher_shortcuts_shortcut_new_event;end',38,0,0,0,1,1,6,-1,X'',NULL,1693589698615,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(29,'New task','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.calendar;component=com.google.android.calendar/com.android.calendar.AllInOneActivity;S.shortcut_id=launcher_shortcuts_shortcut_create_task;end',-100,1,0,4,1,1,6,-1,X'',NULL,1693589677243,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(30,'Contacts','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.contacts/com.android.contacts.activities.PeopleActivity;end',-100,1,4,1,1,1,0,-1,X'',NULL,1693589671550,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(31,'Alex Eaves','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.contacts;component=com.google.android.contacts/com.android.contacts.activities.PeopleActivity;S.shortcut_id=822i60c772551678bd93;end',-100,1,4,3,1,1,6,-1,X'',NULL,1693589681873,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(32,'Add contact','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.contacts;component=com.google.android.contacts/com.android.contacts.activities.PeopleActivity;S.shortcut_id=shortcut-add-contact;end',-100,1,3,3,1,1,6,-1,X'',NULL,1693589681880,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(33,'Call',NULL,-100,1,3,2,1,1,2,-1,NULL,NULL,1693589687263,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(34,'Contacts','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.contacts/com.android.contacts.activities.PeopleActivity;end',33,0,1,0,1,1,0,-1,X'',NULL,1693589686853,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(35,'Gmail','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.gm/.ConversationListActivityGmail;end',33,0,0,1,1,1,0,-1,X'',NULL,1693589690561,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(36,'Files',NULL,-100,1,3,1,1,1,2,-1,NULL,NULL,1693589706696,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(37,'Files','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.nbu.files/.home.HomeActivity;end',36,0,1,0,1,1,0,-1,X'',NULL,1693589694261,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(38,NULL,NULL,-100,1,0,3,1,1,2,-1,NULL,NULL,1693589698611,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(39,'Scan','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.docs;component=com.google.android.apps.docs/.app.NewMainProxyActivity;S.shortcut_id=launcher_shortcut_scan;end',38,0,1,0,1,1,6,-1,X'',NULL,1693589698621,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(40,'Search','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.docs;component=com.google.android.apps.docs/.app.NewMainProxyActivity;S.shortcut_id=launcher_shortcut_search;end',-100,1,3,4,1,1,6,-1,X'',NULL,1693589702696,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(41,'Upload','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.docs;component=com.google.android.apps.docs/.app.NewMainProxyActivity;S.shortcut_id=launcher_shortcut_upload;end',-100,1,1,2,1,1,6,-1,X'',NULL,1693589706711,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(42,'Camera Obfuscator','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.internal.camera.imageobfuscator/.activities.MainActivity;end',-100,1,3,0,1,1,0,-1,X'',NULL,1693589710458,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(43,'Files','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.nbu.files/.home.HomeActivity;end',45,0,0,0,1,1,0,-1,X'',NULL,1693589727388,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(44,'Flag Flipper','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.theflippinapp/.MainActivity;end',-100,2,1,0,1,1,0,-1,X'',NULL,1693589724756,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(45,NULL,NULL,-100,2,0,0,1,1,2,-1,NULL,NULL,1693589727385,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(46,'Flag Flipper','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.theflippinapp/.MainActivity;end',45,0,1,0,1,1,0,-1,X'',NULL,1693589727398,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(47,'Gmail','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.gm/.ConversationListActivityGmail;end',-100,2,2,0,1,1,0,-1,X'',NULL,1693589730037,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(48,'Compose','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.gm;component=com.google.android.gm/.ConversationListActivityGmail;S.shortcut_id=manifest_compose_shortcut;end',-100,2,3,0,1,1,6,-1,X'',NULL,1693589733121,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(49,NULL,NULL,-100,2,0,1,3,2,4,7,NULL,'com.google.android.gm/com.google.android.gm.widget.GmailWidgetProvider',1693589740752,0,0,0,0,-112);
INSERT INTO 'favorites' VALUES(50,NULL,NULL,-100,3,1,0,4,5,4,8,NULL,'com.google.android.calendar/com.google.android.calendar.widgetmonth.MonthViewWidgetProvider',1693589746495,0,0,0,0,-111);
INSERT INTO 'favorites' VALUES(54,'Maps','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.maps/com.google.android.maps.MapsActivity;end',-100,2,3,2,1,1,0,-1,X'',NULL,1693589785990,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(55,'Productivity',NULL,-100,2,3,4,1,1,2,-1,NULL,NULL,1693589797590,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(56,'Work','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.maps;component=com.google.android.apps.maps/com.google.android.maps.MapsActivity;S.shortcut_id=manifest_work;end',55,0,1,0,1,1,6,-1,X'',NULL,1693589789538,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(57,'Home','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.apps.maps;component=com.google.android.apps.maps/com.google.android.maps.MapsActivity;S.shortcut_id=manifest_home;end',-100,2,3,1,1,1,6,-1,X'',NULL,1693589793825,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(58,'Gyotaku','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.internal.gyotaku/.Launcher;end',-100,2,3,3,1,1,0,-1,X'',NULL,1693589797615,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(60,'Phone','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.dialer/.extensions.GoogleDialtactsActivity;end',-100,2,4,3,1,1,0,-1,X'',NULL,1693589805582,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(61,'Photos','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.photos/.home.HomeActivity;end',-100,2,4,4,1,1,0,-1,X'',NULL,1693589809050,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(63,'Pixel Logger',NULL,-100,3,0,3,1,1,2,-1,NULL,NULL,1693589820775,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(65,'Pixel Tips','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.tips/.TipsMain;end',-100,3,0,4,1,1,0,-1,X'',NULL,1693589823832,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(66,'Play Store','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.vending/.AssetBrowserActivity;end',-100,3,0,1,1,1,0,-1,X'',NULL,1693589834647,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(67,NULL,NULL,-100,4,2,0,3,2,4,10,NULL,'com.google.android.apps.youtube.music/com.google.android.apps.youtube.music.player.widget.gm3.FreeformMusicWidgetProvider',1693589842256,0,0,0,0,-112);
INSERT INTO 'favorites' VALUES(68,NULL,NULL,-100,4,0,2,4,3,4,11,NULL,'com.google.android.apps.youtube.music/com.google.android.apps.youtube.music.player.widget.gm3.FreeformMusicWidgetProvider',1693589854706,0,0,0,0,-112);
INSERT INTO 'favorites' VALUES(69,'YouTube','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.youtube/.app.honeycomb.Shell%24HomeActivity;end',-100,4,4,4,1,1,0,-1,X'',NULL,1693589859008,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(70,'Explore','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.youtube;component=com.google.android.youtube/.app.honeycomb.Shell%24HomeActivity;S.shortcut_id=explore-shortcut;end',-100,4,4,3,1,1,6,-1,X'',NULL,1693589867283,0,0,3,0,-1);
INSERT INTO 'favorites' VALUES(71,'Search','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.youtube;component=com.google.android.youtube/.app.honeycomb.Shell%24HomeActivity;S.shortcut_id=search-shortcut;end',-100,4,4,2,1,1,6,-1,X'',NULL,1693589871989,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(72,'Shorts','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.youtube;component=com.google.android.youtube/.app.honeycomb.Shell%24HomeActivity;S.shortcut_id=shorts-shortcut;end',-100,4,0,1,1,1,6,-1,X'',NULL,1693589882256,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(73,'Subscriptions','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.google.android.youtube;component=com.google.android.youtube/.app.honeycomb.Shell%24HomeActivity;S.shortcut_id=subscriptions-shortcut;end',-100,4,0,0,1,1,6,-1,X'',NULL,1693589888244,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(74,'Safety','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.safetyhub/.LauncherActivity;end',-100,4,1,1,1,1,0,-1,X'',NULL,1693589891720,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(75,'Wi‑Fi','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.android.settings;component=com.android.settings/.Settings;S.shortcut_id=manifest-shortcut-wifi;end',-100,5,2,0,1,1,6,-1,X'',NULL,1693589897994,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(76,'Data usage','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.android.settings;component=com.android.settings/.Settings;S.shortcut_id=manifest-shortcut-data-usage;end',-100,5,3,1,1,1,6,-1,X'',NULL,1693589904331,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(77,'Battery','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.android.settings;component=com.android.settings/.Settings;S.shortcut_id=manifest-shortcut-battery;end',-100,5,1,2,1,1,6,-1,X'',NULL,1693589907795,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(78,'Internet','#Intent;action=android.intent.action.MAIN;category=com.android.launcher3.DEEP_SHORTCUT;launchFlags=0x10200000;package=com.android.settings;component=com.android.settings/.Settings;S.shortcut_id=component-shortcut-com.android.settings%2F.Settings%24NetworkProviderSettingsActivity;end',-100,5,2,1,1,1,6,-1,X'',NULL,1693589914187,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(79,'Safety','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.safetyhub/.LauncherActivity;end',-100,5,2,3,1,1,0,-1,X'',NULL,1693589917447,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(80,'Recorder','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.recorder/.ui.recordings.MainActivity;end',-100,5,0,4,1,1,0,-1,X'',NULL,1693589920866,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(81,'Maps','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.apps.maps/com.google.android.maps.MapsActivity;end',82,0,0,0,1,1,0,-1,X'',NULL,1693589929103,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(82,NULL,NULL,-100,5,3,3,1,1,2,-1,NULL,NULL,1693589929099,0,0,0,0,-1);
INSERT INTO 'favorites' VALUES(83,'Flag Flipper','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.theflippinapp/.MainActivity;end',82,0,1,0,1,1,0,-1,X'',NULL,1693589929134,0,0,1,0,-1);
INSERT INTO 'favorites' VALUES(84,'Gmail','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.gm/.ConversationListActivityGmail;end',82,0,2,0,1,1,0,-1,X'',NULL,1693589938320,0,0,2,0,-1);
INSERT INTO 'favorites' VALUES(85,'Google','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.googlequicksearchbox/.SearchActivity;end',82,0,0,1,1,1,0,-1,X'',NULL,1693589938321,0,0,3,0,-1);
INSERT INTO 'favorites' VALUES(86,'Calendar','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.google.android.calendar/com.android.calendar.AllInOneActivity;end',82,0,1,1,1,1,0,-1,X'',NULL,1693589938316,0,0,4,0,-1);
INSERT INTO 'favorites' VALUES(87,'Chrome','#Intent;action=android.intent.action.MAIN;category=android.intent.category.LAUNCHER;launchFlags=0x10200000;component=com.android.chrome/com.google.android.apps.chrome.Main;end',82,0,2,1,1,1,0,-1,X'',NULL,1693589941181,0,0,5,0,-1);
