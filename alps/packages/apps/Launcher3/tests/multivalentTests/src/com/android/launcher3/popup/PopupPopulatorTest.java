/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.launcher3.popup;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;

import static com.android.launcher3.popup.PopupPopulator.MAX_SHORTCUTS;
import static com.android.launcher3.popup.PopupPopulator.NUM_DYNAMIC;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import android.content.pm.ShortcutInfo;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.filters.SmallTest;

import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Tests the sorting and filtering of shortcuts in {@link PopupPopulator}.
 */
@SmallTest
@RunWith(AndroidJUnit4.class)
public class PopupPopulatorTest {

    @Test
    public void testSortAndFilterShortcuts() {
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(3, 0), 3, 0);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(0, 3), 0, 3);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(5, 0), MAX_SHORTCUTS, 0);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(0, 5), 0, MAX_SHORTCUTS);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(3, 3),
                MAX_SHORTCUTS - NUM_DYNAMIC, NUM_DYNAMIC);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(5, 5),
                MAX_SHORTCUTS - NUM_DYNAMIC, NUM_DYNAMIC);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(5, 1), MAX_SHORTCUTS - 1, 1);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(1, 5), 1, MAX_SHORTCUTS - 1);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(5, 3),
                MAX_SHORTCUTS - NUM_DYNAMIC, NUM_DYNAMIC);
        filterShortcutsAndAssertNumStaticAndDynamic(createShortcutsList(3, 5),
                MAX_SHORTCUTS - NUM_DYNAMIC, NUM_DYNAMIC);
    }

    private String generateId(boolean isStatic, int rank) {
        return (isStatic ? "static" : "dynamic") + rank;
    }

    private void filterShortcutsAndAssertNumStaticAndDynamic(
            List<ShortcutInfo> shortcuts, int expectedStatic, int expectedDynamic) {
        Collections.shuffle(shortcuts);
        List<ShortcutInfo> filteredShortcuts = PopupPopulator.sortAndFilterShortcuts(shortcuts);
        assertIsSorted(filteredShortcuts);

        int numStatic = 0;
        int numDynamic = 0;
        for (ShortcutInfo shortcut : filteredShortcuts) {
            if (shortcut.isDeclaredInManifest()) {
                numStatic++;
            }
            if (shortcut.isDynamic()) {
                numDynamic++;
            }
        }
        assertEquals(expectedStatic, numStatic);
        assertEquals(expectedDynamic, numDynamic);
    }

    private void assertIsSorted(List<ShortcutInfo> shortcuts) {
        int lastStaticRank = -1;
        int lastDynamicRank = -1;
        boolean hasSeenDynamic = false;
        for (ShortcutInfo shortcut : shortcuts) {
            int rank = shortcut.getRank();
            if (shortcut.isDeclaredInManifest()) {
                assertFalse("Static shortcuts should come before all dynamic shortcuts.",
                        hasSeenDynamic);
                assertTrue(rank > lastStaticRank);
                lastStaticRank = rank;
            }
            if (shortcut.isDynamic()) {
                hasSeenDynamic = true;
                assertTrue(rank > lastDynamicRank);
                lastDynamicRank = rank;
            }
        }
    }

    private List<ShortcutInfo> createShortcutsList(int numStatic, int numDynamic) {
        List<ShortcutInfo> shortcuts = new ArrayList<>();
        for (int i = 0; i < numStatic; i++) {
            shortcuts.add(createInfo(true, i));
        }
        for (int i = 0; i < numDynamic; i++) {
            shortcuts.add(createInfo(false, i));
        }
        return shortcuts;
    }

    private ShortcutInfo createInfo(boolean isStatic, int rank) {
        ShortcutInfo info = spy(new ShortcutInfo.Builder(
                getApplicationContext(), generateId(isStatic, rank))
                .setRank(rank)
                .build());
        doReturn(isStatic).when(info).isDeclaredInManifest();
        doReturn(!isStatic).when(info).isDynamic();
        return info;
    }
}