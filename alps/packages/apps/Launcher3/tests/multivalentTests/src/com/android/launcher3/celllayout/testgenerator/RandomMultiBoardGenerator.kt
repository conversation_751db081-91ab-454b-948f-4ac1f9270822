/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.launcher3.celllayout.testgenerator

import android.graphics.Rect
import com.android.launcher3.celllayout.board.CellLayoutBoard
import java.util.Random

class RandomMultiBoardGenerator(generator: Random) : RandomBoardGenerator(generator) {
    override fun generateBoard(
        width: Int,
        height: Int,
        remainingEmptySpaces: Int
    ): CellLayoutBoard {
        val cellLayoutBoard = CellLayoutBoard(width, height)
        fillBoard(cellLayoutBoard, Rect(0, 0, width / 2, height), remainingEmptySpaces / 2)
        return fillBoard(
            cellLayoutBoard,
            Rect(width / 2, 0, width, height),
            remainingEmptySpaces / 2
        )
    }
}
