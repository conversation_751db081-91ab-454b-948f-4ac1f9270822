/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.launcher3.util;

import static com.google.common.truth.Truth.assertThat;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.filters.SmallTest;

import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Unit tests for {@link IntArray}
 */
@SmallTest
@RunWith(AndroidJUnit4.class)
public class IntArrayTest {

    @Test
    public void concatAndParseString() {
        int[] array = new int[] {0, 2, 3, 9};
        String concat = IntArray.wrap(array).toConcatString();

        int[] parsed = IntArray.fromConcatString(concat).toArray();
        assertThat(array).isEqualTo(parsed);
    }
}
