/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.launcher3.tapl;

import static com.android.launcher3.testing.shared.TestProtocol.ALL_APPS_STATE_ORDINAL;

import androidx.test.uiautomator.UiObject2;

/**
 * Operations on Home screen qsb.
 */
class HomeQsb extends Qsb {

    HomeQsb(LauncherInstrumentation launcher, UiObject2 hotseat) {
        super(launcher, hotseat, "search_container_hotseat");
    }

    @Override
    protected void clickQsb() {
        // Clicking Qsb will switch to All Apps state.
        mLauncher.runToState(
                () -> super.clickQsb(),
                ALL_APPS_STATE_ORDINAL,
                "Clicking Qsb");
    }
}
