/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.launcher3.tapl;

import android.graphics.Point;

import androidx.test.uiautomator.UiObject2;

import java.util.regex.Pattern;

/**
 * App icon in workspace.
 */
final class WorkspaceAppIcon extends HomeAppIcon {

    WorkspaceAppIcon(LauncherInstrumentation launcher, UiObject2 icon) {
        super(launcher, icon);
    }

    @Override
    protected Pattern getLongClickEvent() {
        return Workspace.LONG_CLICK_EVENT;
    }

    boolean isInCell(int cellX, int cellY) {
        final Point center = Workspace.getCellCenter(mLauncher, cellX, cellY);
        return mObject.getParent().getVisibleBounds().contains(center.x, center.y);
    }
}
