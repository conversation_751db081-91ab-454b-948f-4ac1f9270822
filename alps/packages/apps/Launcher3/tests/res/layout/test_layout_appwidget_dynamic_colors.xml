<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="?android:attr/colorBackground"
    android:padding="8dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation = "horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="neut1"/>
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@android:color/system_neutral1_500"/>
    </LinearLayout>
    <LinearLayout
        android:orientation = "horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="accent1"/>
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@android:color/system_accent1_500"/>
    </LinearLayout>
    <LinearLayout
        android:orientation = "horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="accent2"/>
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@android:color/system_accent2_500"/>
    </LinearLayout>

    </LinearLayout>