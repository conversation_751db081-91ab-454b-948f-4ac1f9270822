<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!-- invalid file - aspect ratio = 0 (it must be bigger than zero) -->
<workspaceSpecs xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <specs launcher:maxAspectRatio="10">
        <workspaceSpec
            launcher:maxAvailableSize="9999dp"
            launcher:dimensionType="height">
            <cellSize launcher:fixedSize="104dp" />
            <endPadding launcher:ofRemainderSpace="1" />
            <gutter launcher:fixedSize="16dp" />
            <startPadding launcher:fixedSize="8dp" />
        </workspaceSpec>

        <workspaceSpec
            launcher:maxAvailableSize="9999dp"
            launcher:dimensionType="width">
            <cellSize launcher:ofRemainderSpace="0.25" />
            <endPadding launcher:fixedSize="22dp" />
            <gutter launcher:fixedSize="16dp" />
            <startPadding launcher:fixedSize="22dp" />
        </workspaceSpec>
    </specs>

    <specs launcher:maxAspectRatio="0">
        <workspaceSpec
            launcher:maxAvailableSize="9999dp"
            launcher:dimensionType="height">
            <cellSize launcher:fixedSize="104dp" />
            <endPadding launcher:ofRemainderSpace="1" />
            <gutter launcher:fixedSize="16dp" />
            <startPadding launcher:fixedSize="8dp" />
        </workspaceSpec>

        <workspaceSpec
            launcher:maxAvailableSize="9999dp"
            launcher:dimensionType="width">
            <cellSize launcher:ofRemainderSpace="0.25" />
            <endPadding launcher:fixedSize="22dp" />
            <gutter launcher:fixedSize="16dp" />
            <startPadding launcher:fixedSize="22dp" />
        </workspaceSpec>
    </specs>

</workspaceSpecs>
