<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<workspaceSpecs xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <specs launcher:maxAspectRatio="10">
        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="648dp">
            <startPadding
                launcher:ofAvailableSpace="0.0125" />
            <endPadding
                launcher:ofAvailableSpace="0.05" />
            <gutter
                launcher:fixedSize="16dp" />
            <!--  value bigger than 1 -->
            <cellSize
                launcher:ofRemainderSpace="1.001" />
        </workspaceSpec>

        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <startPadding
                launcher:ofAvailableSpace="0.0306" />
            <endPadding
                launcher:ofAvailableSpace="0.068" />
            <gutter
                launcher:fixedSize="16dp" />
            <cellSize
                launcher:ofRemainderSpace="0.2" />
        </workspaceSpec>

        <!-- Width spec is always the same -->
        <workspaceSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="9999dp">
            <startPadding
                launcher:ofRemainderSpace="0.21436227" />
            <endPadding
                launcher:ofRemainderSpace="0.21436227" />
            <gutter
                launcher:ofRemainderSpace="0.11425509" />
            <cellSize
                launcher:fixedSize="120dp" />
        </workspaceSpec>
    </specs>
</workspaceSpecs>
