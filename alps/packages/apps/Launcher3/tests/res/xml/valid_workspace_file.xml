<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<workspaceSpecs xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <specs launcher:maxAspectRatio="1.05">
        <!-- 584 grid height -->
        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="584dp">
            <startPadding launcher:fixedSize="0dp" />
            <endPadding launcher:fixedSize="32dp" />
            <gutter launcher:fixedSize="16dp" />
            <cellSize launcher:ofAvailableSpace="0.15808" />
        </workspaceSpec>

        <!-- 584 grid height + 28 remainder space -->
        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="612dp">
            <startPadding launcher:fixedSize="0dp" />
            <endPadding launcher:ofRemainderSpace="1" />
            <gutter launcher:fixedSize="16dp" />
            <cellSize launcher:fixedSize="104dp" />
        </workspaceSpec>

        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <startPadding launcher:fixedSize="8dp" />
            <endPadding launcher:ofRemainderSpace="1" />
            <gutter launcher:fixedSize="16dp" />
            <cellSize launcher:fixedSize="104dp" />
        </workspaceSpec>

        <!--  TODO(b/241386436): other specs here for height ...  -->

        <!-- Width spec is always the same -->
        <workspaceSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="9999dp">
            <startPadding launcher:fixedSize="22dp" />
            <endPadding launcher:fixedSize="22dp" />
            <gutter launcher:fixedSize="16dp" />
            <cellSize launcher:ofRemainderSpace="1" />
        </workspaceSpec>
    </specs>

    <!-- specs from land/handheld_workspace_spec_4x4.xml -->
    <specs launcher:maxAspectRatio="99999">
        <!-- Height spec -->
        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="371dp">
            <startPadding launcher:fixedSize="0dp" />
            <endPadding launcher:fixedSize="24dp" />
            <gutter launcher:fixedSize="12dp" />
            <cellSize launcher:ofRemainderSpace="1" />
        </workspaceSpec>
        <workspaceSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <startPadding launcher:fixedSize="0dp" />
            <endPadding launcher:fixedSize="34dp" />
            <gutter launcher:fixedSize="12dp" />
            <cellSize launcher:ofRemainderSpace="1" />
        </workspaceSpec>

        <!-- Width spec -->
        <workspaceSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="602dp">
            <startPadding launcher:fixedSize="0dp" />
            <endPadding launcher:fixedSize="36dp" />
            <gutter launcher:fixedSize="12dp" />
            <cellSize launcher:ofRemainderSpace="1" />
        </workspaceSpec>
        <workspaceSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="716dp">
            <startPadding launcher:fixedSize="16dp" />
            <endPadding launcher:fixedSize="64dp" />
            <gutter launcher:fixedSize="12dp" />
            <cellSize launcher:ofRemainderSpace="1" />
        </workspaceSpec>
        <workspaceSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="9999dp">
            <startPadding launcher:fixedSize="36dp" />
            <endPadding launcher:fixedSize="80dp" />
            <gutter launcher:fixedSize="12dp" />
            <cellSize launcher:ofRemainderSpace="1" />
        </workspaceSpec>
    </specs>
</workspaceSpecs>
