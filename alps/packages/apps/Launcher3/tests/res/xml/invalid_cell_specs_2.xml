<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!-- invalid: dimension type should be height -->
<cellSpecs xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <!-- portrait -->
    <specs launcher:maxAspectRatio="1.0">
        <cellSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="9999dp">
            <iconDrawablePadding launcher:fixedSize="8dp" />
            <iconSize launcher:fixedSize="48dp" />
            <iconTextSize launcher:fixedSize="12sp" />
        </cellSpec>
        <cellSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <iconDrawablePadding launcher:fixedSize="8dp" />
            <iconSize launcher:fixedSize="48dp" />
            <iconTextSize launcher:fixedSize="12sp" />
        </cellSpec>
    </specs>
    <!-- landscape -->
    <specs launcher:maxAspectRatio="10">
        <cellSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <iconDrawablePadding launcher:fixedSize="0dp" />
            <iconSize launcher:fixedSize="52dp" />
            <iconTextSize launcher:fixedSize="0sp" />
        </cellSpec>
        <cellSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <iconDrawablePadding launcher:fixedSize="0dp" />
            <iconSize launcher:fixedSize="52dp" />
            <iconTextSize launcher:fixedSize="0sp" />
        </cellSpec>
        <cellSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="9999dp">
            <iconDrawablePadding launcher:fixedSize="0dp" />
            <iconSize launcher:fixedSize="52dp" />
            <iconTextSize launcher:fixedSize="0sp" />
        </cellSpec>
    </specs>
</cellSpecs>