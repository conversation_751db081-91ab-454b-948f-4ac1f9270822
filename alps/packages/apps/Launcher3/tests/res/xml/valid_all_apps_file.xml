<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<allAppsSpecs xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <specs launcher:maxAspectRatio="10">
        <allAppsSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <startPadding launcher:fixedSize="0dp" />
            <endPadding launcher:fixedSize="0dp" />
            <gutter launcher:matchWorkspace="true" />
            <cellSize launcher:matchWorkspace="true" />
        </allAppsSpec>

        <allAppsSpec
            launcher:dimensionType="width"
            launcher:maxAvailableSize="9999dp">
            <startPadding launcher:matchWorkspace="true" />
            <endPadding launcher:matchWorkspace="true" />
            <gutter launcher:matchWorkspace="true" />
            <cellSize launcher:matchWorkspace="true" />
        </allAppsSpec>
    </specs>
</allAppsSpecs>
