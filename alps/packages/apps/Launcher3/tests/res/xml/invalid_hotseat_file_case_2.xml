<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!-- invalid: total fixed size > maxAvailableSize -->
<hotseatSpecs xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <specs launcher:maxAspectRatio="10">
        <hotseatSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="847dp">
            <hotseatQsbSpace launcher:fixedSize="845dp" />
            <edgePadding launcher:fixedSize="36dp" />
        </hotseatSpec>

        <hotseatSpec
            launcher:dimensionType="height"
            launcher:maxAvailableSize="9999dp">
            <hotseatQsbSpace launcher:fixedSize="36dp" />
            <edgePadding launcher:fixedSize="36dp" />
        </hotseatSpec>
    </specs>
</hotseatSpecs>