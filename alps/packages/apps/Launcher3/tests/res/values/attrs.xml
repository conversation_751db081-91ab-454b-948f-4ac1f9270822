<?xml version="1.0" encoding="utf-8"?><!--
/* Copyright 2023, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Attributes have to be copied to test for correct parsing of files -->
<resources>
    <!--  Responsive grids attributes  -->
    <declare-styleable name="ResponsiveSpec">
        <attr name="dimensionType" format="integer">
            <enum name="height" value="0" />
            <enum name="width" value="1" />
        </attr>
        <attr name="maxAvailableSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="ResponsiveSpecGroup">
        <attr name="maxAspectRatio" format="float" />
    </declare-styleable>

    <declare-styleable name="WorkspaceSpec">
        <attr name="dimensionType" />
        <attr name="maxAvailableSize" />
    </declare-styleable>

    <declare-styleable name="FolderSpec">
        <attr name="dimensionType" />
        <attr name="maxAvailableSize" />
    </declare-styleable>

    <declare-styleable name="AllAppsSpec">
        <attr name="dimensionType" />
        <attr name="maxAvailableSize" />
    </declare-styleable>

    <declare-styleable name="SizeSpec">
        <attr name="fixedSize" format="dimension" />
        <attr name="ofAvailableSpace" format="float" />
        <attr name="ofRemainderSpace" format="float" />
        <attr name="matchWorkspace" format="boolean" />
        <attr name="maxSize" format="dimension" />
    </declare-styleable>
</resources>
