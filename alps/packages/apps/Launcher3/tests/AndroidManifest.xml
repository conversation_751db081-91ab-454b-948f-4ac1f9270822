<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.android.launcher3.tests">

    <uses-sdk android:targetSdkVersion="29" android:minSdkVersion="25"
              tools:overrideLibrary="android.support.test.uiautomator.v18"/>

    <application android:debuggable="true">
        <uses-library android:name="android.test.runner" />
    </application>

    <instrumentation
        android:functionalTest="false"
        android:handleProfiling="false"
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:targetPackage="com.android.launcher3" >
    </instrumentation>
</manifest>
