<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content">

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:background="@drawable/arrow_toast_rounded_background"
        android:elevation="@dimen/arrow_toast_elevation"
        android:textColor="?attr/arrowTipTextColor"
        android:textSize="@dimen/arrow_toast_text_size"/>

    <View
        android:id="@+id/arrow"
        android:elevation="@dimen/arrow_toast_elevation"
        android:layout_width="@dimen/arrow_toast_arrow_width"
        android:layout_height="@dimen/arrow_toast_arrow_height"/>
</merge>
