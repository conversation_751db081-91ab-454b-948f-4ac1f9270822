<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- This file is used by multiple all_apps.xml. Layout consists of all contents
     showed in all apps screen
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android">

    <include
        layout="@layout/all_apps_bottom_sheet_background"
        android:visibility="gone" />

    <include
        layout="@layout/search_results_rv_layout"
        android:visibility="gone" />

    <include
        layout="@layout/all_apps_rv_layout"
        android:visibility="gone" />

    <com.android.launcher3.allapps.FloatingHeaderView
        android:id="@+id/all_apps_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:layout_below="@id/search_container_all_apps"
        android:paddingTop="@dimen/all_apps_header_top_padding"
        android:paddingBottom="@dimen/all_apps_header_bottom_padding"
        android:orientation="vertical" >

        <include layout="@layout/floating_header_content" />

        <include layout="@layout/all_apps_personal_work_tabs" />

    </com.android.launcher3.allapps.FloatingHeaderView>

    <include layout="@layout/all_apps_fast_scroller" />
</merge>