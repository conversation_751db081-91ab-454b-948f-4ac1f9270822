<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="match_parent"
             android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_margin="8dp"
        android:layout_gravity="center_vertical"
        android:background="@drawable/round_rect_primary"
        android:elevation="2dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:id="@+id/btn_qsb_search"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:text="@string/abandoned_search"
            android:textColor="?android:attr/textColorSecondary"
            android:textAppearance="?android:textAppearanceMedium"
            android:clickable="true"
            android:background="?android:attr/selectableItemBackground" />
        <ImageView
            android:layout_width="48dp"
            android:id="@+id/btn_qsb_setup"
            android:clickable="true"
            android:visibility="gone"
            android:layout_height="match_parent"
            android:src="@drawable/ic_setting"
            android:tint="?android:attr/textColorSecondary"
            android:contentDescription="@string/gadget_setup_text"
            android:padding="8dp"
            android:background="?android:attr/selectableItemBackground" />
    </LinearLayout>
</FrameLayout>