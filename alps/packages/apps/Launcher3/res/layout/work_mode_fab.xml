<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.allapps.WorkModeSwitch
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/work_mode_toggle"
    android:layout_alignParentBottom="true"
    android:layout_alignParentEnd="true"
    android:layout_height="@dimen/work_fab_height"
    android:layout_width="wrap_content"
    android:minHeight="@dimen/work_fab_height"
    android:gravity="center_vertical"
    android:background="@drawable/work_mode_fab_background"
    android:forceHasOverlappingRendering="false"
    android:contentDescription="@string/work_apps_pause_btn_text"
    android:paddingStart="@dimen/work_mode_fab_background_start_padding"
    android:paddingEnd="@dimen/work_mode_fab_background_end_padding"
    android:animateLayoutChanges="true">
    <ImageView
        android:id="@+id/work_icon"
        android:layout_width="@dimen/work_fab_icon_size"
        android:layout_height="@dimen/work_fab_icon_size"
        android:importantForAccessibility="no"
        android:layout_marginEnd="@dimen/work_fab_icon_end_margin"
        android:src="@drawable/ic_corp_off"
        android:tint="@color/work_fab_icon_color"
        android:scaleType="center"/>
    <TextView
        android:id="@+id/pause_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/work_fab_width"
        android:textColor="@color/work_fab_icon_color"
        android:textSize="14sp"
        android:includeFontPadding="false"
        android:textDirection="locale"
        android:text="@string/work_apps_pause_btn_text"
        android:layout_marginEnd="@dimen/work_fab_text_end_margin"
        android:ellipsize="end"
        android:maxLines="1"
        style="@style/TextHeadline"/>
</com.android.launcher3.allapps.WorkModeSwitch>
