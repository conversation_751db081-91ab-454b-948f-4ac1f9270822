<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.allapps.WorkPausedCard xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/all_apps_tabs_margin_top"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        style="@style/PrimaryHeadline"
        android:textColor="?android:attr/textColorPrimary"
        android:id="@+id/work_apps_paused_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/work_apps_paused_title"
        android:textAlignment="center"
        android:textSize="18sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/work_apps_paused_content"
        android:textColor="?android:attr/textColorSecondary"
        android:text="@string/work_apps_paused_body"
        android:textAlignment="center"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp"
        android:textSize="14sp" />

    <Button
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rounded_button_height"
        android:id="@+id/enable_work_apps"
        android:textColor="?android:attr/textColorPrimary"
        android:text="@string/work_apps_enable_btn_text"
        android:textAlignment="center"
        android:background="@drawable/bg_work_apps_paused_action_button"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textSize="14sp" />
</com.android.launcher3.allapps.WorkPausedCard>