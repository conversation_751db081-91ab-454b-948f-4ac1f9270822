<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.allapps.search.AppsSearchContainerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@id/search_container_all_apps"
    android:layout_width="match_parent"
    android:layout_height="@dimen/all_apps_search_bar_field_height"
    android:layout_centerHorizontal="true"
    android:layout_gravity="top|center_horizontal"
    android:background="@drawable/bg_all_apps_searchbox"
    android:focusableInTouchMode="true"
    android:gravity="center"
    android:hint="@string/all_apps_search_bar_hint"
    android:imeOptions="actionSearch|flagNoExtractUi"
    android:importantForAutofill="no"
    android:inputType="text|textNoSuggestions|textCapWords"
    android:maxLines="1"
    android:padding="8dp"
    android:saveEnabled="false"
    android:scrollHorizontally="true"
    android:singleLine="true"
    android:textColor="?android:attr/textColorSecondary"
    android:textColorHint="@drawable/all_apps_search_hint"
    android:textSize="16sp" />