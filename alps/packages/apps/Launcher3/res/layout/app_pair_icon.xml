<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2023 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<com.android.launcher3.apppairs.AppPairIcon
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:focusable="true" >
    <com.android.launcher3.apppairs.AppPairIconGraphic
        android:id="@+id/app_pair_icon_graphic"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="false" />
    <com.android.launcher3.views.DoubleShadowBubbleTextView
        style="@style/BaseIcon.Workspace"
        android:id="@+id/app_pair_icon_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="false"
        android:layout_gravity="top" />
</com.android.launcher3.apppairs.AppPairIcon>
