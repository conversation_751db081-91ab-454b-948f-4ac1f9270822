<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.widget.picker.WidgetsFullSheet
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:theme="?attr/widgetsTheme">

    <com.android.launcher3.views.SpringRelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:importantForAccessibility="no">

        <View
            android:id="@+id/collapse_handle"
            android:layout_width="@dimen/bottom_sheet_handle_width"
            android:layout_height="@dimen/bottom_sheet_handle_height"
            android:layout_marginTop="@dimen/bottom_sheet_handle_margin"
            android:layout_centerHorizontal="true"
            android:background="@drawable/widget_picker_collapse_handle"/>

        <TextView
            style="@style/PrimaryHeadline"
            android:id="@+id/no_widgets_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:visibility="gone"
            android:textSize="18sp"
            android:layout_below="@id/search_and_recommendations_container"
            tools:text="@string/no_widgets_available" />

        <!-- Fast scroller popup -->
        <TextView
            android:id="@+id/fast_scroller_popup"
            style="@style/FastScrollerPopup"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginEnd="@dimen/fastscroll_popup_margin" />

        <com.android.launcher3.views.RecyclerViewFastScroller
            android:id="@+id/fast_scroller"
            android:layout_width="@dimen/fastscroll_width"
            android:elevation="1dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginEnd="@dimen/fastscroll_end_margin" />

        <com.android.launcher3.widget.picker.WidgetsRecyclerView
            android:id="@+id/search_widgets_list_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/collapse_handle"
            android:paddingHorizontal="@dimen/widget_list_horizontal_margin"
            android:visibility="gone"
            android:clipToPadding="false" />

    </com.android.launcher3.views.SpringRelativeLayout>
</com.android.launcher3.widget.picker.WidgetsFullSheet>