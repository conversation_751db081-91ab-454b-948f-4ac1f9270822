<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.widget.picker.WidgetsListHeader xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:launcher="http://schemas.android.com/apk/res-auto"
    android:id="@+id/widgets_list_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:importantForAccessibility="yes"
    android:focusable="true"
    launcher:appIconSize="48dp"
    android:descendantFocusability="afterDescendants"
    android:background="@drawable/bg_widgets_header" >

    <ImageView
        android:id="@+id/app_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:importantForAccessibility="no"
        tools:src="@drawable/ic_corp"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/app_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/widgetPickerHeaderAppTitleColor"
            android:textSize="@dimen/widget_picker_header_app_title_font_size"
            android:textFontWeight="@integer/widget_picker_header_app_title_font_weight"
            android:lineHeight="@dimen/widget_picker_header_app_title_line_height"
            tools:text="App name" />

        <TextView
            android:id="@+id/app_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/widgetPickerHeaderAppSubtitleColor"
            android:textSize="@dimen/widget_picker_header_app_subtitle_font_size"
            android:textFontWeight="@integer/widget_picker_header_app_subtitle_font_weight"
            android:lineHeight="@dimen/widget_picker_header_app_subtitle_line_height"
            tools:text="m widgets, n shortcuts" />

    </LinearLayout>

    <!-- This checkbox is not clickable. The outermost LinearLayout is responsible to handle all
         click event and update the checkbox state. -->
    <ImageView
        android:duplicateParentState="true"
        android:id="@+id/toggle"
        android:alpha=".6"
        android:src="@drawable/widgets_tray_expand_button"
        android:layout_marginHorizontal="16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_alignParentEnd="true"
        android:enabled="false"
        android:clickable="false"
        android:importantForAccessibility="no" />

</com.android.launcher3.widget.picker.WidgetsListHeader>