<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.android.launcher3.widget.WidgetCellPreview
        android:id="@+id/widget_preview_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:importantForAccessibility="noHideDescendants"
        android:hapticFeedbackEnabled="false"
        android:clipChildren="false"
        android:layout_marginVertical="8dp">
        <!-- The image of the widget. This view does not support padding. Any placement adjustment
             should be done using margins. Width & height are set at runtime after scaling the
             preview image. -->
        <com.android.launcher3.widget.WidgetImageView
            android:id="@+id/widget_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:importantForAccessibility="no"
            android:layout_gravity="fill"/>
    </com.android.launcher3.widget.WidgetCellPreview>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:id="@+id/widget_text_container"
            android:orientation="vertical">
                <!-- The name of the widget. -->
                <TextView
                    android:id="@+id/widget_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fadingEdge="horizontal"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center_horizontal|center_vertical"
                    android:singleLine="true"
                    android:maxLines="1"
                    android:textColor="?attr/widgetCellTitleColor"
                    android:textSize="@dimen/widget_cell_title_font_size"
                    android:textFontWeight="@integer/widget_cell_title_font_weight"
                    android:lineHeight="@dimen/widget_cell_title_line_height"
                    android:drawablePadding="@dimen/widget_cell_app_icon_padding" />

                <!-- The original dimensions of the widget -->
                <TextView
                    android:id="@+id/widget_dims"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:textColor="?attr/widgetCellSubtitleColor"
                    android:textSize="@dimen/widget_cell_dims_font_size"
                    android:textFontWeight="@integer/widget_cell_dims_font_weight"
                    android:lineHeight="@dimen/widget_cell_dims_line_height" />

                <TextView
                    android:id="@+id/widget_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:textColor="?attr/widgetCellSubtitleColor"
                    android:textSize="@dimen/widget_cell_description_font_size"
                    android:textFontWeight="@integer/widget_cell_description_font_weight"
                    android:lineHeight="@dimen/widget_cell_description_line_height"
                    android:maxLines="3"
                    android:ellipsize="end"
                    android:fadingEdge="horizontal" />
        </LinearLayout>

        <Button
            android:id="@+id/widget_add_button"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/widget_cell_add_button_height"
            android:layout_gravity="center"
            android:minWidth="0dp"
            android:paddingStart="@dimen/widget_cell_add_button_start_padding"
            android:paddingEnd="@dimen/widget_cell_add_button_end_padding"
            android:text="@string/widget_add_button_label"
            android:textColor="?attr/widgetPickerAddButtonTextColor"
            android:textSize="@dimen/widget_cell_add_button_font_size"
            android:fontWeight="@integer/widget_cell_add_button_font_weight"
            android:lineHeight="@dimen/widget_cell_add_button_line_height"
            android:gravity="center"
            android:visibility="gone"
            android:drawableStart="@drawable/ic_plus"
            android:drawablePadding="@dimen/widget_cell_add_button_drawable_padding"
            android:drawableTint="?attr/widgetPickerAddButtonTextColor"
            android:maxLines="1"
            style="@style/Button.Rounded.Colored"
            android:background="@drawable/widget_cell_add_button_background" />
    </FrameLayout>
</merge>
