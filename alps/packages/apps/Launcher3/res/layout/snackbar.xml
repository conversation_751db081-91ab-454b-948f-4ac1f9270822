<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/label"
        android:layout_height="@dimen/snackbar_content_height"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:lines="1"
        android:ellipsize="end"
        android:textSize="@dimen/snackbar_max_text_size"
        android:textColor="?android:attr/textColorPrimary"
        style="@style/TextTitle"/>
    <TextView
        android:id="@+id/action"
        android:layout_height="@dimen/snackbar_content_height"
        android:layout_width="wrap_content"
        android:layout_weight="0"
        android:gravity="center"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:background="?android:attr/selectableItemBackground"
        android:longClickable="false"
        android:textStyle="bold"
        android:textSize="@dimen/snackbar_max_text_size"
        android:textColor="?android:attr/colorAccent"
        style="@style/TextTitle"
        android:capitalize="sentences"/>
</merge>