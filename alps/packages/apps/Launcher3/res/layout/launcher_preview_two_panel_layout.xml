<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<view class="com.android.launcher3.graphics.LauncherPreviewRenderer$LauncherPreviewLayout"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.android.launcher3.CellLayout
            android:id="@+id/workspace"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:theme="@style/HomeScreenElementTheme"
            launcher:containerType="workspace"
            launcher:layout_constraintStart_toStartOf="parent"
            launcher:layout_constraintTop_toTopOf="parent"
            launcher:layout_constraintEnd_toStartOf="@id/workspace_right"
            launcher:layout_constraintBottom_toBottomOf="parent"
            launcher:pageIndicator="@+id/page_indicator" />

        <com.android.launcher3.CellLayout
            android:id="@+id/workspace_right"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:theme="@style/HomeScreenElementTheme"
            launcher:containerType="workspace"
            launcher:layout_constraintStart_toEndOf="@id/workspace"
            launcher:layout_constraintTop_toTopOf="parent"
            launcher:layout_constraintEnd_toEndOf="parent"
            launcher:layout_constraintBottom_toBottomOf="parent"
            launcher:pageIndicator="@+id/page_indicator" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/hotseat"
        layout="@layout/hotseat" />

</view>