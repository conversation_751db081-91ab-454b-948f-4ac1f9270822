<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<com.android.launcher3.AppWidgetResizeFrame
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="0dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <!-- Frame -->
        <ImageView
            android:id="@+id/widget_resize_frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_margin="@dimen/resize_frame_margin"
            android:src="@drawable/widget_resize_frame" />

        <!-- Left -->
        <ImageView
            android:id="@+id/widget_resize_left_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|center_vertical"
            android:layout_marginLeft="@dimen/widget_handle_margin"
            android:src="@drawable/ic_widget_resize_handle"
            android:tint="?attr/workspaceAccentColor" />

        <!-- Top -->
        <ImageView
            android:id="@+id/widget_resize_top_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center_horizontal"
            android:layout_marginTop="@dimen/widget_handle_margin"
            android:src="@drawable/ic_widget_resize_handle"
            android:tint="?attr/workspaceAccentColor" />

        <!-- Right -->
        <ImageView
            android:id="@+id/widget_resize_right_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|center_vertical"
            android:layout_marginRight="@dimen/widget_handle_margin"
            android:src="@drawable/ic_widget_resize_handle"
            android:tint="?attr/workspaceAccentColor" />

        <!-- Bottom -->
        <ImageView
            android:id="@+id/widget_resize_bottom_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="@dimen/widget_handle_margin"
            android:src="@drawable/ic_widget_resize_handle"
            android:tint="?attr/workspaceAccentColor" />

        <ImageButton
            android:id="@+id/widget_reconfigure_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/widget_reconfigure_button_padding"
            android:layout_gravity="bottom|end"
            android:layout_marginBottom="@dimen/widget_reconfigure_button_margin"
            android:layout_marginEnd="@dimen/widget_reconfigure_button_margin"
            android:src="@drawable/widget_reconfigure_button_frame"
            android:background="?android:attr/selectableItemBackground"
            android:visibility="gone"
            android:contentDescription="@string/widget_reconfigure_button_content_description" />

    </FrameLayout>
</com.android.launcher3.AppWidgetResizeFrame>