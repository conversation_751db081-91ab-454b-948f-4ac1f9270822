<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:id="@+id/widgets_two_pane_sheet_recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="start"
        android:layout_gravity="start"
        android:clipChildren="false"
        android:layout_alignParentStart="true">

        <com.android.launcher3.widget.picker.WidgetsRecyclerView
            android:id="@+id/primary_widgets_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
            android:clipToPadding="false" />

        <!-- SearchAndRecommendationsView without the tab layout as well -->
        <com.android.launcher3.views.StickyHeaderLayout
            android:id="@+id/search_and_recommendations_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToOutline="true"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/search_bar_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                android:clipToPadding="false"
                android:elevation="0.1dp"
                android:paddingBottom="16dp"
                android:paddingHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
                launcher:layout_sticky="true">

                <include layout="@layout/widgets_search_bar" />
            </FrameLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/suggestions_header"
                android:layout_marginHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
                android:paddingBottom="16dp"
                android:background="?attr/widgetPickerPrimarySurfaceColor"
                launcher:layout_sticky="true">
            </FrameLayout>
        </com.android.launcher3.views.StickyHeaderLayout>
    </FrameLayout>
</merge>