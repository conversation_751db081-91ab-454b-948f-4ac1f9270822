<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<ImageView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/private_space_divider"
    android:importantForAccessibility="no"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/ps_app_divider_horizontal_padding"
    android:paddingVertical="@dimen/ps_app_divider_vertical_padding"
    android:src="@drawable/private_space_app_divider"
    android:scaleType="fitXY"
    android:focusable="false" />