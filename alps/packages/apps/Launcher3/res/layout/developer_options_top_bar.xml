<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:showDividers="middle">

    <EditText
        android:id="@+id/filter_box"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/developer_options_filter_margins"
        android:background="@drawable/rounded_action_button"
        android:layout_marginTop="4dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:padding="12dp"
        android:drawableStart="@drawable/ic_allapps_search"
        android:drawableTint="?android:attr/textColorSecondary"
        android:drawablePadding="8dp"
        android:hint="@string/developer_options_filter_hint"
        android:inputType="text"
        android:maxLines="1"
        android:imeOptions="actionDone"
        />
</LinearLayout>