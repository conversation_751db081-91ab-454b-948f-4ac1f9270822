<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.secondarydisplay.SecondaryDragLayer
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/drag_layer"
    android:clipChildren="false"
    android:padding="@dimen/dynamic_grid_edge_margin">

    <GridView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="100dp"
        android:theme="@style/HomeScreenElementTheme"
        android:layout_gravity="center_horizontal|top"
        android:layout_margin="@dimen/dynamic_grid_edge_margin"
        android:id="@+id/workspace_grid" />

    <ImageButton
        android:id="@+id/all_apps_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="40dp"
        android:padding="16dp"
        android:src="@drawable/ic_apps"
        android:background="@drawable/bg_all_apps_button"
        android:contentDescription="@string/all_apps_button_label"
        android:onClick="onAppsButtonClicked" />

    <com.android.launcher3.allapps.SecondaryLauncherAllAppsContainerView
        android:id="@+id/apps_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="true"
        android:clipToPadding="false"
        android:focusable="false"
        android:saveEnabled="false"
        android:layout_gravity="bottom|end"
        android:background="@drawable/round_rect_primary"
        android:visibility="invisible" />
</com.android.launcher3.secondarydisplay.SecondaryDragLayer>