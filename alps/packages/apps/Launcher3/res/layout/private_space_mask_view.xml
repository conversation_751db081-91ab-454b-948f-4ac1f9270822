<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<com.android.launcher3.allapps.FloatingMaskView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:layout_marginLeft="@dimen/ps_floating_mask_end_padding"
    android:layout_marginRight="@dimen/ps_floating_mask_end_padding"
    android:importantForAccessibility="noHideDescendants"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/left_corner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:importantForAccessibility="no"
        android:background="@drawable/bg_ps_mask_left_corner"/>

    <ImageView
        android:id="@+id/right_corner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        android:importantForAccessibility="no"
        android:background="@drawable/bg_ps_mask_right_corner"/>

    <ImageView
        android:id="@+id/bottom_box"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@id/left_corner"
        app:layout_constraintEnd_toEndOf="@id/right_corner"
        app:layout_constraintTop_toBottomOf="@id/left_corner"
        android:importantForAccessibility="no"
        android:background="?attr/allAppsScrimColor"/>

</com.android.launcher3.allapps.FloatingMaskView>