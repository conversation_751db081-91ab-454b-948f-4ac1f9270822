<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto"
    android:id="@+id/widgets_cell_list_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:focusable="true"
    android:descendantFocusability="afterDescendants">

    <!-- Section info -->

    <com.android.launcher3.BubbleTextView
        android:id="@+id/section"
        android:layout_width="match_parent"
        android:layout_height="@dimen/widget_section_height"
        android:background="?android:attr/colorPrimary"
        android:drawablePadding="@dimen/widget_section_horizontal_padding"
        android:focusable="true"
        android:gravity="start|center_vertical"
        android:paddingBottom="@dimen/widget_section_vertical_padding"
        android:paddingLeft="@dimen/widget_section_horizontal_padding"
        android:paddingRight="@dimen/widget_section_horizontal_padding"
        android:paddingTop="@dimen/widget_section_vertical_padding"
        android:singleLine="true"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp"
        android:textAlignment="viewStart"
        launcher:iconDisplay="widget_section"
        launcher:iconSizeOverride="@dimen/widget_section_icon_size"
        launcher:layoutHorizontal="true" />

    <include layout="@layout/widgets_table_container" />
</LinearLayout>
