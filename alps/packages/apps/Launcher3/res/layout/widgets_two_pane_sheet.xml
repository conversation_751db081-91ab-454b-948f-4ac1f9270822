<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.widget.picker.WidgetsTwoPaneSheet
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:theme="?attr/widgetsTheme">

    <com.android.launcher3.views.SpringRelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:importantForAccessibility="no">

        <View
            android:id="@+id/collapse_handle"
            android:layout_width="@dimen/bottom_sheet_handle_width"
            android:layout_height="@dimen/bottom_sheet_handle_height"
            android:layout_marginTop="@dimen/bottom_sheet_handle_margin"
            android:layout_centerHorizontal="true"
            android:background="@drawable/widget_picker_collapse_handle"/>

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center_horizontal"
            android:layout_below="@id/collapse_handle"
            android:paddingHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
            android:text="@string/widget_button_text"
            android:textColor="?attr/widgetPickerTitleColor"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/no_widgets_text"
            style="@style/PrimaryHeadline"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textSize="18sp"
            android:visibility="gone"
            tools:text="@string/no_widgets_available" />

        <LinearLayout
            android:id="@+id/linear_layout_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/title">

            <FrameLayout
                android:id="@+id/recycler_view_container"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingBottom="24dp"
                android:layout_gravity="start"
                android:layout_weight="0.33">
                <TextView
                    android:id="@+id/fast_scroller_popup"
                    style="@style/FastScrollerPopup"
                    android:layout_marginEnd="@dimen/fastscroll_popup_margin" />

                <!-- Fast scroller popup -->
                <com.android.launcher3.views.RecyclerViewFastScroller
                    android:id="@+id/fast_scroller"
                    android:layout_width="@dimen/fastscroll_width"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/fastscroll_end_margin" />

                <com.android.launcher3.widget.picker.WidgetsRecyclerView
                    android:id="@+id/search_widgets_list_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:paddingHorizontal="@dimen/widget_list_horizontal_margin_two_pane"
                    android:visibility="gone" />
            </FrameLayout>

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.67">
                <FrameLayout
                    android:id="@+id/right_pane_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginVertical="@dimen/widget_picker_vertical_margin_right_pane"
                    android:layout_marginEnd="@dimen/widget_list_horizontal_margin_two_pane"
                    android:gravity="end"
                    android:layout_gravity="end"
                    android:orientation="horizontal">
                    <ScrollView
                        android:id="@+id/right_pane_scroll_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/widgets_surface_background"
                        android:scrollbarThumbVertical="@drawable/widget_picker_preview_pane_scroll_thumb"
                        android:clipToOutline="true"
                        android:fillViewport="true">
                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:clipToOutline="true"
                            android:paddingBottom="36dp"
                            android:background="@drawable/widgets_surface_background"
                            android:importantForAccessibility="yes"
                            android:id="@+id/right_pane">
                            <!-- Shown when there are recommendations to display -->
                            <LinearLayout
                                android:id="@+id/widget_recommendations_container"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/widgets_surface_background"
                                android:orientation="vertical"
                                android:visibility="gone">
                                <include layout="@layout/widget_recommendations" />
                            </LinearLayout>
                        </LinearLayout>
                    </ScrollView>
                </FrameLayout>
            </FrameLayout>
        </LinearLayout>
    </com.android.launcher3.views.SpringRelativeLayout>
</com.android.launcher3.widget.picker.WidgetsTwoPaneSheet>
