<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto">

    <com.android.launcher3.widget.picker.WidgetPagedView
        android:id="@+id/widgets_view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:layout_below="@id/collapse_handle"
        android:descendantFocusability="afterDescendants"
        launcher:pageIndicator="@+id/tabs" >

        <com.android.launcher3.widget.picker.WidgetsRecyclerView
            android:id="@+id/primary_widgets_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/widget_list_horizontal_margin"
            android:clipToPadding="false" />

        <com.android.launcher3.widget.picker.WidgetsRecyclerView
            android:id="@+id/work_widgets_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/widget_list_horizontal_margin"
            android:clipToPadding="false" />

    </com.android.launcher3.widget.picker.WidgetPagedView>

    <!-- SearchAndRecommendationsView contains the tab layout as well -->
    <com.android.launcher3.views.StickyHeaderLayout
        android:id="@+id/search_and_recommendations_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/collapse_handle"
        android:paddingBottom="0dp"
        android:clipToOutline="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textSize="24sp"
            android:layout_marginTop="24dp"
            android:textColor="?attr/widgetPickerTitleColor"
            android:text="@string/widget_button_text"/>

        <FrameLayout
            android:id="@+id/search_bar_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="0.1dp"
            android:paddingHorizontal="@dimen/widget_list_horizontal_margin"
            android:background="?attr/widgetPickerPrimarySurfaceColor"
            android:paddingBottom="8dp"
            launcher:layout_sticky="true">
            <include layout="@layout/widgets_search_bar" />
        </FrameLayout>

        <!-- Shown when there are recommendations to display -->
        <LinearLayout
            android:id="@+id/widget_recommendations_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widgets_surface_background"
            android:orientation="vertical"
            android:layout_marginHorizontal="@dimen/widget_list_horizontal_margin"
            android:visibility="gone">
            <include layout="@layout/widget_recommendations" />
        </LinearLayout>

        <com.android.launcher3.workprofile.PersonalWorkSlidingTabStrip
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            android:paddingBottom="8dp"
            android:paddingHorizontal="@dimen/widget_list_horizontal_margin"
            android:background="?attr/widgetPickerPrimarySurfaceColor"
            style="@style/TextHeadline"
            launcher:layout_sticky="true">

            <Button
                android:id="@+id/tab_personal"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/widget_tabs_button_horizontal_padding"
                android:layout_marginVertical="@dimen/widget_apps_tabs_vertical_padding"
                android:layout_weight="1"
                android:background="@drawable/widget_picker_tabs_background"
                android:text="@string/widgets_full_sheet_personal_tab"
                android:textColor="@color/widget_picker_tab_text"
                android:textSize="14sp"
                style="?android:attr/borderlessButtonStyle" />

            <Button
                android:id="@+id/tab_work"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/widget_tabs_button_horizontal_padding"
                android:layout_marginVertical="@dimen/widget_apps_tabs_vertical_padding"
                android:layout_weight="1"
                android:background="@drawable/widget_picker_tabs_background"
                android:text="@string/widgets_full_sheet_work_tab"
                android:textColor="@color/widget_picker_tab_text"
                android:textSize="14sp"
                style="?android:attr/borderlessButtonStyle" />
        </com.android.launcher3.workprofile.PersonalWorkSlidingTabStrip>

    </com.android.launcher3.views.StickyHeaderLayout>
</merge>