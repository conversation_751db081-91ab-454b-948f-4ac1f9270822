<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bottom_sheet_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bottom_sheet_handle_area"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_sheet_handle_area_height" />

    <View
        android:id="@+id/bottom_sheet_handle"
        android:layout_width="@dimen/bottom_sheet_handle_width"
        android:layout_height="@dimen/bottom_sheet_handle_height"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/bottom_sheet_handle_margin"
        android:layout_marginBottom="@dimen/bottom_sheet_handle_margin"
        android:background="@drawable/bg_rounded_corner_bottom_sheet_handle" />
</FrameLayout>
