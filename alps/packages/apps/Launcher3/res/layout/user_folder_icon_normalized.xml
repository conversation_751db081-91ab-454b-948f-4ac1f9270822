<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.folder.Folder xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:defaultFocusHighlightEnabled="false"
    android:orientation="vertical" >

    <com.android.launcher3.folder.FolderPagedView
        android:id="@+id/folder_content"
        android:clipToPadding="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        launcher:pageIndicator="@+id/folder_page_indicator" />

    <LinearLayout
        android:id="@+id/folder_footer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/folder_footer_height_default"
        android:clipChildren="false"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/folder_footer_horiz_padding"
        android:paddingRight="@dimen/folder_footer_horiz_padding">

        <com.android.launcher3.folder.FolderNameEditText
            android:id="@+id/folder_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            style="@style/TextHeadline"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:gravity="center_horizontal"
            android:hint="@string/folder_hint_text"
            android:imeOptions="flagNoExtractUi"
            android:importantForAutofill="no"
            android:singleLine="true"
            android:textColor="?attr/folderTextColor"
            android:textColorHighlight="?android:attr/colorControlHighlight"
            android:textColorHint="?attr/folderHintTextColor"/>

        <com.android.launcher3.pageindicators.PageIndicatorDots
            android:id="@+id/folder_page_indicator"
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:elevation="1dp"
            />

    </LinearLayout>

</com.android.launcher3.folder.Folder>
