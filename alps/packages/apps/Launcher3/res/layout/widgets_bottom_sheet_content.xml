<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:id="@+id/widgets_bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/bottom_sheet_handle_margin"
        android:orientation="vertical">
        <View
            android:id="@+id/collapse_handle"
            android:layout_width="@dimen/bottom_sheet_handle_width"
            android:layout_height="@dimen/bottom_sheet_handle_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/widgets_bottom_sheet_handle_margin"
            android:visibility="gone"
            android:background="@drawable/bg_rounded_corner_bottom_sheet_handle"/>
        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="24sp"/>

        <ScrollView
            android:id="@+id/widgets_table_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:layout_marginHorizontal="@dimen/widget_bottom_sheet_horizontal_margin"
            android:background="@drawable/widgets_surface_background"
            android:scrollbarThumbVertical="@drawable/widget_picker_preview_pane_scroll_thumb"
            android:clipToOutline="true"
            android:clipChildren="true">
            <include layout="@layout/widgets_table_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/widget_list_horizontal_margin"
                android:layout_gravity="center_horizontal" />
        </ScrollView>
    </LinearLayout>
</merge>
