<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto">
    <!-- Fast scroller popup -->
    <TextView
        android:id="@+id/fast_scroller_popup"
        style="@style/FastScrollerPopup"
        android:layout_alignParentEnd="true"
        android:layout_alignTop="@+id/all_apps_header"
        android:layout_marginTop="@dimen/all_apps_header_bottom_padding"
        android:layout_marginEnd="@dimen/fastscroll_popup_margin" />

    <com.android.launcher3.views.RecyclerViewFastScroller
        android:id="@+id/fast_scroller"
        android:layout_width="@dimen/fastscroll_width"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_alignTop="@+id/all_apps_header"
        android:layout_marginTop="@dimen/all_apps_header_bottom_padding"
        android:layout_marginEnd="@dimen/fastscroll_end_margin"
        launcher:canThumbDetach="true" />

</merge>