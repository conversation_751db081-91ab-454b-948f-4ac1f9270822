<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.launcher3.shortcuts.DeepShortcutView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:launcher="http://schemas.android.com/apk/res-auto"
    android:id="@+id/deep_shortcut_material"
    android:layout_width="@dimen/bg_popup_item_width"
    android:layout_height="@dimen/bg_popup_item_height"
    android:elevation="@dimen/deep_shortcuts_elevation"
    android:background="@drawable/middle_item_primary"
    android:theme="@style/PopupItem" >

    <com.android.launcher3.shortcuts.DeepShortcutTextView
        style="@style/BaseIcon"
        android:id="@+id/bubble_text"
        android:background="?android:attr/selectableItemBackground"
        android:gravity="start|center_vertical"
        android:textAlignment="viewStart"
        android:paddingStart="@dimen/deep_shortcuts_text_padding_start"
        android:paddingEnd="@dimen/popup_padding_end"
        android:drawablePadding="@dimen/deep_shortcut_drawable_padding"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="14sp"
        android:textColor="?attr/popupTextColor"
        launcher:layoutHorizontal="true"
        launcher:iconDisplay="shortcut_popup"
        launcher:iconSizeOverride="@dimen/deep_shortcut_icon_size" />

    <View
        android:id="@+id/icon"
        android:layout_width="@dimen/deep_shortcut_icon_size"
        android:layout_height="@dimen/deep_shortcut_icon_size"
        android:layout_marginStart="@dimen/popup_padding_start"
        android:layout_gravity="start|center_vertical"
        android:background="@drawable/ic_deepshortcut_placeholder"/>
</com.android.launcher3.shortcuts.DeepShortcutView>