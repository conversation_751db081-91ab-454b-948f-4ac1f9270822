<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <!-- Miscellaneous -->
    <bool name="config_largeHeap">false</bool>

    <!-- A string pointer to the original app name string. This allows derived projects to
     easily override the app name without providing all translations -->
    <string name="derived_app_name" translatable="false">@string/app_name</string>

    <!-- String representing the intent to delete a package.-->
    <string name="delete_package_intent" translatable="false">#Intent;action=android.intent.action.DELETE;launchFlags=0x10800000;end</string>

    <!-- String representing the fragment class for settings activity.-->
    <string name="settings_fragment_name" translatable="false">com.android.launcher3.settings.SettingsActivity$LauncherSettingsFragment</string>

    <!-- AllApps & Launcher transitions -->
    <!-- The duration of the PagedView page snap animation -->
    <integer name="config_pageSnapAnimationDuration">750</integer>

    <!-- The duration of the PagedView page snap animation -->
    <integer name="config_keyboardTaskFocusSnapAnimationDuration">750</integer>

    <!-- View tag key used to determine if we should fade in the child views.. -->
    <string name="popup_container_iterate_children" translatable="false">popup_container_iterate_children</string>

    <!-- Workspace -->
    <!-- The duration (in ms) of the fade animation on the object outlines, used when
         we are dragging objects around on the home screen. -->
    <integer name="config_dragOutlineFadeTime">500</integer>

    <!-- The alpha value at which to show the most recent drop visualization outline. -->
    <integer name="config_dragOutlineMaxAlpha">255</integer>

    <!-- Parameters controlling the animation for when an item is dropped on the home screen,
         and it animates from its old position to the new one. -->
    <integer name="config_dropAnimMinDuration">100</integer>
    <integer name="config_dropAnimMaxDuration">500</integer>

    <!-- The duration of the UserFolder opening and closing animation -->
    <integer name="config_materialFolderExpandDuration">200</integer>
    <integer name="config_folderDelay">30</integer>

    <!-- The distance at which the animation should take the max duration -->
    <integer name="config_dropAnimMaxDist">800</integer>

    <!-- The duration of the caret animation -->
    <integer name="config_caretAnimationDuration">200</integer>

    <!-- Various classes overriden by projects/build flavors. -->
    <string name="folder_name_provider_class" translatable="false"></string>
    <string name="stats_log_manager_class" translatable="false"></string>
    <string name="instant_app_resolver_class" translatable="false"></string>
    <string name="main_process_initializer_class" translatable="false"></string>
    <string name="app_launch_tracker_class" translatable="false"></string>
    <string name="test_information_handler_class" translatable="false"></string>
    <string name="launcher_activity_logic_class" translatable="false"></string>
    <string name="model_delegate_class" translatable="false"></string>
    <string name="window_manager_proxy_class" translatable="false"></string>
    <string name="secondary_display_predictions_class" translatable="false"></string>
    <string name="widget_holder_factory_class" translatable="false"></string>
    <string name="taskbar_search_session_controller_class" translatable="false"></string>
    <string name="taskbar_model_callbacks_factory_class" translatable="false"></string>
    <string name="taskbar_view_callbacks_factory_class" translatable="false"></string>
    <string name="launcher_restore_event_logger_class" translatable="false"></string>
    <string name="taskbar_edu_tooltip_controller_class" translatable="false"></string>
    <!--  Used for determining category of a widget presented in widget recommendations. -->
    <string name="widget_recommendation_category_provider_class" translatable="false"></string>
    <string name="api_wrapper_class" translatable="false"></string>

    <!-- Default packages -->
    <!-- Modify DAHLIA-737 for wallpaper customization by mlj begin -->

    <!--A01-909 change to AOSP wallpaper zhaoxian start-->
    <string name="wallpaper_picker_package" translatable="false">com.android.wallpaper</string>
    <!--A01-909 change to AOSP wallpaper zhaoxian end-->

    <!-- Modify DAHLIA-737 for wallpaper customization by mlj end -->
    <string name="local_colors_extraction_class" translatable="false"></string>
    <string name="search_session_manager_class" translatable="false"></string>
    <string name="plugin_manager_wrapper_class" translatable="false"></string>

    <!-- Scalable Grid configuration -->
    <!-- This is a float because it is converted to dp later in DeviceProfile -->
    <dimen name="hotseat_bar_bottom_space_default">48</dimen>
    <dimen name="hotseat_qsb_space_default">0</dimen>

    <!-- Whether to enable background preloading of task thumbnails. -->
    <bool name="config_enableTaskSnapshotPreloading">true</bool>

    <!-- Configuration resources -->
    <item name="dismiss_task_trans_y_damping_ratio" type="dimen" format="float">0.73</item>
    <item name="dismiss_task_trans_y_stiffness" type="dimen" format="float">800</item>

    <item name="swipe_up_rect_scale_damping_ratio" type="dimen" format="float">0.75</item>
    <item name="swipe_up_rect_scale_stiffness" type="dimen" format="float">200</item>
    <item name="swipe_up_rect_scale_higher_stiffness" type="dimen" format="float">400</item>
    <!-- Flag: enableScalingRevealHomeAnimation() -->
    <item name="swipe_up_rect_scale_damping_ratio_v2" type="dimen" format="float">0.99</item>
    <item name="swipe_up_rect_scale_stiffness_v2" type="dimen" format="float">500</item>

    <item name="swipe_up_rect_xy_fling_friction" type="dimen" format="float">1.5</item>

    <item name="swipe_up_scale_start"  type="dimen" format="float">0.88</item>

    <item name="swipe_up_rect_xy_damping_ratio" type="dimen" format="float">0.8</item>
    <item name="swipe_up_rect_xy_stiffness" type="dimen" format="float">200</item>
    <!-- Flag: enableScalingRevealHomeAnimation() -->
    <item name="swipe_up_rect_x_damping_ratio" type="dimen" format="float">0.965</item>
    <item name="swipe_up_rect_x_stiffness" type="dimen" format="float">450</item>
    <item name="swipe_up_rect_y_damping_ratio" type="dimen" format="float">0.95</item>
    <item name="swipe_up_rect_y_stiffness" type="dimen" format="float">400</item>

    <!-- Taskbar -->
    <!-- This is a float because it is converted to dp later in DeviceProfile -->
    <item name="taskbar_icon_size" type="dimen" format="float">0</item>

    <!-- These params are only used for hotseat items on devices that have a taskbar. -->
    <item name="taskbar_swipe_up_rect_x_stiffness" type="dimen" format="float">350</item>
    <item name="taskbar_swipe_up_rect_x_damping" type="dimen" format="float">0.9</item>
    <item name="taskbar_swipe_up_rect_y_stiffness" type="dimen" format="float">200</item>
    <item name="taskbar_swipe_up_rect_y_damping" type="dimen" format="float">0.78</item>
    <item name="taskbar_swipe_up_rect_scale_stiffness" type="dimen" format="float">200</item>

    <item name="staggered_damping_ratio" type="dimen" format="float">0.7</item>
    <item name="staggered_stiffness" type="dimen" format="float">150</item>
    <dimen name="unlock_staggered_velocity_dp_per_s">2dp</dimen>

    <item name="hint_scale_damping_ratio" type="dimen" format="float">0.7</item>
    <item name="hint_scale_stiffness" type="dimen" format="float">200</item>
    <dimen name="hint_scale_velocity_dp_per_s">0.3dp</dimen>

    <!-- Swipe up to home related -->
    <dimen name="swipe_up_fling_min_visible_change">18dp</dimen>
    <dimen name="swipe_up_max_workspace_trans_y">-60dp</dimen>
    <dimen name="swipe_up_max_velocity">7.619dp</dimen>
    <!-- Flag: enableScalingRevealHomeAnimation() -->
    <item name="swipe_up_min_velocity_x_px_per_s" type="dimen" format="integer">300</item>
    <item name="swipe_up_max_velocity_x_px_per_s" type="dimen" format="integer">500</item>
    <item name="swipe_up_min_velocity_y_px_per_s" type="dimen" format="integer">2000</item>
    <item name="swipe_up_max_velocity_y_px_per_s" type="dimen" format="integer">5000</item>
    <item name="swipe_up_max_velocity_fall_off_factor" type="dimen" format="float">1.4</item>

    <array name="dynamic_resources">
        <item>@dimen/swipe_up_scale_start</item>
        <item>@dimen/swipe_up_max_velocity</item>
    </array>

    <string-array name="filtered_components" ></string-array>

    <!-- Swipe back to home related -->
    <dimen name="swipe_back_window_scale_x_margin">10dp</dimen>
    <dimen name="swipe_back_window_corner_radius">40dp</dimen>

    <!-- The duration of the bottom sheet opening and closing animation -->
    <integer name="config_bottomSheetOpenDuration">267</integer>
    <integer name="config_bottomSheetCloseDuration">267</integer>

    <!-- The duration of the AllApps opening and closing animation -->
    <integer name="config_allAppsOpenDuration">600</integer>
    <integer name="config_allAppsCloseDuration">300</integer>

    <!-- The max scale for the wallpaper when it's zoomed in -->
    <item name="config_wallpaperMaxScale" format="float" type="dimen">0</item>

    <!-- Whether the floating rotation button should be on the left/right in the device's natural
         orientation -->
    <bool name="floating_rotation_button_position_left">true</bool>

    <!--  Mapping of visual icon size to XML value http://b/235886078  -->
    <dimen name="iconSize48dp">52dp</dimen>
    <dimen name="iconSize50dp">55dp</dimen>
    <dimen name="iconSize52dp">57dp</dimen>
    <dimen name="iconSize54dp">59dp</dimen>
    <dimen name="iconSize56dp">61dp</dimen>
    <dimen name="iconSize58dp">63dp</dimen>
    <dimen name="iconSize60dp">66dp</dimen>
    <dimen name="iconSize66dp">72dp</dimen>
    <dimen name="iconSize72dp">79dp</dimen>
    <dimen name="iconSize82dp">90dp</dimen>
    <dimen name="iconSize110dp">121dp</dimen>
    <dimen name="iconSize144dp">158dp</dimen>

    <!--SR:MMI modify for DAHLIA-1048 app icon need display in idle if downloading from Orange APP center by zhanghuabo 2024.11.23 begin-->
    <string-array name="config_app_center_installed_app_add_in_idle_screen_ecid">
        <item>15632003</item>
        <item>15652002</item>
        <item>15613002</item>
        <item>15624002</item>
        <item>15623003</item>
        <item>15611001</item>
        <item>15612003</item>
        <item>15630086</item>
        <item>35602001</item>
        <item>15627001</item>
        <item>31416077</item>
        <item>15618007</item>
        <item>15646002</item>
        <item>15610002</item>
        <item>15617001</item>
        <item>15604000</item>
        <item>15614004</item>
        <item>15608001</item>
        <item>15619001</item>
        <item>35605001</item>
        <item>11541001</item>
    </string-array>
    <!--SR:MMI modify for DAHLIA-1048 app icon need display in idle if downloading from Orange APP center by zhanghuabo 2024.11.23 end-->

    <!--  Icon size steps in dp  -->
    <integer-array name="icon_size_steps">
        <item>@dimen/iconSize48dp</item>
        <item>@dimen/iconSize50dp</item>
        <item>@dimen/iconSize52dp</item>
        <item>@dimen/iconSize54dp</item>
        <item>@dimen/iconSize56dp</item>
        <item>@dimen/iconSize58dp</item>
        <item>@dimen/iconSize60dp</item>
        <item>@dimen/iconSize66dp</item>
        <item>@dimen/iconSize72dp</item>
        <item>@dimen/iconSize82dp</item>
        <item>@dimen/iconSize110dp</item>
        <item>@dimen/iconSize144dp</item>
    </integer-array>

    <dimen name="minimum_icon_label_size">8sp</dimen>

    <!--  Used for custom widgets  -->
    <array name="custom_widget_providers"/>

    <!-- Embed parameters -->
    <dimen name="activity_split_ratio"  format="float">0.5</dimen>
    <integer name="min_width_split">720</integer>

    <!-- Skip "Install to private" long-press shortcut packages name -->
    <string-array name="skip_private_profile_shortcut_packages" translatable="false">
        <item>com.android.settings</item>
    </string-array>

    <!-- Legacy list of components supporting multiple instances.
         DO NOT ADD TO THIS LIST.  Apps should use the PROPERTY_SUPPORTS_MULTI_INSTANCE_SYSTEM_UI
         property to declare multi-instance support in V+. This resource should match the resource
         of the same name in SystemUI. -->
    <string-array name="config_appsSupportMultiInstancesSplit">
    </string-array>

    <!--SR-MMI: modify for DAHLIA-311 Apk names by zhanghuaibo 2024.11.18 begin DAHLIA-2009 add 23000000 DAHLIA-2307 add 13000001/33000001/13330110/13370002 begin-->
    <string-array name="config_apps_name_two_lines_ecid">
        <item>33732101</item>
        <item>33716010</item>
        <item>33716017</item>
        <item>23000000</item>
        <item>13000001</item>
        <item>33000001</item>
        <item>13330110</item>
        <item>13370002</item>
    </string-array>
    <!--SR-MMI: modify for DAHLIA-311 Apk names by zhanghuaibo 2024.11.18 end DAHLIA-2009 add 23000000 DAHLIA-2307 add 13000001/33000001/13330110/13370002 end-->
</resources>
