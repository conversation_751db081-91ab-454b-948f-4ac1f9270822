<?xml version="1.0" encoding="utf-8"?><!--
/*
* Copyright (C) 2008 The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- General -->
    <skip />

    <!-- Application name -->
    <string name="app_name">Launcher3</string>
    <!-- Work folder name -->
    <string name="work_folder_name">Work</string>
    <!-- Displayed when user selects a shortcut for an app that was uninstalled [CHAR_LIMIT=none]-->
    <string name="activity_not_found">App isn\'t installed.</string>
    <!-- Displayed when user selects a shortcut for an app that is current not available [CHAR_LIMIT=none]-->
    <string name="activity_not_available">App isn\'t available</string>
    <!-- SafeMode shortcut error string -->
    <string name="safemode_shortcut_error">Downloaded app disabled in Safe mode</string>
    <!-- SafeMode widget error string -->
    <string name="safemode_widget_error">Widgets disabled in Safe mode</string>
    <!-- Message shown when a shortcut is not available. It could have been temporarily disabled and may start working again after some time. -->
    <string name="shortcut_not_available">Shortcut isn\'t available</string>
    <!-- User visible name for the launcher/home screen. [CHAR_LIMIT=30] -->
    <string name="home_screen">Home</string>
    <!-- Description for setting the current launcher as the default home app. [CHAR_LIMIT=none]-->
    <string name="set_default_home_app">Set <xliff:g id="launcher_name" example="Launcher3">%1$s</xliff:g> as default home app in Settings</string>

    <!-- Options for recent tasks -->
    <!-- Title for an option to enter split screen mode for a given app -->
    <string name="recent_task_option_split_screen">Split screen</string>
    <string name="split_app_info_accessibility">App info for %1$s</string>
    <string name="split_app_usage_settings">Usage settings for %1$s</string>

    <!-- App pairs -->
    <string name="save_app_pair">Save app pair</string>
    <!-- App pair default title -->
    <string name="app_pair_default_title"><xliff:g id="app1" example="Chrome">%1$s</xliff:g> | <xliff:g id="app2" example="YouTube">%2$s</xliff:g></string>
    <!-- Displayed when an app pair can't launch at this screen size [CHAR_LIMIT=none] -->
    <string name="app_pair_unlaunchable_at_screen_size">This app pair isn\'t supported on this device</string>
    <!-- Displayed when an app pair can't launch at this screen size, but user can unfold device to restore functionality [CHAR_LIMIT=none] -->
    <string name="app_pair_needs_unfold">Unfold device to use this app pair</string>
    <!-- Displayed when user selects a shortcut for an app pair that is currently not available [CHAR_LIMIT=none]-->
    <string name="app_pair_not_available">App pair isn\'t available</string>

    <!-- Widgets -->
    <!-- Message to tell the user to press and hold on a widget to add it [CHAR_LIMIT=50] -->
    <string name="long_press_widget_to_add">Touch &amp; hold to move a widget.</string>
    <!-- Accessibility spoken hint message in widget picker, which allows user to add a widget. Custom action is the label for additional accessibility actions available in this mode [CHAR_LIMIT=100] -->
    <string name="long_accessible_way_to_add">Double-tap &amp; hold to move a widget or use custom actions.</string>
    <!-- The format string for the dimensions of a widget in the drawer -->
    <!-- There is a special version of this format string for Farsi -->
    <string name="widget_dims_format">%1$d \u00d7 %2$d</string>
    <!-- Accessibility spoken message format for the dimensions of a widget in the drawer -->
    <string name="widget_accessible_dims_format">%1$d wide by %2$d high</string>
    <!-- Spoken text for a screen reader. The placeholder text is the widget name.
         [CHAR_LIMIT=none]-->
    <string name="widget_preview_context_description"><xliff:g id="widget_name" example="Calendar month view">%1$s</xliff:g> widget</string>
    <!-- Spoken text for a screen reader. The first placeholder text is the widget name, the
         remaining placeholders are for the widget dimensions.
         [CHAR_LIMIT=none]-->
    <string name="widget_preview_name_and_dims_content_description">
        <xliff:g id="widget_name" example="Calendar month view">%1$s</xliff:g> widget, %2$d wide by %3$d high
    </string>
    <!-- Message to tell the user to press and hold a widget/icon to add it to the home screen.
         [CHAR LIMIT=NONE]  -->
    <string name="add_item_request_drag_hint">Touch &amp; hold the widget to move it around the home screen</string>
    <!-- Button label to automatically add a widget to home screen [CHAR_LIMIT=50] -->
    <string name="add_to_home_screen">Add to home screen</string>
    <!-- Accessibility spoken message announced when a widget gets added to the home screen using a
         button in a dialog. [CHAR_LIMIT=none] -->
    <string name="added_to_home_screen_accessibility_text"><xliff:g id="widget_name" example="Calendar month view">%1$s</xliff:g> widget added to home screen</string>
    <!-- Widget suggestions header title in the full widgets picker for large screen devices
    in landscape mode. [CHAR_LIMIT=50] -->
    <string name="suggested_widgets_header_title">Suggestions</string>
    <!-- Title for the widget suggestions category that displays widgets provided by
    productivity apps for daily use [CHAR_LIMIT=50] -->
    <string name="productivity_widget_recommendation_category_label">Essentials</string>
    <!-- Title for the widget suggestions category that displays widgets provided by
    news and magazines related apps [CHAR_LIMIT=50] -->
    <string name="news_widget_recommendation_category_label">News &amp; magazines</string>
    <!-- Title for the widget suggestions category that displays widgets provided by
    entertainment apps [CHAR_LIMIT=50] -->
    <string name="entertainment_widget_recommendation_category_label">Entertainment</string>
    <!-- Title for the widget suggestions category that displays widgets provided by
    social apps [CHAR_LIMIT=50] -->
    <string name="social_widget_recommendation_category_label">Social</string>
    <!-- Title for the widget suggestions category that displays general widget suggestions
    [CHAR_LIMIT=50] -->
    <string name="others_widget_recommendation_category_label">Suggested for you</string>
    <!-- accessibilityPaneTitle for the right pane when showing suggested widgets. -->
    <string name="widget_picker_right_pane_accessibility_title"><xliff:g id="selected_header" example="Calendar">%1$s</xliff:g> widgets on right, search and options on left</string>
    <!-- Label for showing the number of widgets an app has in the full widgets picker.
         [CHAR_LIMIT=25][ICU SYNTAX] -->
    <string name="widgets_count">
        {count, plural, =1{# widget} other{# widgets}}
    </string>
    <!-- Label for showing the number of shortcut an app has in the full widgets picker.
         [CHAR_LIMIT=25][ICU SYNTAX] -->
    <string name="shortcuts_count">
        {count, plural, =1{# shortcut} other{# shortcuts}}
    </string>
    <!-- Label for showing both the number of widgets and shortcuts an app has in the full widgets
         picker. [CHAR_LIMIT=50] -->
    <string name="widgets_and_shortcuts_count"><xliff:g id="widgets_count" example="5 widgets">%1$s</xliff:g>, <xliff:g id="shortcuts_count" example="1 shortcut">%2$s</xliff:g></string>

    <!-- Text for both the tile of a popup view, which shows all available widgets installed on
         the device, and the text of a button, which opens this popup view. [CHAR LIMIT=30]-->
    <string name="widget_button_text">Widgets</string>
    <!-- Search bar text shown in the popup view showing all available widgets installed on the
         device. [CHAR_LIMIT=50] -->
    <string name="widgets_full_sheet_search_bar_hint">Search</string>
    <!-- Spoken text for screen readers. This text lets a user know that the button is used to clear
         the text that the user entered in the search box. [CHAR_LIMIT=none] -->
    <string name="widgets_full_sheet_cancel_button_description">Clear text from search box</string>
    <!-- Text shown when there are no widgets or shortcuts that can be added to the device.
         [CHAR_LIMIT=none] -->
    <string name="no_widgets_available">Widgets and shortcuts aren\'t available</string>
    <!-- Text shown when there are no matching widget or shortcut search results for user's search
         query. [CHAR_LIMIT=none] -->
    <string name="no_search_results">No widgets or shortcuts found</string>
    <!-- Tab label. A user can tap this tab to access their personal widgets. [CHAR_LIMIT=25] -->
    <string name="widgets_full_sheet_personal_tab">Personal</string>
    <!-- Tab label. A user can tap this tab to access their work widgets. [CHAR_LIMIT=25] -->
    <string name="widgets_full_sheet_work_tab">Work</string>

    <!-- A widget category label for grouping widgets related to conversations. [CHAR_LIMIT=30] -->
    <string name="widget_category_conversations">Conversations</string>

    <!-- A widget category label for grouping widgets related to note taking. [CHAR_LIMIT=30] -->
    <string name="widget_category_note_taking">Note-taking</string>

    <!-- Text on the button that adds a widget to the home screen. [CHAR_LIMIT=15] -->
    <string name="widget_add_button_label">Add</string>
    <!-- Accessibility content description for the button that adds a widget to the home screen. The
         placeholder text is the widget name. [CHAR_LIMIT=none] -->
    <string name="widget_add_button_content_description">Add <xliff:g id="widget_name" example="Calendar month view">%1$s</xliff:g> widget</string>

    <!-- Text on an educational tip on widget informing users that they can change widget settings.
         [CHAR_LIMIT=NONE] -->
    <string name="reconfigurable_widget_education_tip">Tap to change widget settings</string>

    <!-- Spoken text for screen readers. This text is for an icon that lets the user change a
         widget's settings. [CHAR_LIMIT=50] -->
    <string name="widget_reconfigure_button_content_description">Change widget settings</string>

    <!-- All Apps -->
    <!-- Search bar text in the apps view. [CHAR_LIMIT=50] -->
    <string name="all_apps_search_bar_hint">Search apps</string>
    <!-- Loading apps text. [CHAR_LIMIT=50] -->
    <string name="all_apps_loading_message">Loading apps&#8230;</string>
    <!-- No-search-results text. [CHAR_LIMIT=50] -->
    <string name="all_apps_no_search_results">No apps found matching \"<xliff:g id="query" example="Android">%1$s</xliff:g>\"</string>
    <!-- Label for an icon representing any generic app. [CHAR_LIMIT=50] -->
    <string name="label_application">App</string>
    <!-- Label for the header text of the All Apps section in All Apps view, used to separate Predicted Apps and Actions section from All Apps section. [CHAR_LIMIT=50] -->
    <string name="all_apps_label">All apps</string>

    <!-- Popup items -->
    <!-- Text to display as the header above notifications. [CHAR_LIMIT=30] -->
    <string name="notifications_header">Notifications</string>

    <!-- Drag and drop -->
    <!-- Message to tell the user to press and hold on a shortcut to add it [CHAR_LIMIT=50] -->
    <string name="long_press_shortcut_to_add">Touch &amp; hold to move a shortcut.</string>
    <!-- Accessibility spoken hint message in deep shortcut menu, which allows user to add a shortcut. Custom action is the label for additional accessibility actions available in this mode [CHAR_LIMIT=200] -->
    <string name="long_accessible_way_to_add_shortcut">Double-tap &amp; hold to move a shortcut or use custom actions.</string>

    <skip />
    <!-- Error message when a user can't add more apps, widgets, or shortcuts to a Home screen. -->
    <string name="out_of_space">No room on this home screen</string>
    <!-- Error message when user has filled the hotseat -->
    <string name="hotseat_out_of_space">No more room in the Favorites tray</string>

    <!-- All applications label -->
    <string name="all_apps_button_label">Apps list</string>
    <string name="all_apps_search_results">Search results</string>
    <string name="all_apps_button_personal_label">Personal apps list</string>
    <string name="all_apps_button_work_label">Work apps list</string>

    <!-- Label for remove drop target (from the homescreen only).
         May appear next to uninstall_drop_target_label [CHAR_LIMIT=20] -->
    <string name="remove_drop_target_label">Remove</string>
    <!-- Label for uninstall drop target (from the device).
         May appear next to remove_drop_target_label [CHAR_LIMIT=20]-->
    <string name="uninstall_drop_target_label">Uninstall</string>
    <!-- Label for app info drop target. [CHAR_LIMIT=20] -->
    <string name="app_info_drop_target_label">App info</string>
    <!-- Label for install to private profile shortcut label. [CHAR_LIMIT=20] -->
    <string name="install_private_system_shortcut_label">Install in private</string>
    <!-- Label for uninstall app private profile shortcut.-->
    <string name="uninstall_private_system_shortcut_label">Uninstall app</string>
    <!-- Label for install drop target. [CHAR_LIMIT=20] -->
    <string name="install_drop_target_label">Install</string>
    <!-- Label for dismiss prediction. -->
    <string name="dismiss_prediction_label">Don\'t suggest app</string>
    <!-- Label for pinning predicted app. -->
    <string name="pin_prediction">Pin Prediction</string>


    <!-- Permissions: -->
    <skip />
    <!-- Permission short label -->
    <string name="permlab_install_shortcut">install shortcuts</string>
    <!-- Permission description -->
    <string name="permdesc_install_shortcut">Allows an app to add
        shortcuts without user intervention.</string>
    <!-- Permission short label -->
    <string name="permlab_read_settings">read home settings and shortcuts</string>
    <!-- Permission description -->
    <string name="permdesc_read_settings">Allows the app to read the settings and
        shortcuts in home.</string>
    <!-- Permission short label -->
    <string name="permlab_write_settings">write home settings and shortcuts</string>
    <!-- Permission description -->
    <string name="permdesc_write_settings">Allows the app to change the settings and
        shortcuts in home.</string>

    <!-- Widgets: -->
    <skip />

    <!-- Error text that lets a user know that the widget can't load. -->
    <string name="gadget_error_text">Can\'t load widget</string>

    <!-- Button text. This button lets a user change a widget's settings. -->
    <string name="gadget_setup_text">Widget settings</string>

    <!-- Instructional text to encourage a user to finish setting up the widget. -->
    <string name="gadget_complete_setup_text">Tap to finish setup</string>

    <!-- Text to inform the user that they can't uninstall a system application -->
    <string name="uninstall_system_app_text">This is a system app and can\'t be uninstalled.</string>

    <!-- Default folder title -->
    <string name="folder_hint_text">Edit Name</string>

    <!-- Accessibility -->
    <!-- The format string for when an app is temporarily disabled. -->
    <string name="disabled_app_label">Disabled <xliff:g id="app_name" example="Messenger">%1$s</xliff:g></string>
    <!-- The format string for when an app has a notification dot (meaning it has associated notifications). [ICU_FORMAT]-->
    <string name="dotted_app_label">
        {count, plural,
            =1      {{app_name} has # notification}
            other   {{app_name} has # notifications}
        }
    </string>
    <skip />

    <!-- The format string for default page scroll text [CHAR_LIMIT=none] -->
    <string name="default_scroll_format">Page %1$d of %2$d</string>
    <!-- The format string for Workspace page scroll text [CHAR_LIMIT=none] -->
    <string name="workspace_scroll_format">Home screen %1$d of %2$d</string>
    <!-- Description for a new page on homescreen[CHAR_LIMIT=none] -->
    <string name="workspace_new_page">New home screen page</string>

    <!-- Folder accessibility -->
    <!-- The format string for when a folder is opened, speaks the dimensions -->
    <string name="folder_opened">Folder opened, <xliff:g id="width" example="5">%1$d</xliff:g> by <xliff:g id="height" example="3">%2$d</xliff:g></string>
    <!-- Instruction that clicking outside will close folder -->
    <string name="folder_tap_to_close">Tap to close folder</string>
    <!-- Instruction that clicking outside will commit folder rename -->
    <string name="folder_tap_to_rename">Tap to save rename</string>
    <!-- Indication that folder closed -->
    <string name="folder_closed">Folder closed</string>
    <!-- Folder renamed format -->
    <string name="folder_renamed">Folder renamed to <xliff:g id="name" example="Games">%1$s</xliff:g></string>
    <!-- Folder name format when folder has less than 4 items -->
    <string name="folder_name_format_exact">Folder: <xliff:g id="name" example="Games">%1$s</xliff:g>, <xliff:g id="size" example="2">%2$d</xliff:g> items</string>
    <!-- Folder name format when folder has 4 or more items shown in preview-->
    <string name="folder_name_format_overflow">Folder: <xliff:g id="name" example="Games">%1$s</xliff:g>, <xliff:g id="size" example="2">%2$d</xliff:g> or more items</string>

    <!-- App pair accessibility -->
    <!-- App pair name -->
    <string name="app_pair_name_format">App pair: <xliff:g id="app1" example="Chrome">%1$s</xliff:g> and <xliff:g id="app2" example="YouTube">%2$s</xliff:g></string>

    <!-- Strings for the customization mode -->
    <!-- Text for wallpaper change button [CHAR LIMIT=30]-->
    <string name="styles_wallpaper_button_text">Wallpaper &amp; style</string>
    <!-- Text for edit home screen button [CHAR LIMIT=30]-->
    <string name="edit_home_screen">Edit Home Screen</string>
    <!-- Text for settings button [CHAR LIMIT=20]-->
    <string name="settings_button_text">Home settings</string>
    <!-- Message shown when a feature is disabled by the administrator -->
    <string name="msg_disabled_by_admin">Disabled by your admin</string>

    <!-- Strings for settings -->
    <!-- Title for Allow Rotation setting. [CHAR LIMIT=50] -->
    <string name="allow_rotation_title">Allow home screen rotation</string>
    <!-- Text explaining when the home screen will get rotated. [CHAR LIMIT=100] -->
    <string name="allow_rotation_desc">When phone is rotated</string>
    <!-- Title for Notification dots setting. Tapping this will link to the system Notifications settings screen where the user can turn off notification dots globally. [CHAR LIMIT=50] -->
    <string name="notification_dots_title">Notification dots</string>
    <!-- Text to indicate that the system notification dots setting is on [CHAR LIMIT=100] -->
    <string name="notification_dots_desc_on">On</string>
    <!-- Text to indicate that the system notification dots setting is off [CHAR LIMIT=100] -->
    <string name="notification_dots_desc_off">Off</string>
    <!-- Title for the dialog shown when the app does not has notification access, explaining the requirement for notification access [CHAR LIMIT=50] -->
    <string name="title_missing_notification_access">Notification access needed</string>
    <!-- Message explaining to the user that the notification access is required by the app for showing 'Notification dots' [CHAR LIMIT=NONE] -->
    <string name="msg_missing_notification_access">To show Notification Dots, turn on app notifications for <xliff:g id="name" example="My App">%1$s</xliff:g></string>
    <!-- Button text in the confirmation dialog which would take the user to the system settings [CHAR LIMIT=50] -->
    <string name="title_change_settings">Change settings</string>
    <!-- Summary for Notification dots setting. Tapping this will link enable/disable notification dots feature on the home screen. [CHAR LIMIT=50] -->
    <string name="notification_dots_service_title">Show notification dots</string>

    <!-- Title for Developer Options setting. [CHAR LIMIT=50] -->
    <string name="developer_options_title">Developer Options</string>

    <!-- Label for the setting that allows the automatic placement of launcher shortcuts for applications and games installed on the device [CHAR LIMIT=60] -->
    <string name="auto_add_shortcuts_label">Add app icons to home screen</string>
    <!-- Text description of the setting that allows the automatic placement of launcher shortcuts for applications and games installed on the device [CHAR LIMIT=NONE] -->
    <string name="auto_add_shortcuts_description">For new apps</string>

    <!-- Label on an icon that references an uninstalled package, for which we have no information about when it might be installed. [CHAR_LIMIT=15] -->
    <string name="package_state_unknown">Unknown</string>

    <!-- Button for abandoned promises dialog, to removes this abandoned promise icon. -->
    <string name="abandoned_clean_this">Remove</string>
    <!-- Button for abandoned promise dialog, to search in the market for the missing package. -->
    <string name="abandoned_search">Search</string>
    <!-- Title for abandoned promise dialog. -->
    <string name="abandoned_promises_title">This app is not installed</string>
    <!-- Explanation for abandoned promise dialog. "The first 'it' refers to the shortcut icon.
    The second "it" refers to the app. -->
    <string name="abandoned_promise_explanation">The app for this icon isn\'t installed.
        You can remove it, or search for the app and install it manually.
    </string>
    <!-- Title for an app which is being installed. -->
    <string name="app_installing_title"><xliff:g id="name" example="Messenger">%1$s</xliff:g> installing, <xliff:g id="progress" example="30%">%2$s</xliff:g> complete</string>
    <!-- Title for an app which is being downloaded. -->
    <string name="app_downloading_title"><xliff:g id="name" example="Messenger">%1$s</xliff:g> downloading, <xliff:g id="progress" example="30%">%2$s</xliff:g> complete</string>
    <!-- Title for an app whose download has been started. -->
    <string name="app_waiting_download_title"><xliff:g id="name" example="Messenger">%1$s</xliff:g> waiting to install</string>
    <!-- Title for an app which is archived. -->
    <string name="app_archived_title"><xliff:g id="name" example="Messenger">%1$s</xliff:g> is archived. Tap to download and restore.</string>


    <!-- Title shown on the alert dialog prompting the user to update the application in market
     in order to re-enable the disabled shortcuts -->
    <string name="dialog_update_title">App update required</string>
    <!-- Message shown on the alert dialog prompting the user to update the application -->
    <string name="dialog_update_message">The app for this icon isn\'t updated. You can update manually to re-enable this shortcut, or remove the icon.</string>
    <!-- Message for the update button in the alert dialog, will bring the user to market -->
    <string name="dialog_update">Update</string>
    <!-- Message for the remove button in the alert dialog -->
    <string name="dialog_remove">Remove</string>

    <!-- Strings for widgets & more in the popup container/bottom sheet -->

    <!-- Accessibility title for the popup containing a list of widgets. [CHAR_LIMIT=50] -->
    <string name="widgets_list">Widgets list</string>
    <!-- Text announced by accessibility when the popup containing the list of widgets is closed. [CHAR_LIMIT=100] -->
    <string name="widgets_list_closed">Widgets list closed</string>

    <!-- Strings for accessibility actions -->
    <!-- Accessibility action to add an app to workspace. [CHAR_LIMIT=30] -->
    <string name="action_add_to_workspace">Add to home screen</string>

    <!-- Accessibility action to move item to the current location. [CHAR_LIMIT=30] -->
    <string name="action_move_here">Move item here</string>

    <!-- Accessibility confirmation for item added to workspace. -->
    <string name="item_added_to_workspace">Item added to home screen</string>

    <!-- Accessibility confirmation for item removed. [CHAR_LIMIT=50]-->
    <string name="item_removed">Item removed</string>

    <!-- Action shown in snackbar to undo item removal. [CHAR_LIMIT=20] -->
    <string name="undo">Undo</string>

    <!-- Accessibility action to move an item on the workspace. [CHAR_LIMIT=30] -->
    <string name="action_move">Move item</string>

    <!-- Accessibility description to move item to empty cell. -->
    <string name="move_to_empty_cell_description">Move to row <xliff:g id="number" example="1">%1$s</xliff:g> column <xliff:g id="number" example="1">%2$s</xliff:g> in <xliff:g id="string" example="Home screen 2 of 4">%3$s</xliff:g></string>

    <!-- Accessibility description to move item inside a folder. -->
    <string name="move_to_position">Move to position <xliff:g id="number" example="1">%1$s</xliff:g></string>

    <!-- Accessibility description to move item to the hotseat. -->
    <string name="move_to_hotseat_position">Move to favorites position <xliff:g id="number" example="1">%1$s</xliff:g></string>

    <!-- Accessibility confirmation for item move. -->
    <string name="item_moved">Item moved</string>

    <!-- Accessibility description to move item into an existing folder. -->
    <string name="add_to_folder">Add to folder: <xliff:g id="name" example="Games">%1$s</xliff:g></string>

    <!-- Accessibility description to move item into an existing folder containing an app. -->
    <string name="add_to_folder_with_app">Add to folder with <xliff:g id="name" example="Messenger">%1$s</xliff:g></string>

    <!-- Accessibility confirmation for item added to folder. -->
    <string name="added_to_folder">Item added to folder</string>

    <!-- Accessibility description to create folder with another item. -->
    <string name="create_folder_with">Create folder with: <xliff:g id="name" example="Game">%1$s</xliff:g></string>

    <!-- Accessibility confirmation for folder created. -->
    <string name="folder_created">Folder created</string>

    <!-- Accessibility action to move an item from folder to workspace. [CHAR_LIMIT=30] -->
    <string name="action_move_to_workspace">Move to home screen</string>

    <!-- Accessibility action to resize a widget. [CHAR_LIMIT=30] -->
    <string name="action_resize">Resize</string>

    <!-- Accessibility action to increase width of a widget. [CHAR_LIMIT=30] -->
    <string name="action_increase_width">Increase width</string>

    <!-- Accessibility action to increase height of a widget. [CHAR_LIMIT=30] -->
    <string name="action_increase_height">Increase height</string>

    <!-- Accessibility action to decrease width of a widget. [CHAR_LIMIT=30] -->
    <string name="action_decrease_width">Decrease width</string>

    <!-- Accessibility action to decrease height of a widget. [CHAR_LIMIT=30] -->
    <string name="action_decrease_height">Decrease height</string>

    <!-- Accessibility confirmation for widget resize. -->
    <string name="widget_resized">Widget resized to width <xliff:g id="number" example="2">%1$s</xliff:g> height <xliff:g id="number" example="1">%2$s</xliff:g></string>

    <!-- Accessibility action to show quick actions menu for an icon. [CHAR_LIMIT=30] -->
    <string name="action_deep_shortcut">Shortcuts</string>

    <!-- Accessibility action to dismiss a notification in the shortcuts menu for an icon. [CHAR_LIMIT=30] -->
    <string name="action_dismiss_notification">Dismiss</string>

    <!-- Content description for arrow tip close button. [CHAR LIMIT=NONE] -->
    <string name="accessibility_close">Close</string>

    <!-- Label of tab to indicate personal apps -->
    <string name="all_apps_personal_tab">Personal</string>

    <!-- Label of tab to indicate work apps -->
    <string name="all_apps_work_tab">Work</string>

    <!-- This string is in the work profile tab when a user has All Apps open on their phone. This is a label for a toggle to turn the work profile on and off. "Work profile" means a separate profile on a user's phone that's specifically for their work apps and managed by their company. "Work" is used as an adjective.-->
    <string name="work_profile_toggle_label">Work profile</string>
    <!--- User onboarding title for work profile apps -->
    <string name="work_profile_edu_work_apps">Work apps are badged and visible to your IT admin</string>
    <!-- Action label to finish work profile edu-->
    <string name="work_profile_edu_accept">Got it</string>
    <!-- Info icon unicode for alpha scroller when work edu card is present -->
    <string name="work_profile_edu_section" translatable="false">\u24D8</string>

    <!--- heading shown when user opens work apps tab while work apps are paused -->
    <string name="work_apps_paused_title">Work apps are paused</string>
    <!--- body shown when user opens work apps tab while work apps are paused.-->
    <string name="work_apps_paused_info_body">You won’t receive notifications from your work apps</string>
    <!--- body shown when user opens work apps tab while work apps are paused. -->
    <string name="work_apps_paused_body">Your work apps can’t send you notifications, use your battery, or access your location</string>
    <!--- body shown when user opens work apps tab while work apps are paused and calls and texts are unavailable-->
    <string name="work_apps_paused_telephony_unavailable_body">You won’t receive phone calls, text messages, or notifications from your work apps</string>
    <!-- string shown in educational banner about work profile -->
    <string name="work_apps_paused_edu_banner">Work apps are badged and visible to your IT admin</string>
    <!-- button string shown to dismiss work tab education -->
    <string name="work_apps_paused_edu_accept">Got it</string>

    <!-- button string shown pause work profile [CHAR LIMIT=28] -->
    <string name="work_apps_pause_btn_text">Pause work apps</string>
    <!-- button string shown enable work profile -->
    <string name="work_apps_enable_btn_text">Unpause</string>

    <!-- A hint shown in launcher settings develop options filter box -->
    <string name="developer_options_filter_hint">Filter</string>

    <!-- Failed action error message: e.g. Failed: Pause -->
    <string name="remote_action_failed">Failed: <xliff:g id="what" example="Pause">%1$s</xliff:g></string>

    <!-- Strings for Private Space -->
    <!-- Private space tile label -->
    <string name="private_space_label">Private space</string>
    <!-- Private space tile secondary label -->
    <string name="private_space_secondary_label">Tap to set up or open</string>
    <!-- Title for Private Space Container shown at the bottom of all apps drawer -->
    <string name="ps_container_title">Private</string>
    <!-- Description for Private Space Settings button -->
    <string name="ps_container_settings">Private Space Settings</string>
    <!-- Description for Private Space Unlock button -->
    <string name="ps_container_unlock_button_content_description">Private, unlocked.</string>
    <!-- Description for Private Space Lock button -->
    <string name="ps_container_lock_button_content_description">Private, locked.</string>
    <string name="ps_container_lock_title">Lock</string>
    <!-- Description for Private Space Transition button -->
    <string name="ps_container_transition">Private Space Transitioning</string>
    <!-- Title for Private Space install app icon -->
    <string name="ps_add_button_label">Install</string>
    <!-- Content description for install app icon -->
    <string name="ps_add_button_content_description">Install apps to Private Space</string>
    <!--[MMI][DAHLIA-595] modify battery temperature warning huanglj 20241202 begin-->
    <string name="battery_warning_title">battery temperature warning</string>
    <string name="battery_temperature_warning">battery temperature is too high,Please use it after it has cooled down.</string>
    <!--[MMI][DAHLIA-595] modify battery temperature warning huanglj 20241202 end-->
</resources>
