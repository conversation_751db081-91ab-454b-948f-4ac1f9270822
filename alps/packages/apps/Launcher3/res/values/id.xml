<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (C) 2020 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<resources>
    <item type="id" name="apps_list_view_work" />
    <item type="id" name="view_type_widgets_space" />
    <item type="id" name="view_type_widgets_list" />
    <item type="id" name="view_type_widgets_header" />

    <!-- Accessibility actions -->
    <item type="id" name="action_remove" />
    <item type="id" name="action_uninstall" />
    <item type="id" name="action_reconfigure" />
    <item type="id" name="action_add_to_workspace" />
    <item type="id" name="action_move" />
    <item type="id" name="action_move_to_workspace" />
    <item type="id" name="action_move_screen_backwards" />
    <item type="id" name="action_move_screen_forwards" />
    <item type="id" name="action_resize" />
    <item type="id" name="action_deep_shortcuts" />
    <item type="id" name="action_remote_action_shortcut" />
    <item type="id" name="action_dismiss_prediction" />
    <item type="id" name="action_pin_prediction"/>
    <item type="id" name="action_close"/>
    <!--  Used for A11y actions in staged split to identify each task uniquely  -->
    <item type="id" name="action_app_info_top_left" />
    <item type="id" name="action_app_info_bottom_right" />
    <item type="id" name="action_digital_wellbeing_top_left" />
    <item type="id" name="action_digital_wellbeing_bottom_right" />

    <!-- QSB IDs. DO not change -->
    <item type="id" name="search_container_workspace" />
    <item type="id" name="search_container_all_apps" />
    <item type="id" name="search_container_hotseat" />

    <!-- View ID to use for QSB widget -->
    <item type="id" name="qsb_widget" />

    <!-- View ID used by cell layout to jail its content -->
    <item type="id" name="cell_layout_jail_id" />

    <!-- View IDs to store item highlight information -->
    <item type="id" name="view_unhighlight_background" />

    <!-- view ID used to restore work tab state -->
    <item type="id" name="work_tab_state_id" />

    <!-- Menu id for feature flags -->
    <item type="id" name="menu_apply_flags" />

    <!--  Do not change, must be kept in sync with sysui navbar button IDs for tests!  -->
    <item type="id" name="home" />
    <item type="id" name="recent_apps" />
    <item type="id" name="back" />
    <item type="id" name="close"/>
    <item type="id" name="ime_switcher" />
    <item type="id" name="accessibility_button" />
    <item type="id" name="rotate_suggestion" />
    <item type="id" name="space" />
    <!--  /Do not change, must be kept in sync with sysui navbar button IDs for tests!  -->

    <item type="id" name="quick_settings_button" />
    <item type="id" name="notifications_button" />
    <item type="id" name="cache_entry_tag_id" />

    <item type="id" name="saved_clip_children_tag_id" />
    <item type="id" name="saved_clip_to_padding_tag_id" />

    <item type="id" name="saved_floating_widget_foreground" />
    <item type="id" name="saved_floating_widget_background" />

    <item type="id" name="dismiss_view" />

    <!-- Private Space parameters -->
    <item type="id" name="ps_container_header" />
    <item type="id" name="ps_lock_unlock_button" />
    <item type="id" name="ps_settings_button" />
    <item type="id" name="ps_transition_image" />

    <!-- Recents -->
    <item type="id" name="overview_panel"/>

    <!-- DragController -->
    <item type="id" name="drag_event_parity" />

    <!-- View tag key used to store SpringAnimation data. -->
    <item type="id" name="spring_animation_tag" />

</resources>
