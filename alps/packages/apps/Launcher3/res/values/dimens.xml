<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>

    <dimen name="click_shadow_elevation">4dp</dimen>

    <!-- Dynamic Grid -->
    <dimen name="dynamic_grid_edge_margin">10.77dp</dimen>
    <dimen name="dynamic_grid_left_right_margin">8dp</dimen>
    <!-- Minimum amount of next page visible in spring loaded mode -->
    <dimen name="dynamic_grid_spring_loaded_min_next_space_visible">48dp</dimen>

    <dimen name="dynamic_grid_cell_border_spacing">16dp</dimen>
    <dimen name="cell_layout_padding">10.77dp</dimen>
    <dimen name="dynamic_grid_cell_padding_x">8dp</dimen>

    <!-- Hotseat -->
    <dimen name="dynamic_grid_hotseat_bottom_tall_padding">0dp</dimen>
    <dimen name="spring_loaded_hotseat_top_margin">76dp</dimen>
    <dimen name="spring_loaded_hotseat_top_margin_wide_portrait">64dp</dimen>

    <dimen name="dynamic_grid_hotseat_side_padding">0dp</dimen>

    <!-- Scalable Grid -->
    <dimen name="min_qsb_margin">8dp</dimen>

    <!-- Workspace page indicator -->
    <!-- DAHLIA-4648 [AVCM] Wallpapers claro is not obscured  donglinxin begin-->
    <!--DAHMAX-807 xuxiaomeng modify for [SR][BUG][DahliaMax][T451A1][FC][Requirement][LATAM][Launcher]The Claro logo on the home screen under AVDO is blocked, which does not meet the requirements 2025.7.5 begin-->
    <dimen name="workspace_page_indicator_height">50dp</dimen>
    <!--DAHMAX-807 xuxiaomeng modify for [SR][BUG][DahliaMax][T451A1][FC][Requirement][LATAM][Launcher]The Claro logo on the home screen under AVDO is blocked, which does not meet the requirements 2025.7.5 end-->
    <!-- DAHLIA-4648 [AVCM] Wallpapers claro is not obscured donglinxin end-->
    <dimen name="workspace_page_indicator_line_height">1dp</dimen>
    <dimen name="workspace_page_indicator_overlap_workspace">0dp</dimen>

    <!-- Drop target bar -->
    <dimen name="dynamic_grid_drop_target_size">56dp</dimen>
    <dimen name="drop_target_vertical_gap">20dp</dimen>
    <dimen name="drop_target_top_margin">32dp</dimen>
    <dimen name="drop_target_bottom_margin">16dp</dimen>
    <dimen name="drop_target_bottom_margin_wide_portrait">32dp</dimen>

    <!-- App Widget resize frame -->
    <!-- Button drop target bar -->
    <dimen name="button_drop_target_min_text_size">10sp</dimen>
    <dimen name="button_drop_target_resize_text_increment">1sp</dimen>

    <!-- App Widget resize frame -->
    <dimen name="widget_handle_margin">13dp</dimen>
    <dimen name="resize_frame_background_padding">24dp</dimen>
    <dimen name="resize_frame_margin">23dp</dimen>
    <dimen name="resize_frame_invalid_drag_across_two_panel_opacity_margin">24dp</dimen>

    <!-- App widget reconfigure button -->
    <dimen name="widget_reconfigure_button_corner_radius">14dp</dimen>
    <dimen name="widget_reconfigure_button_padding">6dp</dimen>
    <dimen name="widget_reconfigure_button_margin">32dp</dimen>
    <dimen name="widget_reconfigure_button_size">36dp</dimen>
    <dimen name="widget_reconfigure_tip_top_margin">16dp</dimen>

    <!-- Fast scroll -->
    <dimen name="fastscroll_track_min_width">6dp</dimen>
    <dimen name="fastscroll_track_max_width">8dp</dimen>
    <dimen name="fastscroll_thumb_padding">1dp</dimen>
    <dimen name="fastscroll_thumb_height">52dp</dimen>
    <dimen name="fastscroll_thumb_touch_inset">-24dp</dimen>

    <dimen name="fastscroll_popup_width">75dp</dimen>
    <dimen name="fastscroll_popup_height">62dp</dimen>
    <dimen name="fastscroll_popup_padding">13dp</dimen>
    <dimen name="fastscroll_popup_text_size">32dp</dimen>
    <dimen name="fastscroll_popup_margin">19dp</dimen>

    <!--
      Fast scroller draws the content horizontally centered. The end of the track should be
      aligned at the end of the container.
        fastscroll_end_margin = - (fastscroll_width - fastscroll_track_min_width) / 2
    -->
    <dimen name="fastscroll_width">58dp</dimen>
    <dimen name="fastscroll_end_margin">-26dp</dimen>
    <!-- Extra margin between bottom of the scrollbar and the search bar protection layer. -->
    <dimen name="fastscroll_bottom_margin_floating_search">4dp</dimen>

    <!-- PagedView -->
    <dimen name="fling_threshold_velocity">500dp</dimen>
    <dimen name="easy_fling_threshold_velocity">400dp</dimen>
    <dimen name="min_fling_velocity">250dp</dimen>
    <!-- The minimum velocity of a page snap after a fling gesture -->
    <dimen name="min_page_snap_velocity">1500dp</dimen>

    <!-- All Apps -->
    <dimen name="all_apps_starting_vertical_translate">300dp</dimen>
    <dimen name="all_apps_search_bar_field_height">48dp</dimen>
    <!-- all_apps_search_bar_field_height / 2 -->
    <dimen name="all_apps_search_bar_content_overlap">24dp</dimen>
    <dimen name="all_apps_search_bar_bottom_padding">30dp</dimen>
    <dimen name="all_apps_empty_search_message_top_offset">40dp</dimen>
    <dimen name="all_apps_search_top_row_extra_height">4dp</dimen>
    <dimen name="all_apps_header_pill_height">48dp</dimen>
    <dimen name="all_apps_header_pill_corner_radius">12dp</dimen>
    <dimen name="all_apps_header_tab_height">48dp</dimen>
    <dimen name="all_apps_tabs_indicator_height">2dp</dimen>
    <dimen name="all_apps_header_top_margin">33dp</dimen>
    <dimen name="all_apps_header_top_padding">36dp</dimen>
    <!-- Additional top padding to add when Floating Searchbar is enabled. -->
    <dimen name="all_apps_additional_top_padding_floating_search">16dp</dimen>
    <dimen name="all_apps_header_bottom_padding">14dp</dimen>
    <dimen name="all_apps_header_top_adjustment">6dp</dimen>
    <dimen name="all_apps_header_bottom_adjustment">4dp</dimen>
    <dimen name="all_apps_work_profile_tab_footer_top_padding">16dp</dimen>
    <dimen name="all_apps_work_profile_tab_footer_bottom_padding">20dp</dimen>
    <dimen name="all_apps_tabs_button_horizontal_padding">4dp</dimen>
    <dimen name="all_apps_tabs_vertical_padding">6dp</dimen>
    <dimen name="all_apps_tabs_margin_top">8dp</dimen>
    <dimen name="all_apps_divider_height">2dp</dimen>
    <dimen name="all_apps_divider_width">128dp</dimen>
    <dimen name="all_apps_content_fade_in_offset">150dp</dimen>
    <dimen name="all_apps_tip_bottom_margin">8dp</dimen>
    <dimen name="all_apps_height_extra">6dp</dimen>
    <dimen name="all_apps_paged_view_top_padding">40dp</dimen>
    <dimen name="all_apps_recycler_view_decorator_padding">1dp</dimen>
    <dimen name="all_apps_recycler_view_decorator_group_radius">28dp</dimen>
    <dimen name="all_apps_recycler_view_decorator_result_radius">4dp</dimen>

    <dimen name="all_apps_icon_drawable_padding">8dp</dimen>
    <dimen name="all_apps_predicted_icon_vertical_padding">8dp</dimen>
    <!-- The size of corner radius of the arrow in the arrow toast. -->
    <dimen name="arrow_toast_corner_radius">2dp</dimen>
    <dimen name="arrow_toast_elevation">2dp</dimen>
    <dimen name="arrow_toast_arrow_width">10dp</dimen>
    <dimen name="arrow_toast_arrow_height">10dp</dimen>
    <dimen name="arrow_toast_text_size">14sp</dimen>

    <dimen name="all_apps_divider_margin_vertical">8dp</dimen>

    <!-- Floating action button inside work tab to toggle work profile -->
    <dimen name="work_fab_height">56dp</dimen>
    <dimen name="work_fab_radius">16dp</dimen>
    <dimen name="work_fab_icon_size">24dp</dimen>
    <dimen name="work_fab_icon_end_margin">12dp</dimen>
    <dimen name="work_fab_text_end_margin">16dp</dimen>
    <dimen name="work_card_padding_horizontal">10dp</dimen>
    <dimen name="work_fab_width">214dp</dimen>
    <dimen name="work_card_button_height">52dp</dimen>
    <dimen name="work_fab_margin">16dp</dimen>
    <dimen name="work_fab_margin_bottom">20dp</dimen>
    <dimen name="work_mode_fab_background_start_padding">16dp</dimen>
    <dimen name="work_mode_fab_background_end_padding">4dp</dimen>
    <dimen name="work_profile_footer_padding">20dp</dimen>
    <dimen name="work_edu_card_margin">16dp</dimen>
    <dimen name="work_edu_card_radius">16dp</dimen>
    <dimen name="work_edu_card_bottom_margin">26dp</dimen>
    <dimen name="work_edu_card_text_end_margin">32dp</dimen>
    <dimen name="work_apps_paused_button_stroke">1dp</dimen>

    <dimen name="work_card_margin">24dp</dimen>
    <!-- (x) icon button inside work edu card -->
    <dimen name="rounded_button_width">24dp</dimen>
    <dimen name="x_icon_size">16dp</dimen>

    <!-- rounded button shown inside card views, and snack bars  -->
    <dimen name="padded_rounded_button_height">48dp</dimen>
    <dimen name="rounded_button_height">48dp</dimen>
    <dimen name="rounded_button_radius">200dp</dimen>
    <dimen name="rounded_button_padding">8dp</dimen>


    <!-- Widget tray -->
    <dimen name="widget_cell_vertical_padding">8dp</dimen>
    <dimen name="widget_cell_horizontal_padding">8dp</dimen>
    <dimen name="widget_cell_title_font_size">14sp</dimen>
    <integer name="widget_cell_title_font_weight">500</integer>
    <dimen name="widget_cell_title_line_height">20sp</dimen>
    <dimen name="widget_cell_dims_font_size">14sp</dimen>
    <integer name="widget_cell_dims_font_weight">400</integer>
    <dimen name="widget_cell_dims_line_height">20sp</dimen>
    <dimen name="widget_cell_description_font_size">12sp</dimen>
    <integer name="widget_cell_description_font_weight">400</integer>
    <dimen name="widget_cell_description_line_height">16sp</dimen>
    <dimen name="widget_cell_app_icon_size">24dp</dimen>
    <dimen name="widget_cell_app_icon_padding">8dp</dimen>
    <dimen name="widget_cell_add_button_height">48dp</dimen>
    <dimen name="widget_cell_add_button_start_padding">8dp</dimen>
    <dimen name="widget_cell_add_icon_button_start_padding">16dp</dimen>
    <dimen name="widget_cell_add_button_end_padding">16dp</dimen>
    <dimen name="widget_cell_add_button_scroll_padding">24dp</dimen>
    <dimen name="widget_cell_add_button_font_size">14sp</dimen>
    <integer name="widget_cell_add_button_font_weight">500</integer>
    <dimen name="widget_cell_add_button_line_height">20sp</dimen>
    <dimen name="widget_cell_add_button_drawable_padding">8dp</dimen>
    <dimen name="widget_cell_add_button_drawable_width">19dp</dimen>

    <dimen name="widget_tabs_button_horizontal_padding">4dp</dimen>
    <dimen name="widget_tabs_horizontal_padding">16dp</dimen>
    <dimen name="widget_apps_tabs_vertical_padding">6dp</dimen>
    <dimen name="widget_picker_landscape_tablet_left_right_margin">117dp</dimen>
    <dimen name="widget_picker_two_panels_left_right_margin">0dp</dimen>
    <dimen name="widget_picker_header_app_title_font_size">16sp</dimen>
    <integer name="widget_picker_header_app_title_font_weight">500</integer>
    <dimen name="widget_picker_header_app_title_line_height">24sp</dimen>
    <dimen name="widget_picker_header_app_subtitle_font_size">14sp</dimen>
    <integer name="widget_picker_header_app_subtitle_font_weight">400</integer>
    <dimen name="widget_picker_header_app_subtitle_line_height">20sp</dimen>
    <dimen name="widget_recommendations_table_vertical_padding">8dp</dimen>
    <!-- Bottom margin for the search and recommended widgets container without work profile -->
    <dimen name="search_and_recommended_widgets_container_bottom_margin">16dp</dimen>
    <!-- Bottom margin for the search and recommended widgets container with work profile -->
    <dimen name="search_and_recommended_widgets_container_small_bottom_margin">10dp</dimen>

    <dimen name="widget_list_top_bottom_corner_radius">28dp</dimen>
    <dimen name="widget_list_content_corner_radius">4dp</dimen>

    <dimen name="widget_list_header_view_vertical_padding">20dp</dimen>
    <dimen name="widget_list_entry_spacing">2dp</dimen>
    <!-- Less margin on sides to let widgets table width be close to the workspace width. -->
    <dimen name="widget_list_horizontal_margin">11dp</dimen>
    <!-- Margin applied to the recycler view with search bar & the list of widget apps below it. -->
    <dimen name="widget_list_left_pane_horizontal_margin">0dp</dimen>
    <dimen name="widget_list_horizontal_margin_two_pane">24dp</dimen>
    <dimen name="widget_picker_vertical_margin_right_pane">24dp</dimen>
    <!-- Margin on sides of the widgets scrollview in widgets bottom sheet -->
    <dimen name="widget_bottom_sheet_horizontal_margin">16dp</dimen>

    <dimen name="widget_preview_shadow_blur">0.5dp</dimen>
    <dimen name="widget_preview_key_shadow_distance">1dp</dimen>
    <dimen name="widget_preview_corner_radius">2dp</dimen>
    <dimen name="widget_preview_cell_divider_width">0.5dp</dimen>
    <dimen name="widget_preview_shortcut_padding">8dp</dimen>

    <dimen name="widget_section_height">56dp</dimen>
    <dimen name="widget_section_icon_size">40dp</dimen>
    <dimen name="widget_section_vertical_padding">8dp</dimen>
    <dimen name="widget_section_horizontal_padding">16dp</dimen>

    <dimen name="widget_row_padding">8dp</dimen>
    <dimen name="widget_row_divider">2dp</dimen>

    <dimen name="widget_picker_education_tip_max_width">308dp</dimen>
    <dimen name="widget_picker_education_tip_min_margin">4dp</dimen>

    <!-- Padding applied to shortcut previews -->
    <dimen name="shortcut_preview_padding_left">0dp</dimen>
    <dimen name="shortcut_preview_padding_right">0dp</dimen>
    <dimen name="shortcut_preview_padding_top">0dp</dimen>

    <!-- Pin widget dialog -->
    <dimen name="pin_widget_button_padding_horizontal">8dp</dimen>
    <dimen name="pin_widget_button_padding_vertical">4dp</dimen>
    <dimen name="pin_widget_button_inset_horizontal">4dp</dimen>
    <dimen name="pin_widget_button_inset_vertical">6dp</dimen>

    <!-- Dragging -->
    <!-- Drag padding to add to the bottom of drop targets -->
    <dimen name="drop_target_drag_padding">14dp</dimen>
    <dimen name="drop_target_text_size">16sp</dimen>
    <dimen name="drop_target_shadow_elevation">2dp</dimen>
    <dimen name="drop_target_bar_margin_horizontal">4dp</dimen>
    <dimen name="drop_target_button_drawable_size">20dp</dimen>
    <dimen name="drop_target_button_drawable_padding">8dp</dimen>
    <dimen name="drop_target_button_drawable_horizontal_padding">16dp</dimen>
    <dimen name="drop_target_button_drawable_vertical_padding">8dp</dimen>
    <dimen name="drop_target_button_frame_radius">80dp</dimen>
    <dimen name="drop_target_button_gap">28dp</dimen>
    <dimen name="drop_target_button_workspace_edge_gap">0dp</dimen>

    <!-- the distance an icon must be dragged before button drop targets accept it -->
    <dimen name="drag_distanceThreshold">30dp</dimen>

    <!-- Elevation for the drag view. It should be larger than elevation of all other drag sources
         and drop targets like all-apps and folders -->
    <dimen name="drag_elevation">30dp</dimen>

    <dimen name="drag_flingToDeleteMinVelocity">-1500dp</dimen>

    <dimen name="spring_loaded_panel_border">2dp</dimen>
    <dimen name="keyboard_drag_stroke_width">4dp</dimen>

    <!-- Folders -->
    <dimen name="page_indicator_dot_size">6dp</dimen>
    <dimen name="page_indicator_size">10dp</dimen>


    <dimen name="folder_cell_x_padding">9dp</dimen>
    <dimen name="folder_cell_y_padding">6dp</dimen>
    <!-- label text size = workspace text size multiplied by this scale -->
    <dimen name="folder_label_text_scale">1.14</dimen>
    <dimen name="folder_footer_height_default">56dp</dimen>

    <dimen name="folder_content_padding_left_right">8dp</dimen>
    <dimen name="folder_content_padding_top">16dp</dimen>

    <!-- Sizes for managed profile badges -->
    <dimen name="profile_badge_size">24dp</dimen>
    <dimen name="profile_badge_minimum_top">2dp</dimen>

    <!-- Shadows and outlines -->
    <dimen name="blur_size_thin_outline">1dp</dimen>
    <dimen name="blur_size_medium_outline">2dp</dimen>
    <dimen name="blur_size_click_shadow">4dp</dimen>
    <dimen name="click_shadow_high_shift">2dp</dimen>

    <!-- Pending widget -->
    <dimen name="pending_widget_min_padding">8dp</dimen>
    <dimen name="pending_widget_elevation">2dp</dimen>

    <!-- Deep shortcuts -->
    <dimen name="deep_shortcuts_elevation">2dp</dimen>
    <dimen name="bg_popup_padding">2dp</dimen>
    <dimen name="bg_popup_item_width">216dp</dimen>
    <dimen name="bg_popup_item_height">52dp</dimen>
    <dimen name="bg_popup_item_vertical_padding">12dp</dimen>
    <dimen name="pre_drag_view_scale">6dp</dimen>
    <!-- Minimum size of the widget dragged view to keep it visible under the finger. -->
    <dimen name="widget_drag_view_min_scale_down_size">70dp</dimen>
    <!-- an icon with shortcuts must be dragged this far before the container is removed. -->
    <dimen name="deep_shortcuts_start_drag_threshold">16dp</dimen>
    <!-- Possibly related to b/235886078, icon needs to be scaled up to match expected visual size of 32 dp -->
    <dimen name="deep_shortcut_icon_size">35dp</dimen>
    <dimen name="popup_margin">2dp</dimen>
    <dimen name="popup_single_item_radius">100dp</dimen>
    <dimen name="popup_smaller_radius">4dp</dimen>
    <dimen name="deep_shortcut_drawable_padding">16dp</dimen>
    <dimen name="popup_padding_start">10dp</dimen>
    <dimen name="popup_padding_end">14dp</dimen>
    <dimen name="popup_vertical_padding">4dp</dimen>
    <dimen name="popup_arrow_width">12dp</dimen>
    <dimen name="popup_arrow_height">10dp</dimen>
    <dimen name="popup_arrow_vertical_offset">-1dp</dimen>
    <!-- popup_padding_start + deep_shortcut_icon_size / 2 -->
    <dimen name="popup_arrow_horizontal_center_offset">26dp</dimen>
    <dimen name="popup_arrow_corner_radius">2dp</dimen>
    <!-- popup_padding_start + deep_shortcut_icon_size + 12dp -->
    <dimen name="deep_shortcuts_text_padding_start">54dp</dimen>
    <dimen name="system_shortcut_icon_size">20dp</dimen>
    <!-- popup_arrow_horizontal_center_offset - system_shortcut_icon_size / 2 -->
    <dimen name="system_shortcut_margin_start">16dp</dimen>
    <dimen name="system_shortcut_header_height">52dp</dimen>
    <dimen name="system_shortcut_header_icon_touch_size">48dp</dimen>
    <!-- (system_shortcut_header_icon_touch_size - system_shortcut_icon_size) / 2 -->
    <dimen name="system_shortcut_header_icon_padding">14dp</dimen>
    <!-- side of start/end icon near to container edge -->
    <dimen name="system_shortcut_header_icon_padding_outer">16dp</dimen>
    <!-- side of start/end icon far from container edge -->
    <dimen name="system_shortcut_header_icon_padding_inner">12dp</dimen>


    <!-- Notifications -->
    <dimen name="bg_round_rect_radius">8dp</dimen>
    <dimen name="notification_container_height">104dp</dimen>
    <dimen name="notification_max_trans">8dp</dimen>
    <dimen name="notification_space">8dp</dimen>
    <dimen name="notification_padding_end">16dp</dimen>
    <dimen name="notification_padding_bottom">12dp</dimen>
    <dimen name="notification_padding_top">12dp</dimen>
    <dimen name="notification_padding_header_top">16dp</dimen>
    <dimen name="notification_header_padding_start">14dp</dimen>
    <dimen name="notification_header_text_size">14sp</dimen>
    <dimen name="notification_header_count_text_size">12sp</dimen>
    <dimen name="notification_main_title_size">14sp</dimen>
    <dimen name="notification_main_text_size">14sp</dimen>
    <dimen name="notification_circle_icon_size">24dp</dimen>
    <dimen name="notification_icon_size">32dp</dimen>
    <dimen name="notification_icon_padding_start">10dp</dimen>
    <!-- notification_icon_padding + notification_icon_size + notification_icon_padding -->
    <dimen name="notification_main_text_padding_start">54dp</dimen>
    <dimen name="horizontal_ellipsis_size">18dp</dimen>

    <!-- Overview -->
    <dimen name="options_menu_icon_size">24dp</dimen>
    <dimen name="options_menu_thumb_size">32dp</dimen>

    <!-- Snackbar -->
    <dimen name="snackbar_height">48dp</dimen>
    <dimen name="snackbar_content_height">48dp</dimen>
    <dimen name="snackbar_padding">8dp</dimen>
    <dimen name="snackbar_min_margin_left_right">6dp</dimen>
    <dimen name="snackbar_max_margin_left_right">72dp</dimen>
    <dimen name="snackbar_margin_bottom">30dp</dimen>
    <dimen name="snackbar_elevation">3dp</dimen>
    <dimen name="snackbar_min_text_size">12sp</dimen>
    <dimen name="snackbar_max_text_size">14sp</dimen>
    <dimen name="snackbar_max_width">504dp</dimen>

    <!-- Developer Options -->
    <dimen name="developer_options_filter_margins">10dp</dimen>

    <!-- Theming related -->
    <dimen name="default_dialog_corner_radius">26dp</dimen>
    <dimen name="dialogCornerRadius">@dimen/default_dialog_corner_radius</dimen>

    <!-- Onboarding bottomsheet related -->
    <dimen name="bottom_sheet_edu_padding">24dp</dimen>

    <!-- Taskbar related (placeholders to compile in Launcher3 without Quickstep) -->
    <dimen name="taskbar_size">0dp</dimen>
    <dimen name="taskbar_phone_size">@*android:dimen/navigation_bar_frame_height</dimen>
    <dimen name="taskbar_phone_home_button_size">80dp</dimen>
    <dimen name="taskbar_phone_content_padding">8dp</dimen>
    <dimen name="taskbar_phone_rounded_corner_content_margin">
        @*android:dimen/rounded_corner_content_padding
    </dimen>
    <dimen name="taskbar_stashed_size">0dp</dimen>
    <dimen name="qsb_widget_height">0dp</dimen>
    <dimen name="qsb_shadow_height">0dp</dimen>
    <dimen name="min_hotseat_icon_space">18dp</dimen>
    <dimen name="max_hotseat_icon_space">50dp</dimen>
    <dimen name="min_hotseat_qsb_width">0dp</dimen>
    <dimen name="taskbar_running_app_indicator_height">0dp</dimen>
    <dimen name="taskbar_running_app_indicator_width">0dp</dimen>
    <dimen name="taskbar_running_app_indicator_top_margin">0dp</dimen>
    <dimen name="taskbar_minimized_app_indicator_height">0dp</dimen>
    <dimen name="taskbar_minimized_app_indicator_width">0dp</dimen>
    <dimen name="taskbar_minimized_app_indicator_top_margin">0dp</dimen>

    <!-- Transient taskbar (placeholders to compile in Launcher3 without Quickstep) -->
    <dimen name="transient_taskbar_padding">0dp</dimen>
    <dimen name="transient_taskbar_bottom_margin">0dp</dimen>
    <dimen name="transient_taskbar_shadow_blur">0dp</dimen>
    <dimen name="transient_taskbar_key_shadow_distance">0dp</dimen>
    <dimen name="transient_taskbar_stashed_height">0dp</dimen>
    <dimen name="transient_taskbar_clamped_offset_bound">0dp</dimen>
    <dimen name="taskbar_icon_spacing">0dp</dimen>
    <dimen name="taskbar_nav_buttons_size">0dp</dimen>
    <dimen name="taskbar_contextual_button_suw_margin">0dp</dimen>
    <dimen name="taskbar_hotseat_nav_spacing">0dp</dimen>
    <dimen name="taskbar_button_margin_default">0dp</dimen>
    <dimen name="taskbar_button_space_inbetween">0dp</dimen>
    <dimen name="taskbar_button_space_inbetween_phone">0dp</dimen>
    <dimen name="taskbar_button_margin_split">0dp</dimen>
    <dimen name="taskbar_button_margin_6_5">0dp</dimen>

    <!--- Floating Ime Inset height-->
    <dimen name="floating_ime_inset_height">0dp</dimen>

    <!-- Bubble bar (placeholders to compile in Launcher3 without Quickstep) -->
    <dimen name="bubblebar_hotseat_adjustment_threshold">0dp</dimen>

    <!-- Size of the maximum radius for the enforced rounded rectangles. -->
    <dimen name="enforced_rounded_corner_max_radius">16dp</dimen>

    <!-- Size of the radius for the rounded corners of Persistent Taskbar. -->
    <dimen name="persistent_taskbar_corner_radius">16dp</dimen>

    <!-- Base Swipe Detector, speed in dp/s -->
    <dimen name="base_swift_detector_fling_release_velocity">1dp</dimen>

    <!-- Overview placeholder to compile in Launcher3 without Quickstep -->
    <dimen name="task_thumbnail_icon_size">0dp</dimen>
    <dimen name="task_thumbnail_icon_drawable_size">0dp</dimen>
    <dimen name="task_thumbnail_icon_drawable_size_grid">0dp</dimen>
    <dimen name="task_thumbnail_icon_menu_drawable_touch_size">0dp</dimen>
    <dimen name="task_menu_edge_padding">0dp</dimen>
    <dimen name="overview_task_margin">0dp</dimen>
    <dimen name="overview_actions_height">0dp</dimen>
    <dimen name="overview_actions_button_spacing">0dp</dimen>
    <dimen name="overview_actions_margin_gesture">0dp</dimen>
    <dimen name="overview_actions_top_margin">0dp</dimen>
    <dimen name="overview_grid_side_margin">0dp</dimen>
    <dimen name="overview_grid_row_spacing">0dp</dimen>
    <dimen name="overview_page_spacing">0dp</dimen>
    <dimen name="overview_top_margin_grid_only">0dp</dimen>
    <dimen name="overview_bottom_margin_grid_only">0dp</dimen>

    <dimen name="split_placeholder_size">72dp</dimen>
    <dimen name="split_placeholder_inset">16dp</dimen>
    <dimen name="split_placeholder_icon_size">44dp</dimen>
    <dimen name="task_menu_width_grid">216dp</dimen>
    <dimen name="split_instructions_radius">22dp</dimen>
    <dimen name="split_instructions_elevation">1dp</dimen>
    <dimen name="split_instructions_horizontal_padding">24dp</dimen>
    <dimen name="split_instructions_vertical_padding">12dp</dimen>
    <dimen name="split_instructions_drawable_padding">10dp</dimen>
    <dimen name="split_instructions_bottom_margin_phone_landscape">24dp</dimen>
    <dimen name="split_instructions_bottom_margin_phone_portrait">60dp</dimen>
    <dimen name="split_instructions_start_margin_cancel">8dp</dimen>
    <dimen name="split_divider_handle_region_width">96dp</dimen>
    <dimen name="split_divider_handle_region_height">48dp</dimen>

    <dimen name="focus_outline_radius">16dp</dimen>
    <dimen name="focus_inner_outline_radius">14dp</dimen>
    <dimen name="focus_outline_stroke_width">2dp</dimen>
    <!-- -4dp for double stroke focus outlines, -2dp for padding between outline and widget,
    make it negative to outset the rect and leave space for drawing focus outline and padding -->
    <dimen name="focus_rect_widget_outsets">-6dp</dimen>

    <!-- Workspace grid visualization parameters -->
    <dimen name="grid_visualization_rounding_radius">16dp</dimen>
    <dimen name="grid_visualization_horizontal_cell_spacing">6dp</dimen>
    <dimen name="grid_visualization_vertical_cell_spacing">6dp</dimen>

    <!-- Search results related parameters -->
    <dimen name="search_row_icon_size">48dp</dimen>
    <dimen name="search_row_small_icon_size">32dp</dimen>
    <dimen name="padded_rounded_button_padding">8dp</dimen>

    <!-- Bottom sheet related parameters -->
    <dimen name="bottom_sheet_handle_area_height">36dp</dimen>
    <dimen name="bottom_sheet_handle_width">32dp</dimen>
    <dimen name="bottom_sheet_handle_height">4dp</dimen>
    <dimen name="bottom_sheet_handle_margin">16dp</dimen>
    <dimen name="widgets_bottom_sheet_handle_margin">24dp</dimen>
    <dimen name="bottom_sheet_handle_corner_radius">2dp</dimen>

    <!-- State transition -->
    <item name="workspace_content_scale" format="float" type="dimen">0.97</item>

    <!--  Folder spaces  -->
    <dimen name="folder_top_padding_default">24dp</dimen>
    <dimen name="folder_footer_horiz_padding">20dp</dimen>

    <!-- Default Ime height. Used only for logging purposes.
    Assume this is default keyboard height in EN locale in case the keyboard height is not known when queried.-->
    <dimen name="default_ime_height">300dp</dimen>

    <!-- Private Space parameters -->
    <dimen name="ps_container_corner_radius">28dp</dimen>
    <dimen name="ps_header_height">72dp</dimen>
    <dimen name="ps_header_relative_layout_height">48dp</dimen>
    <dimen name="ps_header_image_height">48dp</dimen>
    <dimen name="ps_header_text_height">24dp</dimen>
    <dimen name="ps_header_layout_margin">16dp</dimen>
    <dimen name="ps_lock_button_margin_end">12dp</dimen>
    <dimen name="ps_header_text_size">16sp</dimen>
    <dimen name="ps_button_height">40dp</dimen>
    <dimen name="ps_button_width">40dp</dimen>
    <dimen name="ps_lock_button_width">89dp</dimen>
    <dimen name="ps_app_divider_horizontal_padding">16dp</dimen>
    <dimen name="ps_app_divider_vertical_padding">32dp</dimen>
    <dimen name="ps_extra_bottom_padding">16dp</dimen>
    <dimen name="ps_lock_corner_radius">20dp</dimen>
    <dimen name="ps_lock_icon_size">20dp</dimen>
    <dimen name="ps_lock_icon_margin_top">10dp</dimen>
    <dimen name="ps_lock_icon_margin_bottom">10dp</dimen>
    <dimen name="ps_lock_icon_text_margin_start_expanded">8dp</dimen>
    <dimen name="ps_lock_icon_text_margin_end_expanded">6dp</dimen>
    <dimen name="ps_lock_button_background_padding">10dp</dimen>
    <dimen name="ps_floating_mask_corner_radius">28dp</dimen>
    <dimen name="ps_floating_mask_end_padding">16dp</dimen>

    <!-- WindowManagerProxy -->
    <dimen name="max_width_and_height_of_small_display_cutout">136px</dimen>
</resources>
