<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/accent_ripple_color">
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/rounded_button_radius" />
            <solid android:color="@android:color/white" />
        </shape>
    </item>
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/rounded_button_radius" />
            <stroke
                android:width="@dimen/work_apps_paused_button_stroke"
                android:color="@color/work_turn_on_stroke" />
        </shape>
    </item>
</ripple>