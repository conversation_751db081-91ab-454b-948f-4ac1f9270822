<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!--
A variation of material's scrollbar_handle_material.xml that has paddings to make it smaller.
ScrollView's "insideInsets" / "insideOverlay" styles don't consider corner radius applied to scroll
views, so we apply matching padding to the thumb to align it.
 -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:bottom="@dimen/widget_list_top_bottom_corner_radius"
        android:top="@dimen/widget_list_top_bottom_corner_radius">
        <shape
            android:shape="rectangle"
            android:tint="?android:attr/colorControlNormal">
            <solid android:color="#84ffffff" />
        </shape>
    </item>
</layer-list>