<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <vector
            android:viewportWidth="28"
            android:viewportHeight="28"
            android:width="@dimen/ps_floating_mask_corner_radius"
            android:height="@dimen/ps_floating_mask_corner_radius">
            <path
                android:pathData="M28 28V0C28 3.67725 27.2759 7.31787 25.8687 10.7153C24.4614 14.1123 22.3989 17.1987 19.7988 19.7988C17.1987 22.3989 14.1123 24.4614 10.7153 25.8687C7.31787 27.2759 3.67725 28 0 28H28Z"
                android:fillType="evenOdd"
                android:fillColor="?attr/allAppsScrimColor" />
        </vector>
    </item>
</layer-list>