<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (C) 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">
<!--redmine 300822 modify for Press and hold the desktop to enter the home screen. The horizontal font is not fully displayed zhaoxian start-->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/colorPrimary"
        android:outlineAmbientShadowColor="@android:color/transparent"
        android:outlineSpotShadowColor="@android:color/transparent"
        android:theme="@style/HomeSettings.CollapsingToolbar">
<!--redmine 300822 modify for Press and hold the desktop to enter the home screen. The horizontal font is not fully displayed zhaoxian end-->

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="226dp"
            android:clipToPadding="false"
            app:collapsedTitleTextAppearance="@style/HomeSettings.CollapsedToolbarTitle"
            app:contentScrim="@color/home_settings_header_collapsed"
            app:expandedTitleMarginEnd="24dp"
            app:expandedTitleMarginStart="24dp"
            app:expandedTitleTextAppearance="@style/HomeSettings.ExpandedToolbarTitle"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap"
            app:maxLines="3"
            app:scrimAnimationDuration="50"
            app:scrimVisibleHeightTrigger="174dp"
            app:statusBarScrim="@null"
            app:titleCollapseMode="fade"
            app:toolbarId="@id/action_bar">

            <Toolbar
                android:id="@+id/action_bar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:theme="?android:attr/actionBarTheme"
                android:transitionName="shared_element_view"
                app:layout_collapseMode="pin" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
        android:id="@+id/content_frame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>