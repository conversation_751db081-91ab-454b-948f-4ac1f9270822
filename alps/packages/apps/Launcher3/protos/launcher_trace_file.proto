/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto2";

import "launcher_trace.proto";

package com.android.launcher3.tracing;

option java_multiple_files = true;

/* represents a file full of launcher trace entries.
   Encoded, it should start with 0x9 0x4C 0x4E 0x43 0x48 0x52 0x54 0x52 0x43 (.LNCHRTRC), such
   that they can be easily identified. */
message LauncherTraceFileProto {

    /* constant; MAGIC_NUMBER = (long) MAGIC_NUMBER_H << 32 | MagicNumber.MAGIC_NUMBER_L
       (this is needed because enums have to be 32 bits and there's no nice way to put 64bit
        constants into .proto files. */
    enum MagicNumber {
        INVALID = 0;
        MAGIC_NUMBER_L = 0x48434E4C;  /* LNCH (little-endian ASCII) */
        MAGIC_NUMBER_H = 0x43525452;  /* RTRC (little-endian ASCII) */
    }

    optional fixed64 magic_number = 1;  /* Must be the first field, set to value in MagicNumber */
    repeated LauncherTraceEntryProto entry = 2;
}

/* one launcher trace entry. */
message LauncherTraceEntryProto {
    /* required: elapsed realtime in nanos since boot of when this entry was logged */
    optional fixed64 elapsed_realtime_nanos = 1;

    optional LauncherTraceProto launcher = 3;
}
