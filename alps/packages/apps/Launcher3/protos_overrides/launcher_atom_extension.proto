/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto2";

option java_package = "com.android.launcher3.logger";
option java_outer_classname = "LauncherAtomExtensions";


// This proto file contains placeholder messages that can be overridden by
// other Launcher variants.
// Variants could have its own launcher_atom_extension.proto file(should have
// same messages declared here but with different implementation); when building
// variant's apk launcher_atom.proto will reference variant's extension file,
// essentially overriding this file.


// Wrapper message for additional containers used in variants.
message ExtendedContainers {}
