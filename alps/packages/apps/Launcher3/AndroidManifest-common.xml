<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2016, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.android.launcher3">

    <!--
    The manifest defines the common entries that should be present in any derivative of Launcher3.
    The components should generally not require any changes.

    Rest of the components are defined in AndroidManifest.xml which is merged with this manifest
    at compile time. Note that the components defined in AndroidManifest.xml are also required,
    with some minor changed based on the derivative app.
    -->

    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.READ_DEVICE_CONFIG" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.VIBRATE"/>
    <!-- for rotating surface by arbitrary degree -->
    <uses-permission android:name="android.permission.ROTATE_SURFACE_FLINGER" />
    <uses-permission android:name="android.permission.WAKEUP_SURFACE_FLINGER" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!--
    Permissions required for read/write access to the workspace data. These permission name
    should not conflict with that defined in other apps, as such an app should embed its package
    name in the permissions. eq com.mypackage.permission.READ_SETTINGS
    -->
    <permission
        android:name="${applicationId}.permission.READ_SETTINGS"
        android:protectionLevel="signatureOrSystem"
        android:label="@string/permlab_read_settings"
        android:description="@string/permdesc_read_settings"/>
    <permission
        android:name="${applicationId}.permission.WRITE_SETTINGS"
        android:protectionLevel="signatureOrSystem"
        android:label="@string/permlab_write_settings"
        android:description="@string/permdesc_write_settings"/>

    <uses-permission android:name="${applicationId}.permission.READ_SETTINGS" />
    <uses-permission android:name="${applicationId}.permission.WRITE_SETTINGS" />

    <application
        android:backupAgent="com.android.launcher3.LauncherBackupAgent"
        android:name="com.android.launcher3.LauncherApplication"
        android:fullBackupOnly="true"
        android:backupInForeground="true"
        android:fullBackupContent="@xml/backupscheme"
        android:hardwareAccelerated="true"
        android:icon="@drawable/ic_launcher_home"
        android:label="@string/derived_app_name"
        android:largeHeap="@bool/config_largeHeap"
        android:restoreAnyVersion="true"
        android:supportsRtl="true" >

        <!-- Intent received when a session is committed -->
        <receiver
            android:name="com.android.launcher3.SessionCommitReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.content.pm.action.SESSION_COMMITTED" />
            </intent-filter>
        </receiver>

        <!-- Intent received used to initialize a restored widget -->
        <receiver android:name="com.android.launcher3.AppWidgetsRestoredReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_HOST_RESTORED"/>
            </intent-filter>
        </receiver>

        <service
            android:name="com.android.launcher3.notification.NotificationListener"
            android:label="@string/notification_dots_service_title"
            android:exported="true"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <meta-data android:name="android.nfc.disable_beam_default"
                       android:value="true" />

        <activity android:name="com.android.launcher3.dragndrop.AddItemActivity"
            android:theme="@style/AddItemActivityTheme"
            android:excludeFromRecents="true"
            android:autoRemoveFromRecents="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.content.pm.action.CONFIRM_PIN_SHORTCUT" />
                <action android:name="android.content.pm.action.CONFIRM_PIN_APPWIDGET" />
            </intent-filter>
        </activity>

        <!--
        The settings provider contains Home's data, like the workspace favorites. The permissions
        should be changed to what is defined above. The authorities should also be changed to
        represent the package name.
        -->
        <provider
            android:name="com.android.launcher3.LauncherProvider"
            android:authorities="${applicationId}.settings"
            android:exported="true"
            android:writePermission="${applicationId}.permission.WRITE_SETTINGS"
            android:readPermission="${applicationId}.permission.READ_SETTINGS" />

        <!--
        The content provider for exposing various launcher grid options.
        TODO: Add proper permissions
        -->
        <provider
            android:name="com.android.launcher3.graphics.GridCustomizationsProvider"
            android:authorities="${applicationId}.grid_control"
            android:exported="true" />

        <!--
        The settings activity. To extend point settings_fragment_name to appropriate fragment class
        -->
        <activity
            android:name="com.android.launcher3.settings.SettingsActivity"
            android:label="@string/settings_button_text"
            android:theme="@style/HomeSettings.Theme"
            android:exported="true"
            android:autoRemoveFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.APPLICATION_PREFERENCES" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.android.launcher3.testing.TestInformationProvider"
            android:authorities="${applicationId}.TestInfo"
            android:readPermission="android.permission.WRITE_SECURE_SETTINGS"
            android:writePermission="android.permission.WRITE_SECURE_SETTINGS"
            android:exported="true"
            android:enabled="false" />

        <!--
        Launcher activity for secondary display
        -->
        <activity
            android:name="com.android.launcher3.secondarydisplay.SecondaryDisplayLauncher"
            android:theme="@style/AppTheme"
            android:launchMode="singleTop"
            android:exported="true"
            android:enabled="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.SECONDARY_HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Disable eager initialization of Jetpack libraries. See bug 197780098. -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="remove" />

        <property
            android:name="android.window.PROPERTY_ACTIVITY_EMBEDDING_SPLITS_ENABLED"
            android:value="true" />
    </application>
</manifest>
