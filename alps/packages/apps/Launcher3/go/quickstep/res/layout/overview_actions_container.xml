<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.quickstep.views.GoOverviewActionsView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal|bottom">

    <!--DAHLIA-2346:modify for display issue by shaoxuesong 2025.0102@start -->
    <!-- android:layout_height="@dimen/overview_actions_height" -->
    <LinearLayout
        android:id="@+id/action_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:orientation="horizontal">
    <!--DAHLIA-2346:modify for display issue by shaoxuesong 2025.0102@end -->

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/layout_translate"
            style="@style/GoOverviewActionButtonContainer">
            <ImageButton
                android:id="@+id/action_translate"
                style="@style/GoOverviewActionButton"
                android:src="@drawable/ic_translate"
                android:contentDescription="@string/action_translate" />
            <TextView
                style="@style/GoOverviewActionButtonCaption"
                android:text="@string/action_translate" />
        </LinearLayout>

        <Space
            android:id="@+id/spacer_translate"
            android:layout_width="@dimen/go_overview_button_container_margin"
            android:layout_height="1dp" />

        <LinearLayout
            android:id="@+id/layout_listen"
            style="@style/GoOverviewActionButtonContainer">
            <ImageButton
                android:id="@+id/action_listen"
                style="@style/GoOverviewActionButton"
                android:src="@drawable/ic_listen"
                android:contentDescription="@string/action_listen"
                android:background="@drawable/round_rect_button" />
            <TextView
                style="@style/GoOverviewActionButtonCaption"
                android:text="@string/action_listen" />
        </LinearLayout>

        <Space
            android:id="@+id/spacer_listen"
            android:layout_width="@dimen/go_overview_button_container_margin"
            android:layout_height="1dp" />

        <LinearLayout
            android:id="@+id/layout_screenshot"
            style="@style/GoOverviewActionButtonContainer">
            <ImageButton
                android:id="@+id/action_screenshot"
                style="@style/GoOverviewActionButton"
                android:src="@drawable/ic_screenshot"
                android:contentDescription="@string/action_screenshot"
                android:background="@drawable/round_rect_button" />
            <!--DAHLIA-2346:modify for display issue by shaoxuesong 2025.0102@start -->
            <TextView
                style="@style/GoOverviewActionButtonCaption"
                android:maxLines="2"
                android:ellipsize="end"
                android:text="@string/action_screenshot" />
            <!--DAHLIA-2346:modify for display issue by shaoxuesong 2025.0102@end -->
        </LinearLayout>

        <!--modify for DAHLIA-2745 replace clear all button by tanghao 2025.01.24 begin-->
        <Space
            android:id="@+id/spacer_listen"
            android:layout_width="@dimen/go_overview_button_container_margin"
            android:layout_height="1dp" />

        <LinearLayout
            android:id="@+id/layout_clearall"
            style="@style/GoOverviewActionButtonContainer">
            <ImageButton
                android:id="@+id/clear_all_button"
                style="@style/GoOverviewActionButton"
                android:src="@drawable/ic_clearall_button"
                android:background="@drawable/round_rect_button" />
            <TextView
                style="@style/GoOverviewActionButtonCaption"
                android:maxLines="2"
                android:ellipsize="end"
                android:text="@string/recents_clear_all" />
        </LinearLayout>
        <!--modify for DAHLIA-2745 replace clear all button by tanghao 2025.01.24 end-->
        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- Will be enabled in a future version. -->
        <LinearLayout
            style="@style/GoOverviewActionButtonContainer"
            android:visibility="gone" >
            <ImageButton
                android:id="@+id/action_search"
                style="@style/GoOverviewActionButton"
                android:src="@drawable/ic_search"
                android:contentDescription="@string/action_search"
                android:background="@drawable/round_rect_button" />
            <TextView
                style="@style/GoOverviewActionButtonCaption"
                android:text="@string/action_search" />
        </LinearLayout>

        <!-- Unused. Included only for compatibility with parent class. -->
        <Button
            android:id="@+id/action_split"
            style="@style/GoOverviewActionButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_split_vertical"
            android:text="@string/action_split"
            android:theme="@style/ThemeControlHighlightWorkspaceColor"
            android:visibility="gone" />

        <Space
            android:id="@+id/action_split_space"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Unused. Included only for compatibility with parent class. -->
    <Button
        android:id="@+id/action_save_app_pair"
        style="@style/GoOverviewActionButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:drawableStart="@drawable/ic_save_app_pair_up_down"
        android:text="@string/action_save_app_pair"
        android:theme="@style/ThemeControlHighlightWorkspaceColor"
        android:visibility="gone" />

</com.android.quickstep.views.GoOverviewActionsView>