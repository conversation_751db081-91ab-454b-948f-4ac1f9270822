<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <!-- App themes -->
    <style name="LauncherTheme" parent="@style/BaseLauncherTheme">
        <item name="overviewButtonTextColor">@color/go_overview_text_color</item>
        <item name="overviewButtonIconColor">@color/go_overview_text_color</item>
        <item name="overviewButtonBackgroundColor">@color/go_overview_button_color</item>
        <item name="modalDialogBackground">@color/go_modal_dialog_background</item>
    </style>

    <style name="LauncherTheme.Dark" parent="@style/LauncherTheme">
        <item name="overviewButtonTextColor">@color/go_overview_text_color_dark</item>
        <item name="overviewButtonIconColor">@color/go_overview_text_color_dark</item>
        <item name="overviewButtonBackgroundColor">@color/go_overview_button_color_dark</item>
        <item name="modalDialogBackground">@color/go_modal_dialog_background_dark</item>
    </style>

    <!-- Overview -->
    <style name="GoOverviewActionButton">
        <item name="android:tint">?attr/overviewButtonIconColor</item>
        <item name="android:backgroundTint">?attr/overviewButtonBackgroundColor</item>
        <item name="android:background">@drawable/round_rect_button</item>
        <item name="android:layout_width">@dimen/go_overview_button_width</item>
        <item name="android:layout_height">@dimen/go_overview_button_height</item>
        <item name="android:layout_gravity">center_horizontal</item>
    </style>

    <style name="GoOverviewActionButtonCaption">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">?attr/overviewButtonTextColor</item>
        <item name="android:lineHeight">20dp</item>
        <item name="android:textAlignment">center</item>
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/go_overview_button_caption_margin</item>
        <item name="android:layout_gravity">center_horizontal</item>
    </style>

    <style name="GoOverviewActionButtonContainer">
        <item name="android:layout_width">@dimen/go_overview_button_container_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
    </style>

    <!-- Modal Dialogs -->
    <style name="ModalDialogTitle">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:lineHeight">24dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_horizontal</item>
    </style>

    <style name="ModalDialogText">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
        <item name="android:lineHeight">24dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_horizontal</item>
    </style>

    <style name="ModalDialogButton" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/colorAccent</item>
        <item name="android:lineHeight">20dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
</resources>