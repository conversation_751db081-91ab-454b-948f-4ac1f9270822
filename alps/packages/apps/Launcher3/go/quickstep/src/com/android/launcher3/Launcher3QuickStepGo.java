/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.launcher3;

import com.android.launcher3.popup.SystemShortcut;
import com.android.launcher3.uioverrides.QuickstepLauncher;

import java.util.stream.Stream;

/**
 * The Launcher variant used for Android Go Edition
 */
public class Launcher3QuickStepGo extends QuickstepLauncher {
    private static final String TAG = "Launcher3QuickStepGo";

    @Override
    public Stream<SystemShortcut.Factory> getSupportedShortcuts() {
        Stream<SystemShortcut.Factory> shortcuts = super.getSupportedShortcuts();

        if (AppSharing.ENABLE_APP_SHARING) {
            shortcuts = Stream.concat(shortcuts, Stream.of(AppSharing.SHORTCUT_FACTORY));
        }

        return shortcuts;
    }
}
