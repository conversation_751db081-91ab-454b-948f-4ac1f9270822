<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2017, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.android.launcher3" >

    <uses-sdk android:targetSdkVersion="29" android:minSdkVersion="25"/>

    <uses-permission android:name="android.permission.GET_TOP_ACTIVITY_INFO" />

    <application
        android:backupAgent="com.android.launcher3.LauncherBackupAgent"
        android:fullBackupOnly="true"
        android:fullBackupContent="@xml/backupscheme"
        android:hardwareAccelerated="true"
        android:icon="@drawable/ic_launcher_home"
        android:label="@string/derived_app_name"
        android:theme="@style/AppTheme"
        android:largeHeap="@bool/config_largeHeap"
        android:restoreAnyVersion="true"
        android:supportsRtl="true" >

        <!-- Activity for handling PinItemRequest is disabled on Android Go. -->
        <activity android:name="com.android.launcher3.dragndrop.AddItemActivity"
            android:theme="@android:style/Theme.DeviceDefault.Light.Dialog.Alert"
            android:excludeFromRecents="true"
            android:autoRemoveFromRecents="true"
            android:label="@string/action_add_to_workspace"
            android:enabled="false"
            tools:node="replace" >
        </activity>

        <service
            android:name="com.android.launcher3.notification.NotificationListener"
            android:label="@string/notification_dots_service_title"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:enabled="false"
            tools:node="replace" />

        <service
            android:name="com.android.launcher3.model.AppShareabilityJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
    </application>

</manifest>
