/*
 * Copyright (C) 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.launcher3.widget.model;

import android.os.Process;

import com.android.launcher3.model.data.PackageItemInfo;

import java.util.Collections;

/**
 * Entry representing the top empty space
 */
public class WidgetListSpaceEntry extends WidgetsListBaseEntry {

    public WidgetListSpaceEntry() {
        super(new PackageItemInfo(/* packageName= */ "", Process.myUserHandle()),
                /* titleSectionName= */ "",
                Collections.EMPTY_LIST);
        mPkgItem.title = "";
    }
}
