<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.4.0-alpha01" type="baseline" client="" dependencies="true" name="" variant="all" version="8.4.0-alpha01">

    <issue
        id="NewApi"
        message="`@android:dimen/system_app_widget_background_radius` requires API level 31 (current min is 26)"
        errorLine1='    &lt;corners android:radius="@android:dimen/system_app_widget_background_radius" /&gt;'
        errorLine2="             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="packages/apps/Launcher3/res/drawable/widget_resize_frame.xml"
            line="20"
            column="14"/>
    </issue>

    <issue
        id="NewApi"
        message="Call requires API level 31 (current min is 26): `android.appwidget.AppWidgetHostView#resetColorResources`"
        errorLine1="            resetColorResources();"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="packages/apps/Launcher3/src/com/android/launcher3/widget/LauncherAppWidgetHostView.java"
            line="117"
            column="13"/>
    </issue>

    <issue
        id="NewApi"
        message="Call requires API level 31 (current min is 30): `android.appwidget.AppWidgetHostView#setColorResources`"
        errorLine1="            view.setColorResources(mWallpaperColorResources);"
        errorLine2="                 ~~~~~~~~~~~~~~~~~">
        <location
            file="packages/apps/Launcher3/src/com/android/launcher3/graphics/LauncherPreviewRenderer.java"
            line="433"
            column="18"/>
    </issue>

</issues>