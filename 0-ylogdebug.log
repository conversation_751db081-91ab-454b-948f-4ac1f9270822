ylogdebug_0  on 01-01 08:19:16


uptime on Thu Jan  1 08:19:16 CST 1970
 08:19:16 up 0 min,  0 users,  load average: 2.97, 0.63, 0.20
run finished on 01-01 08:19:17


logcat -S on Thu Jan  1 08:19:17 CST 1970
size/num main               system             crash              kernel             Total
Total    46000/350          10748/95           0/0                643024/6088        699772/6533
Now      46000/350          10748/95           0/0                643024/6088        699772/6533
Logspan  10.049             9.958                                 10.07              10.07
Overhead 65616              65616                                 201932             337695

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                                22899
  PID/UID   COMMAND LINE                                       "
  504/1000  /apex/com.android.sdkext/bin/derive_classpath   10086
  385/1000 ...<EMAIL> 9049
  295/1000  /system/system_ext/bin/hwservicemanager          2931
  288/1000  /system/bin/servicemanager                        311
  591/1000 ...m_ext/bin/hw/android.hidl.allocator@1.0-service 261
0     root                                                  20476
9999  nobody                                                 1465
1036  auditd                                                  376
1017  keystore                                                360
1069  lmkd                                                    273


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1017  keystore                                               5469
0     root                                                   4264
1000  system                                                 1015
  PID/UID   COMMAND LINE                                       "
  516/1000  /product/bin/ylog                                 895
  536/1000  /product/bin/ylogkat                              120

run finished on 01-01 08:19:17


ylogctl q on Thu Jan  1 08:19:17 CST 1970
status = enable 
file = /data/ylog/ap/000-0101_081917_poweron.ylog 
size = 393593 
pid =  516  
[   530]  lastlog      -> Open    -> lastlog.log      [     37]->[      4] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     2C] 
[   536]  android      -> Open    -> android.log      [      4]->[      0] 
[   539]  kernel       -> Open    -> kernel.log       [     63]->[      2] 
[   540]  trace        -> Open    -> trace.log        [      0]->[      0] 
[   541]  sgm          -> Open    -> sgm.csv          [      1]->[      0] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [      C]->[      0] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   551]  ylogdebug    -> Open    -> ylogdebug.log    [      C]->[      0] 
[   556]  phoneinfo    -> Open    -> phoneinfo.log    [     10]->[      0] 
[   561]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   567]  trustlog     -> Open    -> trusty.log       [      3]->[      0] 

run finished on 01-01 08:19:17


ylogctl space on Thu Jan  1 08:19:17 CST 1970
Root:/data/ylog/ap/   APLogSize:0 APLogMaxSize:46570 DiskFreeSpace:46277  DiskReserved:60
run finished on 01-01 08:19:18


cat /data/ylog/ylog.conf on Thu Jan  1 08:19:18 CST 1970
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 01-01 08:19:18


ls -l /data/ylog/ap/ on Thu Jan  1 08:19:18 CST 1970
total 536
-rw-rw-rw- 1 <USER> <GROUP> 524665 1970-01-01 08:19 000-0101_081917_poweron.ylog
-rw-rw-rw- 1 <USER> <GROUP>  19006 1970-01-01 08:19 analyzer.py
run finished on 01-01 08:19:18


cat /data/ylog/journal.log on Thu Jan  1 08:19:18 CST 1970
[01-01 08:19:16.559] (     1) [    14]     516     516  

.............................
[01-01 08:19:16.562] (     2) [    14]     516     516  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[01-01 08:19:16.566] (     3) [    14]     516     516  /data/ylog/ylog.conf error,reinit it
[01-01 08:19:16.567] (     4) [    14]     516     516  ylog config is /product/etc/ylog.conf.debug
[01-01 08:19:16.576] (     5) [    14]     516     516  syncLegcyConfig
[01-01 08:19:16.579] (     6) [    14]     516     516  startlogServcie LogEnable : 1
[01-01 08:19:16.579] (     7) [    14]     516     516  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[01-01 08:19:16.583] (     8) [    14]     516     516  setSubLog onOff:0 logType:lastlog
[01-01 08:19:16.584] (     9) [    14]     516     516  setSubLog onOff:0 logType:uboot
[01-01 08:19:16.584] (    10) [    14]     516     516  setSubLog onOff:0 logType:android
[01-01 08:19:16.584] (    11) [    14]     516     516  setSubLog onOff:0 logType:kernel
[01-01 08:19:16.584] (    12) [    14]     516     516  setSubLog onOff:0 logType:trace
[01-01 08:19:16.584] (    13) [    14]     516     516  setSubLog onOff:0 logType:sgm
[01-01 08:19:16.584] (    14) [    14]     516     516  setSubLog onOff:0 logType:sysinfo
[01-01 08:19:16.584] (    15) [    14]     516     516  setSubLog onOff:0 logType:thermal
[01-01 08:19:16.584] (    16) [    14]     516     516  setSubLog onOff:0 logType:ylogdebug
[01-01 08:19:16.584] (    17) [    14]     516     516  setSubLog onOff:0 logType:phoneinfo
[01-01 08:19:16.584] (    18) [    14]     516     516  setSubLog onOff:1 logType:hcidump
[01-01 08:19:16.584] (    19) [    14]     516     516  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[01-01 08:19:16.584] (    20) [    14]     516     516  setSubLog onOff:1 logType:tcpdump
[01-01 08:19:16.584] (    21) [    14]     516     516  setSubLog onOff:0 logType:trustlog
[01-01 08:19:16.587] (    22) [    14]     516     516  syncLegcyConfig
[01-01 08:19:16.588] (    23) [    14]     516     516  SubLog OnOff:0, logType lastlog is 1
[01-01 08:19:16.588] (    24) [    14]     516     516  SubLog OnOff:0, logType uboot is 1
[01-01 08:19:16.588] (    25) [    14]     516     516  SubLog OnOff:0, logType android is 1
[01-01 08:19:16.588] (    26) [    14]     516     516  SubLog OnOff:0, logType kernel is 1
[01-01 08:19:16.588] (    27) [    14]     516     516  SubLog OnOff:0, logType trace is 1
[01-01 08:19:16.588] (    28) [    14]     516     516  SubLog OnOff:0, logType sgm is 1
[01-01 08:19:16.588] (    29) [    14]     516     516  SubLog OnOff:0, logType sysinfo is 1
[01-01 08:19:16.588] (    30) [    14]     516     516  SubLog OnOff:0, logType thermal is 0
[01-01 08:19:16.588] (    31) [    14]     516     516  SubLog OnOff:0, logType ylogdebug is 1
[01-01 08:19:16.588] (    32) [    14]     516     516  SubLog OnOff:0, logType phoneinfo is 1
[01-01 08:19:16.588] (    33) [    14]     516     516  SubLog OnOff:1, logType hcidump is 1
[01-01 08:19:16.588] (    34) [    14]     516     516  SubLog OnOff:1, logType tcpdump is 1
[01-01 08:19:16.588] (    35) [    14]     516     516  SubLog OnOff:0, logType trustlog is 1
[01-01 08:19:16.589] (    36) [    14]     516     516  index:11,logType:tcpdump, logSize:256, totalSize:4096
[01-01 08:19:16.589] (    37) [    14]     516     516  index:10,logType:hcidump, logSize:64, totalSize:1024
[01-01 08:19:16.589] (    38) [    14]     516     516  value:default
[01-01 08:19:16.589] (    39) [    14]     516     516  make dir:/data/ylog/ap/
[01-01 08:19:16.590] (    40) [    14]     516     516  mSubLog:1
[01-01 08:19:16.590] (    41) [    14]     516     516  mSubLog:1
[01-01 08:19:16.590] (    42) [    14]     516     516  logSourceCnt:13 compressLevel:3
[01-01 08:19:16.591] (    43) [    14]     516     528  [lastlog]  configure is [1]
[01-01 08:19:16.596] (    44) [    14]     516     528  [uboot]  configure is [1]
[01-01 08:19:16.600] (    45) [    14]     516     528  [android]  configure is [1]
[01-01 08:19:16.603] (    46) [    14]     516     528  [kernel]  configure is [1]
[01-01 08:19:16.609] (    47) [    14]     516     528  [trace]  configure is [1]
[01-01 08:19:16.614] (    48) [    14]     516     528  [sgm]  configure is [1]
[01-01 08:19:16.624] (     1) [    14]     524     524  aplogfilesize : 256
[01-01 08:19:16.629] (     2) [    14]     524     542  LogReboot:startrebootServcie
[01-01 08:19:16.633] (    49) [    14]     516     528  [sysinfo]  configure is [1]
[01-01 08:19:16.635] (     3) [    14]     524     524  srootdir : default/
[01-01 08:19:16.636] (     4) [    14]     524     524  aplogmaxsize : 99%
[01-01 08:19:16.636] (     5) [    14]     524     524  aplogrotate : 1
[01-01 08:19:16.636] (     6) [    14]     524     524  prioritypath : 0
[01-01 08:19:16.646] (     7) [    14]     524     542  mkdir /data/log/reliability/dumplog/ success
[01-01 08:19:16.659] (    50) [    14]     516     528  [thermal]  configure is [0]
[01-01 08:19:16.659] (    51) [    14]     516     528  [ylogdebug]  configure is [1]
[01-01 08:19:16.676] (    52) [    14]     516     528  [phoneinfo]  configure is [1]
[01-01 08:19:16.711] (    53) [    14]     516     528  [hcidump]  configure is [1]
[01-01 08:19:16.729] (    54) [    14]     516     528  [tcpdump]  configure is [1]
[01-01 08:19:16.750] (    55) [    14]     516     528  [trustlog]  configure is [1]
[01-01 08:19:16.757] (    56) [    14]     516     528  listen to 12 source 
[01-01 08:19:16.856] (    57) [    14]     516     528  index:11 log buffer is null 
[01-01 08:19:16.857] (    58) [    14]     516     528  index:12 log buffer is null 
[01-01 08:19:17.323] (     8) [    14]     524     524  mount changed: [] -> [/data/]
[01-01 08:19:17.332] (     9) [    14]     524     524  set logdir to /data/ylog/ap/, diskfree:46278
[01-01 08:19:17.338] (    10) [    14]     524     624  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[01-01 08:19:17.338] (    11) [    14]     524     624  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
[01-01 08:19:17.346] (    12) [    14]     524     524  last ylog file  [] not exsit,backup mmap file
[01-01 08:19:17.346] (    13) [    14]     524     524  last ylog not exit, backupMmapData error.
[01-01 08:19:17.351] (    14) [    14]     524     524  create first file
[01-01 08:19:17.367] (    15) [    14]     524     524  get new  file name(new logfile) : /data/ylog/ap/000-0101_081917_poweron.ylog 
[01-01 08:19:17.368] (    16) [    14]     524     524  open log file:/data/ylog/ap/000-0101_081917_poweron.ylog fd:24 diskfree:46278
[01-01 08:19:17.371] (    17) [    15]     524     524  update UID file:/data/ylog/loguid=0
[01-01 08:19:17.372] (    59) [    15]     516     521  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
run finished on 01-01 08:19:19
ylogdebug end




ylogdebug_1  on 01-01 11:03:31


uptime on Wed Jan  1 11:03:31 CST 2025
 11:03:31 up 5 min,  0 users,  load average: 31.93, 22.75, 10.09
run finished on 01-01 11:03:31


logcat -S on Wed Jan  1 11:03:31 CST 2025
size/num main               system             crash              kernel             Total
Total    11598470/81559     7379983/38486      0/0                2151539/20111      21129992/140156
Now      749462/5414        1168213/5531       0/0                973036/9433        2890711/20378
Logspan                                                                              
Overhead 247703             253487                                261147             779951

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               192275
  PID/UID   COMMAND LINE                                       "
 1200/1000  system_server                                  119546
  714/1000  /system/bin/surfaceflinger                      40715
  614/1000 ...ndroid.hardware.graphics.composer@2.4-service 14280
  383/1000  /system/bin/hw/android.system.suspend-service    4254
  611/1000 ...ndroid.hardware.graphics.allocator@4.0-service 4215
 9870/1000  com.android.DeviceAsWebcam                       2967
  688/1000 ...r/bin/hw/vendor.sprd.hardware.vibrator-service 2713
10134 com.google.android.dialer                             86090
10145 com.android.vending                                   71404
10140 com.google.android.gms                                63199
1041  audioserver                                           61941
0     root                                                  61121
10143 com.google.android.apps.messaging                     40455
10191 com.android.systemui                                  35802
10175 com.google.android.apps.tachyon                       22890
10146 com.google.android.googlequicksearchbox               21846
1013  media                                                 12160
1027  nfc                                                    8657
10165 com.google.android.gm                                  8065
10188 com.android.launcher3                                  7941
10206 com.google.android.rkpdapp                             7490
10160 com.google.android.contacts                            7436
10070 com.android.calllogbackup                              7240
10129 com.google.android.as                                  6894
10178 com.google.android.apps.photos                         3955
10154 com.android.chrome                                     3953
10168 com.google.android.inputmethod.latin                   3464
1046  mediacodec                                             3166
10077 com.android.providers.media                            2973
10142 com.google.android.apps.turbo                          2560
10123 com.google.android.configupdater                       2323


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1125446
10191 com.android.systemui                                  24807
1017  keystore                                              12677

run finished on 01-01 11:03:31


ylogctl q on Wed Jan  1 11:03:31 CST 2025
status = enable 
file = /data/ylog/ap/000-0101_081917_poweron.ylog 
size = 7020069 
pid =  516  
[   530]  lastlog      -> Open    -> lastlog.log      [     37]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1530]->[    5D0] 
[   539]  kernel       -> Open    -> kernel.log       [    1F5]->[    122] 
[   540]  trace        -> Open    -> trace.log        [      0]->[      0] 
[   541]  sgm          -> Open    -> sgm.csv          [   11DA]->[    143] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [    374]->[     F0] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   551]  ylogdebug    -> Open    -> ylogdebug.log    [     32]->[     14] 
[   556]  phoneinfo    -> Open    -> phoneinfo.log    [     DD]->[      E] 
[   561]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   567]  trustlog     -> Open    -> trusty.log       [     8D]->[     34] 

run finished on 01-01 11:03:32


ylogctl space on Wed Jan  1 11:03:32 CST 2025
Root:/data/ylog/ap/   APLogSize:6 APLogMaxSize:46570 DiskFreeSpace:40788  DiskReserved:60
run finished on 01-01 11:03:32


cat /data/ylog/ylog.conf on Wed Jan  1 11:03:32 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 01-01 11:03:32


ls -l /data/ylog/ap/ on Wed Jan  1 11:03:32 CST 2025
total 6942
-rw-rw-rw- 1 <USER>   <GROUP> 7076992 2025-01-01 11:03 000-0101_081917_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>   19006 1970-01-01 08:19 analyzer.py
drwxrwxrwx 2 <USER> <GROUP>    3452 2025-01-01 11:00 hcidump
drwxrwxrwx 2 <USER> <GROUP>    3452 1970-01-01 08:19 tcpdump
run finished on 01-01 11:03:32


tail -n +77</data/ylog/journal.log on Wed Jan  1 11:03:33 CST 2025
[01-01 08:19:17.371] (    17) [    15]     524     524  update UID file:/data/ylog/loguid=0
[01-01 08:19:17.372] (    59) [    15]     516     521  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[12-31 21:19:19.902] (    60) [    17]     516     527  make dir /data/ylog/ap/tcpdump/
[12-31 21:19:19.922] (    61) [    17]     516     527  open new log:/data/ylog/ap/tcpdump/001_1231_211919_tcpdump.cap, wfd:34, logname:/data/ylog/ap/tcpdump/001_1231_211919_tcpdump.cap
[12-31 21:19:19.922] (    62) [    17]     516     527  logType->totalwriten:0 sublogsize:0
[12-31 21:19:21.648] (    18) [    19]     524     542  currentTime: 19691231211921-28017037
[12-31 21:19:21.650] (    19) [    19]     524     542  SystemBootMode::LINUXKERNEL
[12-31 21:19:21.665] (    20) [    19]     524     542  boot_cause: Pbint triggered
[12-31 21:19:21.665] (    21) [    19]     524     542  boot_reason: normalboot
[12-31 21:19:21.665] (    22) [    19]     524     542  boot_category: normalboot
[12-31 21:19:21.669] (    23) [    19]     524     542  Crash_reason: Normal
[12-31 21:19:21.677] (    24) [    19]     524     915  open /dev/block/by-name/sd_klog failed No such file or directory
[01-01 00:00:05.858] (    63) [   111]     516     527  make dir /data/ylog/ap/hcidump/
[01-01 00:00:05.884] (    64) [   111]     516     527  open new log:/data/ylog/ap/hcidump/001_0101_000005_hcidump.cap, wfd:35, logname:/data/ylog/ap/hcidump/001_0101_000005_hcidump.cap
[01-01 00:00:05.885] (    65) [   111]     516     527  logType->totalwriten:0 sublogsize:0
run finished on 01-01 11:03:33
ylogdebug end




